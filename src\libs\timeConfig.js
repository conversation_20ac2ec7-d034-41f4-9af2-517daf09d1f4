import { dataDictionary } from "@/api";

class TimeConfig {
  zlsdb() {
    return this._getCurrentTime("FIND_LAWYER_BOTTOM");
  }

  /** im中显示留资时间 */
  imlz() {
    return this._getCurrentTime("IM_SHOW_RETENTION");
  }

  sydb() {
    return this._getCurrentTime("HOME_BOTTOM");
  }

  /** 问律师中清除缓存 */
  wlsClearCache() {
    return this._getCurrentTime("FIND_LAWYER_CLEAR_CACHE");
  }

  autoConfirmCity() {
    return this._getCurrentTime("CONFIRM_CITY");
  }

  /**
   * 问律师支付卡片出现后停留xx秒出现权益升级弹窗
   * 单位：s
   */
  wlsEquityUpgrade() {
    return this._getCurrentTime("PAY_CARD_SHOW_UPGRADE");
  }

  /**
   * 问律师支付卡片出现后停留xx秒出现降价弹窗
   * 单位：s
   */
  wlsDiscount() {
    return this._getCurrentTime("PAY_CARD_SHOW_DISCOUNT");
  }

  /**
   * 工具延迟弹窗时间
   * 单位：ms
   */
  toolDelay() {
    return this._getCurrentTime("TOOL_DELAY");
  }

  _getCurrentTime(value) {
    return this._getSystemTime().then(data => {
      return (
        Number(data.find(item => item.value === value)?.supplementDesc?.time) ||
        0
      );
    });
  }

  /** 请求系统参数 */
  _getSystemTime() {
    return dataDictionary({
      groupCode: "C_TIME_CONFIG"
    }).then(res => {
      return res.data.map(item => {
        try {
          return {
            ...item,
            supplementDesc: JSON.parse(item.supplementDesc)
          };
        } catch (e) {
          return item;
        }
      });
    });
  }
}

const timeConfig = new TimeConfig();

export default timeConfig;
