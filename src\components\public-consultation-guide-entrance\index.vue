<template>
  <div :style="{ minHeight: minHeight }">
    <div v-if="!$basicsTools.isNull(data) && !$basicsTools.isObjNull(data)">
      <slot :data="data" />
    </div>
  </div>
</template>

<script>
import { serviceManegeInfoCommon } from "@/api";

export default {
  name: "PublicConsultationGuideEntrance",
  props: {
    scene: {
      type: String,
      default: "",
      required: true,
    },
    minHeight: {
      type: String,
      default: "0px",
    },
  },
  data() {
    return {
      data: {},
    };
  },
  watch: {
    scene: {
      immediate: true,
      handler() {
        this.getServiceManegeInfoCommon();
      },
    },
  },
  methods: {
    /* 获取公共咨询引导入口*/
    getServiceManegeInfoCommon() {
      serviceManegeInfoCommon(this.scene).then(({ data }) => {
        this.data = data;
        this.$emit("getServiceManegeInfoCommon", data);
      });
    },
  },
};
</script>

<style scoped></style>
