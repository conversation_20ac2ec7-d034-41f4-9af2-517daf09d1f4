<template>
  <login-layout>
    <div class="askdetail">
      <detail-header />
      <div class="ques-desc">
        <!--      <span class="label">{{ detail.typeLabel }}</span>-->
        {{ detail.problemDesc }}
        <p class="tags-wr">
          <span class="text-tag">#{{ detail.typeLabel }}</span>
        </p>
      </div>
      <div class="background-container">
        <div class="askdetail-container">
          <div
            class="lawyer-info position-relative flex flex-space-between flex-align-center"
          >
            <img
              alt=""
              class="background-image"
              src="@/pages/askdetail/imgs/Frame3291@2x(1).png"
            >
            <navigator-link
              :url="lawyerHome"
              class="flex-1"
              rel="nofollow"
            >
              <div class="flex flex-align-center">
                <img
                  :src="detail.lawyerAvatar"
                  alt=""
                  class="avatar"
                >
                <div>
                  <p class="lawyer-name">
                    {{ detail.lawyerName }} 律师
                  </p>
                  <p class="lawyer-institution">
                    {{ detail.lawyerOffice }}
                  </p>
                </div>
              </div>
            </navigator-link>
            <navigator-link
              :url="lawyerHome"
              rel="nofollow"
            >
              <div class="jump flex flex-align-center">
                <img
                  alt=""
                  class="little-card"
                  src="@/pages/askdetail/imgs/card2.png"
                >
                <div>律师主页</div>
                <!--              <img-->
                <!--                alt=""-->
                <!--                class="img-line"-->
                <!--                src="@/pages/askdetail/imgs/line.png"-->
                <!--              />-->
              </div>
            </navigator-link>
          </div>
          <div
            v-if="detail.consultSuggest"
            class="title"
          >
            <div class="text-img">
              律师解答
            </div>
          </div>
          <div
            class="relative"
            @click="handleBlur"
          >
            <div
              v-if="blur"
              class="text-[14px] mt-[8px] text-[#3887F5] flex items-center justify-center absolute absolute-center-xy"
            >
              <div class="shrink-0">
                点击解锁可查看全部内容
              </div>
              <img
                alt=""
                class="w-[16px] h-[16px] block"
                src="@/pages/rapid-consultation-confirm-order/featured-answers/img/Z6GTp1.png"
              >
            </div>
            <div
              :class="[
                {
                  'text-blur': blur
                }
              ]"
            >
              <p class="desc">
                {{ detail.consultSuggest }}
              </p>
              <div
                v-if="detail.referenceLaw"
                class="title"
              >
                <div class="text-img">
                  法律依据
                </div>
              </div>
              <p class="desc">
                {{ detail.referenceLaw }}
              </p>
            </div>
          </div>
          <p class="date">
            解答于 {{ detail.createTimeDesc }}
          </p>
        </div>
      </div>

      <div class="link-wrapper">
        <div class="title">
          更多<span class="label">{{ detail.typeLabel }}</span>法律指南
        </div>
        <div
          v-for="i in list"
          :key="i.id"
          class="item"
        >
          <legal-guide-item :data="i" />
        </div>
        <navigator-link
          :url="
            '/pages/lawyer-home/ask/index' + '?typeValue=' + detail.typeValue
          "
        >
          <p class="look-more flex flex-align-center flex-space-center">
            查看更多
            <img
              alt=""
              class="img-line"
              src="@/pages/askdetail/imgs/line.png"
            >
          </p>
        </navigator-link>
      </div>
      <div
        class="float-btn flex flex-align-center flex-space-center"
        @click="toSelfSupportLawyerOrder"
      >
        <img
          alt=""
          class="btn-img"
          src="@/pages/askdetail/imgs/<EMAIL>"
        >
      </div>
      <!--    <app-popup :show="showCenterPopup" mode="center" show-cancel-button @cancel="showCenterPopup=false" :popupStyle="{width:'654rpx'}">-->
      <!--      <div class="popup-wr">-->
      <!--        <img class="img-main" src="@/pages/ask/img/<EMAIL>" alt="">-->
      <!--        <img class="img-btn" src="@/pages/ask/img/<EMAIL>" alt="" @click="toAskLawyer()">-->
      <!--      </div>-->
      <!--    </app-popup>-->
      <!--    <app-popup :show="showBottomPopup" mode="bottom" show-cancel-button @cancel="showBottomPopup=false" bg-color="transparent" :popupStyle="{width:'375px'}">-->
      <!--      <div class="popup-wr-btm">-->
      <!--        <img class="img-main" src="@/pages/ask/img/<EMAIL>" alt="">-->
      <!--        <img class="img-btn" src="@/pages/ask/img/<EMAIL>" alt="" @click="toAskLawyer()">-->
      <!--      </div>-->
      <!--    </app-popup>-->
    </div>
  </login-layout>
</template>

<script>
import NavigatorLink from "@/components/navigator-link/index.vue";
import {
  lawGuideOpinionDetail,
  lawGuideServerPage,
  shareDataDictionary
} from "@/api/index.js";
import LegalGuideItem from "@/components/legal-guide-item/index.vue";
import { toAskLawyer, toSelfSupportLawyerOrder } from "@/libs/turnPages";
import DetailHeader from "@/components/detail-header/index.vue";
import { SHARE_DICTIONARY } from "@/libs/config";
import {
  setNavigationBarTitle,
  shareAppMessageNew,
  whetherToLogIn
} from "@/libs/tools";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import LoginLayout from "@/components/login/login-layout.vue";
import seo from "@/libs/seo";

export default {
  name: "AskDetail",
  components: {
    LoginLayout,
    DetailHeader,
    LegalGuideItem,
    NavigatorLink
  },
  data() {
    return {
      detail: {},
      list: [],
      showCenterPopup: false,
      showBottomPopup: false,
      blur: true
    };
  },
  async onShareAppMessage() {
    const { remark: data } =
      (await shareDataDictionary()).find(
        item => item.value === SHARE_DICTIONARY.ASKDETAIL
      ) || {};

    return shareAppMessageNew(data);
  },
  onLoad(options) {
    buryPointChannelBasics({
      code: "LAW_APPLET_LEGAL_GUIDELINES_PAGE",
      behavior: BURY_POINT_CHANNEL_TYPE.VI,
      type: 1
    });

    buryPointChannelBasics({
      code: "LAW_APPLET_HIGH_QUALITY_ANSWER_DETAIL_PAGE",
      behavior: BURY_POINT_CHANNEL_TYPE.VI,
      type: 1
    });

    setNavigationBarTitle({
      title: "法律指南",
      backgroundColor: "#EFF4FD"
    });
    lawGuideOpinionDetail({
      caseSourceServerId: options.caseSourceServerId
    }).then(({ data = {} }) => {
      this.detail = data;
      this.getList(data.typeValue);

      seo.setPageInfo({
        title: `${this.detail.problemDesc} - 法临网`,
        keywords: `${this.detail.typeLabel}`,
        description: `${this.detail.consultSuggest?.slice(0, 100)}`
      });
    });
  },
  onShow(options) {
    this.timer = setTimeout(() => {
      if (this.isShowPopup) return;
      this.showCenterPopup = true;
    }, 1000 * 20);
  },
  onHide() {
    clearTimeout(this.timer);
  },
  onReachBottom() {
    this.scrollToLower();
  },
  computed: {
    /* 律师主页*/
    lawyerHome() {
      return `/pages/lawyer-home/index?id=${this.detail.lawyerId}`;
    },
    consultationPage() {
      return `/pages/lawyer-home/pay-lawyer-guide/index?lawyerId=${this.detail.lawyerId}`;
    },
    isShowPopup() {
      return this.showCenterPopup || this.showBottomPopup;
    }
  },
  methods: {
    toSelfSupportLawyerOrder() {
      buryPointChannelBasics({
        code: "LAW_APPLET_HIGH_QUALITY_ANSWER_DETAIL_PAGE_WANNA_CONSULT_CLICK",
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
        type: 1
      });

      toSelfSupportLawyerOrder();
    },
    getList(typeValue = null) {
      lawGuideServerPage({
        homePage: 1,
        currentPage: 1,
        pageSize: 3,
        typeValue: typeValue === null ? "" : Number(typeValue),
        excludeId: this.detail.id
      }).then(({ data = {} }) => {
        const { records = [] } = data;
        this.list = records;
      });
    },
    scrollToLower() {
      if (!this.isShowPopup) this.showBottomPopup = true;
    },
    toAskLawyer() {
      this.showCenterPopup = false;
      toAskLawyer();
    },
    handleBlur() {
      buryPointChannelBasics({
        code: "LAW_APPLET_HIGH_QUALITY_ANSWER_DETAIL_PAGE_UNLOCK_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      whetherToLogIn(() => {
        this.blur = false;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.background-container {
  //background: linear-gradient(180deg, #eff4fd 0%, rgba(239, 244, 253, 0) 100%);
}

.askdetail {
  min-height: 100vh;
  padding-bottom: 82px;
  position: relative;

  .ques-desc {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    padding: 0 16px;
    background: #fff;

    .label {
      color: #3887f5;
      position: relative;

      &:after {
        content: "";
        display: inline-block;
        width: 1px;
        height: 14px;
        background: #ddd;
        margin: 0 10px;
      }
    }

    .tags-wr {
      padding: 12px 0 10px;

      .text-tag {
        font-size: 12px;
        font-weight: 400;
        color: #3887f5;
      }
    }
  }

  .lawyer-info {
    height: 62px;
    padding: 0 16px 0 12px;
    z-index: 1;

    .avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      margin-right: 12px;
    }

    .lawyer-name {
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }

    .lawyer-institution {
      padding-top: 3px;
      font-size: 13px;
      font-weight: 400;
      color: #999999;
    }

    .jump {
      font-size: 13px;
      font-weight: 400;
      color: #9f6310;

      .img-line {
        width: 12px;
        height: 12px;
        display: block;
      }

      .little-card {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        display: block;
      }
    }
  }

  .askdetail-container {
    background: #ffffff;
    border-radius: 0 0 16px 16px;
    padding: 0 16px;

    .date {
      font-size: 12px;
      font-weight: 400;
      color: #999999;
      padding: 16px 0 24px;
    }

    .title {
      .text-img {
        font-size: 16px;
        font-weight: bold;
        color: #333333;
        line-height: 19px;
      }

      padding: 16px 0;
      font-size: 0;
    }

    .desc {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      word-break: break-all;
    }
  }

  .btn {
    position: fixed;
    bottom: calc(8px + constant(safe-area-inset-bottom, 0));
    bottom: calc(8px + env(safe-area-inset-bottom, 0));
    left: 50%;
    transform: translateX(-50%);
    font-size: 15px;
    font-weight: 500;
    color: #ffffff;
    background: linear-gradient(116deg, #71b5ff 0%, #2676e4 100%);
    border-radius: 68px 68px 68px 68px;
    box-shadow: 0px 4px 12px 0px rgba(56, 135, 245, 0.2);

    .btn-text {
      color: #ffffff;
      width: 327px;
      line-height: 44px;
      text-align: center;
    }
  }

  .link-wrapper {
    padding: 0 12px;

    .title {
      margin: 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: #333333;

      .label {
        color: #f78c3e;
      }
    }

    .item {
      overflow: hidden;
      border-radius: 8px;

      & + .item {
        margin-top: 12px;
      }
    }
  }

  .look-more {
    font-size: 13px;
    font-weight: 400;
    color: #3887f5;
    padding: 16px 0;

    .img-line {
      width: 16px;
      height: 16px;
    }
  }

  .float-btn {
    position: fixed;
    right: 0;
    bottom: 34px;
    z-index: 999;

    .btn-img {
      width: 149px;
      height: 76px;
    }
  }
}

.popup-wr,
.popup-wr-btm {
  position: relative;

  .img-main {
    width: 100%;
  }

  .img-btn {
    position: absolute;
    width: 287px;
    height: 54px;
    top: 154px;
    left: 20px;
  }
}

.popup-wr-btm {
  font-size: 0;

  .img-main {
    height: 262px;
  }

  .img-btn {
    width: 311px;
    top: 164px;
    left: 32px;
  }
}
</style>
