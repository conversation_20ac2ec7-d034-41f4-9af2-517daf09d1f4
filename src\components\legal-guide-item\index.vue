<template>
  <div class="background-white legal-guide-item relative">
    <img
      alt=""
      class="w-30px h-30px block absolute top-0 left-0"
      src="@/components/legal-guide-item/imgs/<EMAIL>"
    >
    <navigator-link
      :url="askDetail"
      rel="nofollow"
    >
      <div class="detail">
        <div class="text-16px font-medium text-[#333333] text-ellipsis">
          {{ data.problemDesc }}
        </div>
        <div class="pb-12px border-0 border-b-0_5px border-solid border-EEEEEE">
          <div
            class="text-[#666666] border-box text-14px font-normal text-ellipsis-2 mt-12px"
          >
            <span class="text-[#3887F5]">律师建议：</span>{{ data.consultSuggest }}
          </div>
        </div>
      </div>
    </navigator-link>
    <div class="flex items-center justify-between">
      <navigator-link
        :url="lawyerHome"
        class="flex-1"
        rel="nofollow"
      >
        <div class="flex items-center">
          <img
            :src="data.lawyerAvatar"
            alt=""
            class="w-32px h-32px rounded-full"
          >
          <div class="ml-8px">
            <div class="flex items-center">
              <div class="text-14px text-[#333333]">
                {{ data.lawyerName }}
              </div>
              <div class="text-11px text-[#999999] ml-8px">
                解答于 {{ data.createTimeDesc }}
              </div>
            </div>
            <div class="text-12px text-[#999999] mt-2px">
              {{ data.lawyerOffice }}
            </div>
          </div>
        </div>
      </navigator-link>
      <div @click="handleClick">
        <div
          class="w-72px h-27px rounded-40px border-1px border-solid border-3887F5 flex items-center justify-center border-box text-12px text-[#3887F5]"
        >
          <div>我也要问</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import NavigatorLink from "@/components/navigator-link/index.vue";
import { askDetailPage, toAskLawyer } from "@/libs/turnPages";

export default {
  name: "LegalGuideItem",
  components: { NavigatorLink },
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    /* 律师主页*/
    lawyerHome() {
      return `/pages/lawyer-home/index?id=${this.data.lawyerId}`;
    },
    /**/
    askDetail() {
      return askDetailPage({
        caseSourceServerId: this.data.caseSourceServerId
      });
    }
  },
  methods: {
    handleClick() {
      toAskLawyer({
        question: this.data.problemDesc
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.legal-guide-item {
  padding: 16px 12px 12px 12px;
  border-radius: 8px;

  .detail {
    padding-bottom: 12px;
  }
}
</style>
