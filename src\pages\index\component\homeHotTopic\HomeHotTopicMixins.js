import { dataDictionary } from "@/api";

export default {
  data(){
    return {
      homeHotTopic: []
    };
  },
  computed: {
    special(){
      return this.homeHotTopic.filter((item) => item.supplementDesc.special)?.[0] || { supplementDesc: {} };
    },
    homeHotTopicCommon(){
      return this.homeHotTopic.filter(item => !item.supplementDesc.special);
    }
  },
  mounted() {
    this.getHomeHotTopic();
  },
  methods: {
    getHomeHotTopic() {
      let groupCode = "HOME_HOT_TOPIC";
      
      // #ifdef MP-TOUTIAO
      groupCode = "HOME_HOT_TOPIC_TOUTIAO";
      // #endif

      dataDictionary({ groupCode }).then(res => {
        try {
          this.homeHotTopic = res.data.map(item => ({
            ...item,
            supplementDesc: JSON.parse(item.supplementDesc)
          }));
        } catch (e) {
          this.homeHotTopic = res.data;
        }

        // 请求其它数据
        this?.getOtherData?.();
      });
    }
  }
};
