import { requestCommon } from "@/libs/axios";

/* 我的咨询列表信息*/
export const userCaseSourcePage = (data) => requestCommon.post("/info/userCaseSource/page", data);

/* 我的咨询详情信息*/
export const userCaseSourceDetail = (caseSourceId) => requestCommon.post(`/info/userCaseSource/detail/${caseSourceId}`);

/* 点击确认完成*/
export const userCaseSourceComplete = (serverId) => requestCommon.post(`/info/userCaseSource/complete/${serverId}`);

/** 免费咨询问答追问 */
export const qaMessageContinueAsk = (data) => requestCommon.post("/info/qaMessage/continueAsk", data);

/** 
 * 我的咨询-付费咨询
 * https://showdoc.imlaw.cn/web/#/5/2435
 */
export const getMyCaseSourceServerV2Page = (data) => requestCommon.post("/info/caseSourceServerV2/getMyCaseSourceServerV2Page", data);


/** 合同分页列表 */
export const contractTemplatesPage = (data) => requestCommon.post("/info/contractTemplates/page", data);


/** 合同详情 */
export const contractTemplatesDetailById = (data) => requestCommon.post("/info/contractTemplates/detailById", data);


/** 用户购买合同模板分页 */
export const lawyerContractPage = (data) => requestCommon.post("/info/lawyerContract/page", data);
