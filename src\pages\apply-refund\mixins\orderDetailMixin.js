import { getServiceInfoByOrderId, refundInfo, refundInfoNew, userChangeLawyerApplyDetail } from "@/api/order";
import { refundTypeEnum } from "@/pages/apply-refund/js/refundInfo";
import { speedRefundType } from "@/libs/config";
import { amountFilter } from "@/libs/filter";

export default {
  data() {
    return {
      orderDetail: {},
    };
  },
  computed: {
    /** 退款界面 */
    refundType() {
      return this.routerParams.type === refundTypeEnum.REFUND;
    },
    /** 换律师界面 */
    changeLawyer() {
      return this.routerParams.type === refundTypeEnum.CHANGE_LAWYER;
    },
    /** 是否能撤销换律师申请 */
    canCancelChangeLawyer() {
      return this.changeLawyer && this.orderDetail.refundStatus === 0;
    },
    refundStatusMsg() {
      if(this.refundType){
        switch (this.orderDetail.refundStatus) {
        case speedRefundType.REFUNDING:
          return "审核中";
        case speedRefundType.REFUNDSUCESS:
          return "审核通过";
        case speedRefundType.REFUNDFAILED:
          return "审核失败";
        }
      }

      if(this.changeLawyer){
        switch (this.orderDetail.refundStatus) {
        case 0:
          return "审核中";
        case 1:
          return "审核通过";
        case 2:
          return "审核失败";
        }
      }

      return "已撤销";
    },
    refundMsg() {
      // 退款界面
      if (this.refundType) {
        switch (this.orderDetail.refundStatus) {
        case speedRefundType.REFUNDING:
          return "请耐心等待，平台在3-5个工作日处理退款";
        case speedRefundType.REFUNDSUCESS:
          return `平台退款申请审核已通过，退款金额 ¥${amountFilter(
            this.orderDetail.price || this.orderDetail.payAmount
          )}`;
        case speedRefundType.REFUNDFAILED:
          return "平台退款申请审核未通过，退款关闭交易结束";
        }
      }

      // 换律师界面
      if (this.changeLawyer) {
        switch (this.orderDetail.refundStatus) {
        case 0:
          return "工作人员即将为您审核，非工作日审核较慢，请耐心等待";
        case 1:
          return "平台已为您派新的律师，您可在咨询-付费咨询中查看";
        case 2:
          return "您的换律师请求平台不通过";
        case 9:
          return "您已撤销申请";
        }
      }

      return "";
    },
  },
  methods: {
    /** 通过orderId获取服务信息 */
    getDetailByOrderId() {
      getServiceInfoByOrderId({
        orderId: this.routerParams.orderId,
      }).then(({ data = {} }) => {
        this.orderDetail = {
          ...data,
          createTime: data.orderTime,
          refundTypeStr: this.routerParams.refundType,
        };
      });
    },
    /** 获取服务信息 */
    getServiceInfo() {
      if (this.refundType) {
        this.getRefundInfo();
      }
      if (this.changeLawyer) {
        this.getChangeLawyerInfo();
      }
    },
    /** 获取换律师信息 */
    getChangeLawyerInfo() {
      userChangeLawyerApplyDetail({
        orderId: this.routerParams.orderId,
      }).then(({ data = {} }) => {
        this.orderDetail = {
          ...data,
          refundReason: data.changeAddNote,
          refundStatus: data.checkStatus,
          refuseReason: data.checkReason,
          refundNo: data.refundNo,
          refundLabelId: data.changeReasonLabelId,
          refundPic: data.changePic,
          refundTypeStr: this.routerParams.refundType,
        };
      });
    },
    /** 获取退款信息 */
    async getRefundInfo() {
      try {
        let refundType = this.routerParams.refundType || "historyOrder";
        let params = { orderId: this.routerParams.orderId };
        let res =
          refundType === "newOrder"
            ? await refundInfoNew(params)
            : await refundInfo(params);
        if (res.data)
          this.orderDetail = {
            ...res.data,
            createTime: res.data.orderTime,
            refundTypeStr: this.routerParams.refundType,
          };
      } catch (error) {
        console.log(error);
      }
    },
  },
};
