<template>
  <app-popup
    :show="getShow"
    :zIndex="10002"
    class="popup"
    lowCancelButton
    mode="center"
    @cancel="close"
  >
    <div>
      <div class="w-[311px] border-t-[1px] border-white rounded-[16px] bg-gradient-to-br from-[#f9faff] to-[#dde7ff] box-border relative pt-[37px]">
        <img
          v-if="data.imgUrl"
          :src="data.imgUrl"
          alt=""
          class="-mt-[37px] mx-auto block w-[74px] h-[74px] border-[4px] border-white rounded-full overflow-hidden"
        >
        <img
          v-else
          class="-mt-[37px] mx-auto block w-[74px] h-[74px] border-[4px] border-white rounded-full overflow-hidden"
          src="../../../assets/<EMAIL>"
        >
        <p class="text-center pt-[10px] pb-[12px] text-[16px] font-bold text-[#333333]">
          {{ data.realName }} 律师
        </p>
        <p class="text-[13px] font-normal text-[#666666] max-w-[263px] mx-auto my-0">
          律师接单后，律师会以电话形式与您沟通，在律师服务时间段内，不限拨打次数和通话时长
        </p>
        <div class="pt-[18px] px-[24px] pb-[24px] relative">
          <div class="flex bg-white rounded-[8px] box-border p-[16px]">
            <img
              :src="productInfo.icon"
              alt=""
              class="w-[44px] h-[44px] mr-[12px] rounded-[8px]"
            >
            <div class="flex-1 flex flex-col justify-between">
              <p class="text-[16px] font-bold text-[#333333]">
                电话咨询
              </p>
              <p class="text-[12px] font-normal text-[#999999]">
                规格：{{ productInfo.serviceNum }}{{ productInfo.unitLabel }}
              </p>
            </div>
            <div class="text-[16px] font-bold text-[#333333]">
              ¥{{ productInfo.servicePrice | amountFilter }}
            </div>
          </div>
        </div>
        <div class="flex justify-center">
          <img
            alt=""
            class="w-[263px] h-[52px] mx-auto mb-[30px]"
            src="@/pages/lawyer-im/assets/<EMAIL>"
            @click="toOrder"
          >
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import { toConfirmOrder } from "@/libs/tools";
import AppPopup from "@/components/app-components/app-popup";
import { buryPointChannelBasics } from "@/api/burypoint.js";
import { BURY_POINT_CHANNEL_TYPE, POINT_CODE } from "@/enum/burypoint.js";

export default {
  name: "PraisePopup",
  components: {
    AppPopup,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
      desc: "律师信息",
    },
    serverId: {
      type: Number,
      default: null,
    },
    /* 商品信息*/
    productInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      activeIndex: 0,
    };
  },
  computed: {
    getShow: {
      set(value) {
        this.$emit("input", value);
      },
      get() {
        return this.value;
      },
    },
  },
  watch: {
    getShow() {
      if (this.getShow) {
        buryPointChannelBasics({
          code: POINT_CODE.LAW_APPLET_APPRECIATE_POPUP_PAGE,
          behavior: BURY_POINT_CHANNEL_TYPE.VI,
        });
      }
    },
  },
  mounted() {
    console.log(this.productInfo);
  },
  methods: {
    close() {
      this.$emit("input", false);
    },
    selected(index) {
      this.activeIndex = index;
    },
    toOrder() {
      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_APPRECIATE_POPUP_NOW_APPRECIATE_BUTTON_CLICK,
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
      });
      toConfirmOrder({
        serviceCode: this.productInfo.serviceCode,
        type: "2",
        lawyerId: this.data.id,
        businessId: this.serverId,
      });
    },
  },
};
</script>