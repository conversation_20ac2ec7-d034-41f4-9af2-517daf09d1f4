/* !! 这个接口 后台做了限制 前端缓存一下*/
import { isArrNull, isObjNull } from "@/libs/basics-tools.js";
import { objKeySort } from "@/libs/tools.js";

/* 请求信息 queue队列 store接口缓存 state请求状态 requestTime请求时间 storeMaxTime最大缓存时间ms api请求接口 data请求参数*/
const queues = {};

/* 接口请求*/
const startRequest = (apiName) => {
  const {
    state,
    queue,
    store,
    requestTime,
    api,
    storeMaxTime,
    data: apiData
  } = queues[apiName];
  if (state) return false;
  /* 判断任务队列是否为空*/
  if (!isArrNull(queue)) {
    queues[apiName].state = true;
    /* 获取当前需要的回调*/
    const [resolve, reject] = queues[apiName].queue.shift();
    const resolveCallback = (data) => {
      queues[apiName].store = data;
      queues[apiName].state = false;
      /* 这里深拷贝 是怕有些使用地方修改了源数据 照成污染*/
      resolve(JSON.parse(JSON.stringify(data)));
      startRequest(apiName);
    };
    const currentDate = Date.now();
    /* 是否缓存时间过期*/
    const isItExpired =
      storeMaxTime === 0 ? false : currentDate - requestTime >= storeMaxTime;
    /* 判断有无缓存 是否过了缓存时间*/
    if (isObjNull(store) || isItExpired) {
      api(apiData)
        .then((res) => {
          queues[apiName].requestTime = Date.now();
          resolveCallback(res);
        })
        .catch(() => {
          queues[apiName].state = false;
          reject();
          startRequest(apiName);
        });
    } else {
      resolveCallback(store);
    }
  }
};

/* !api 是匿名函数时  cacheName一定要传！！！！*/
/**
 * @description 这个缓存接口的最终结果一定要为对象，否则可能会出现缓存失效
 * @date 2023/12/29
 * @param {Promise} data.api 接口
 * @param {Object} data.data 接口参数
 * @param {String} data.cacheName 接口缓存名称
 * @param {Number} data.storeMaxTime 接口缓存时间 0就是永久缓存，因为小程序的特殊性，不像h5可以使用刷新按钮，所以小程序不推荐使用永久缓存
 * @return {Promise}
 */
export const storeRequest = ({
  api,
  data = {},
  cacheName = "",
  storeMaxTime = 0
}) => {
  let name = cacheName || api.name || "default";
  /* 这里处理同一个接口 不同的参数缓存*/
  /* 将才接口参数排序 */
  name += JSON.stringify(objKeySort(data));
  return new Promise((resolve, reject) => {
    if (!queues[name]) {
      /* 请求信息 queue队列 api请求接口 data请求参数 store接口缓存 state请求状态 requestTime请求时间*/
      queues[name] = {
        queue: [],
        store: {},
        state: false,
        requestTime: 0,
        storeMaxTime,
        api,
        data
      };
    }
    queues[name].queue.push([resolve, reject]);
    startRequest(name);
  });
};
