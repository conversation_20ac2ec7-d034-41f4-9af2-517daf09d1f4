<template>
  <div class="h-screen case-details-prefect flex flex-column">
    <my-litigate-container position="top">
      <div class="pd-bm-16 pd-tp-16">
        <case-type-select :typeLabel="caseInfo.typeLabel" />
      </div>
    </my-litigate-container>
    <div class="flex-1 case-details-undone">
      <my-litigate-container>
        <case-details-prefect-undone :caseSourceData="caseSourceData" />
      </my-litigate-container>
    </div>
  </div>
</template>

<script>
import MyLitigateContainer from "@/components/my-litigate/my-litigate-container.vue";
import CaseTypeSelect from "@/pages/case-details/components/case-details/case-type-select.vue";
import CaseDetailsPrefectUndone from "@/pages/case-details/components/case-details/case-details-prefect-undone.vue";
import { userCaseSourceDetail } from "@/api/my-consultation-new";
// import {caseDetailsPageUndoneBuried} from './js/index.js'
/**
 * 完善案件资料界面
 * @FilePath pages\case-details\prefect\index.vue
 */
export default {
  name: "CaseDetailsPerfect",
  components: {
    CaseDetailsPrefectUndone,
    CaseTypeSelect,
    MyLitigateContainer
  },
  data() {
    return {
      /** 当前的案件类型 */
      caseInfo: {
        typeValue: "",
        typeLabel: "",
      },
      caseSourceData: {},
    };
  },
  onLoad(options) {
    // // 埋点 页面访问PV、UV埋点
    // caseDetailsPageUndoneBuried()
    /** 获取案源详情 */
    userCaseSourceDetail(options.id).then((res) => {
      this.caseSourceData = res.data;

      const { typeValue, typeLabel } = res.data;

      this.caseInfo = {
        typeValue,
        typeLabel,
      };
    });
  },
};
</script>

<style lang="scss" scoped>
.case-details-undone{
  background: #fff;
}
</style>
