import { mapActions, mapState } from "vuex";

import { checkAppLoginAndGetCode, getUserTokenStorage } from "@/libs/token";

import { commonLoginRequest } from "@/api/law-login";

import { buryPointChannelBasics } from "@/api/burypoint.js";
import { BURY_POINT_CHANNEL_TYPE, POINT_CODE } from "@/enum/burypoint.js";
import { getDyXcxChannel } from "@/api/user";

export default {
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo,
      token: state => state.user.token
    }),
    makeSureLogin() {
      return !!(this.token && this.userInfo.id && getUserTokenStorage());
    }
  },
  methods: {
    ...mapActions({
      setToken: "user/setToken",
      setUserIfo: "user/setUserInfo",
      setOpenid: "user/setOpenid",
      userLoginIM: "im/userLogin"
    }),
    handleClick() {
      // 按钮点击事件
      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_LOGIN_POPUP_ALLOW_CLICK,
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
        type: 1
      });
    },
    /*
          获取 相关app 登录状态和 code

          字节小程序：
          ! 使用（用户点击 button）前需先调用tt.login接口。如果在回调中调用 tt.login 会刷新登录态，导致登录后换取的 session_key 与手机号码加密时使用的 session_key 不同，
          ! 从而导致解密失败。
        */
    // 手机号授权登录
    async getPhoneNumberHandler(e) {
      // 目前（v1.0.1）新用户会直接进入问律师，此时需要带人转化路径 （目前已取消该路径）
      // if(this.needTransformationPath) {
      //   console.log('获取到：needTransformationPath');
      //   buryPointTransformationPath.new(globalTransformationPath.LAW_APPLET_ONE_CLICK_LOGIN)
      // }
      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_ONE_CLICK_LOGIN,
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
        type: 1
      });

      buryPointChannelBasics({
        code: "LAW_APPLET_ASK_LAWYER_LOGIN_POP_UP_PAGE_LOGIN_CLICK",
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
        type: 1
      });

      // ! 支付宝登陆单独处理
      // #ifdef MP-ALIPAY
      my.getPhoneNumber({
        success: res => {
          buryPointChannelBasics({
            code: POINT_CODE.LAW_APPLET_LOGIN_CLICK,
            behavior: BURY_POINT_CHANNEL_TYPE.CK,
            type: 1
          });

          console.log("获取手机号成功", res);
          this.login(res.response);
        },
        fail: () => {
          uni.showToast({
            title: "您拒绝了授权手机号快捷登录",
            icon: "none"
          });
        }
      });
      // #endif

      // #ifndef MP-ALIPAY
      if (e.detail.encryptedData) {
        buryPointChannelBasics({
          code: POINT_CODE.LAW_APPLET_LOGIN_CLICK,
          behavior: BURY_POINT_CHANNEL_TYPE.CK,
          type: 1
        });
        this.login(e.detail);
      } else {
        uni.showToast({
          title: "您拒绝了授权手机号快捷登录",
          icon: "none"
        });
      }
      // #endif
    },
    async login(eniv) {
      uni.showLoading({
        title: "登录中"
      });
      // #ifdef MP-ALIPAY
      // 支付宝传入的是一个单独的字符串
      const params = {
        code: uni.getStorageSync("loginCode"),
        encryptedData: eniv
      };
      // #endif
      // #ifndef MP-ALIPAY
      const params = {
        code: uni.getStorageSync("loginCode"),
        encryptedData: eniv.encryptedData,
        iv: eniv.iv
      };
      // #endif
      const loginQuery = uni.getStorageSync("loginQuery");
      // 登陆时需要使用的参数
      if (loginQuery) Object.assign(params, loginQuery);

      const lawyerShareCode = uni.getStorageSync("lawyerShareCode");
      if (lawyerShareCode) params.lawyerShareCode = lawyerShareCode;
      try {
        const result = await commonLoginRequest(params);
        if (result.code === 0) {
          this.$store.commit("user/SET_LOGIN_INFO",result.data)
          // 登录成功的回调
          await this.setData(result.data.token);
          this.setOpenid(result.data.openid);
          this.$nextTick(() => {
            this.loginSuccess();
            this.getUserChannelId();
          });
          uni.showToast({
            title: "登录成功",
            icon: "none"
          });
        }
        uni.hideLoading();
      } catch (error) {
        console.log(error, "error");
        uni.hideLoading();
        uni.showToast({
          title: error?.message,
          icon: "none"
        });
        checkAppLoginAndGetCode();
      }
    },

    async setData(token) {
      await this.setToken(token);
      await this.setUserIfo();
      await this.userLoginIM();
    },

    getUserChannelId() {
      // #ifdef MP-TOUTIAO
      // #endif
      getDyXcxChannel().then(({ data = {} }) => {
        if (data && data.channelId) {
          uni.setStorageSync("shareChannelId", data.channelId);
        }
      });
    }
  }
};
