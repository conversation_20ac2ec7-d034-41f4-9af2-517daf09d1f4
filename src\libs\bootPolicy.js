import { getUserTokenStorage } from "@/libs/token.js";
import { caseSourceV2GetCaseOrZx } from "@/api/special.js";
import { userActionInfo } from "@/api/user.js";
import { sceneType } from "@/libs/config.js";
import dayjs from "dayjs";
import { getCommonConfigKey } from "@/api/index.js";

/* 访问律师支付页 间隔时间*/
let  askLawyerPayPageTime = -1;

/* 9.9场景*/
const nineBlocksOfNineScenes = {
  // 场景
  scene: sceneType.jjclwd,
  /* 场景标题*/
  sceneTitle: "一问多答",
  /* 标题*/
  title: "长时间无律师回复？",
  /* 描述*/
  desc: "提醒多个律师优先解答；",
};

/* 29.9场景  一阶段和二阶段的文案提示不一样*/
const twentyNineBlocksOfTwentyNineScenes = (type = 0) => ({
  // 场景
  scene: sceneType.jjcltw,
  /* 场景标题*/
  sceneTitle: "图文咨询",
  /* 标题*/
  title: ["叮~限时优惠已到账！", "优惠倒计时进行中！"][type],
  /* 描述*/
  desc: ["1对1私聊 快速解决法律难题；", "1对1剖析案情 提供解决方案；"][type],
});
/* 49.9场景*/
const fortyNineBlocksOfFortyNineScenes = {
  // 场景
  scene: sceneType.jjcldh,
  /* 场景标题*/
  sceneTitle: "电话咨询",
  /* 标题*/
  title: "叮~限时优惠已到账！",
  /* 描述*/
  desc: "1对1私聊 快速解决法律难题；",
};

/* 3.0.9小程序引导策略*/


/* 本地页面行为缓存枚举*/
export const  BOOT_POLICY_USER_ACTION_STORE_ENUM = {
  /* 律师假im发送数 */
  THE_NUMBER_OF_LAWYERS_SENT_FAKE_IM: "THE_NUMBER_OF_LAWYERS_SENT_FAKE_IM",
  /* 律师假im电话咨询弹窗访问数*/
  THE_NUMBER_OF_LAWYERS_SENT_FAKE_IM_PHONE: "THE_NUMBER_OF_LAWYERS_SENT_FAKE_IM_PHONE",
  /* 进入电话咨询访问数*/
  THE_NUMBER_OF_VISITS_TO_TELEPHONE_CONSULTATION: "THE_NUMBER_OF_VISITS_TO_TELEPHONE_CONSULTATION",
  /* 问律师支付页访问数*/
  THE_NUMBER_OF_ASK_LAWYER_PAY_PAGE: "THE_NUMBER_OF_ASK_LAWYER_PAY_PAGE",
  /* 首次登录的留资状态*/
  FIRST_LOGIN_LEAVE_INFO_STATUS: "FIRST_LOGIN_LEAVE_INFO_STATUS",
  /* 点击过的场景 用于隐藏首页*/
  CLICKED_SCENE: "CLICKED_SCENE",
};


/* 获取本地页面行为数据*/
export const getBootPolicyUserActionStore = (key) => {
  let userActionStore = {};
  try {
    userActionStore = JSON.parse(uni.getStorageSync("userActionStore") || "{}");
  } catch (e) {
    console.log(e);
  }
  return key ? userActionStore[key] : userActionStore;
};

/* 本地储存页面行为数据 每条数据都有time时间搓 num访问行为数量*/
export const setBootPolicyUserActionStore = (key, data = {}) => {
  if(!key || !getUserTokenStorage()) return;
  let userActionStore = getBootPolicyUserActionStore() || {};
  userActionStore[key] = {
    time: +new Date(),
    num: (userActionStore[key] || {}).num + 1 || 1,
    ...(data || {})
  };
  uni.setStorageSync("userActionStore", JSON.stringify(userActionStore));
};

/* 本地储存最近一次的策略场景*/
export const setBootPolicyScene = (data = {}) => {
  uni.setStorageSync("bootPolicyScene", JSON.stringify(data));
};
/* 获取本地存储的最近一次场景策略*/
export const getBootPolicyScene = () => {
  let bootPolicyScene = [];
  try {
    bootPolicyScene = JSON.parse(uni.getStorageSync("bootPolicyScene") || "[]");
  } catch (e) {
    console.log(e);
  }
  return bootPolicyScene;
};


/* 本地存储页面行为缓存 依某个字段去重存储*/
/*
* @param key 本地存储的key
* @param id 去重的value
* @param data 其他数据
* */
export const setBootPolicyUserActionStoreByField = (key, id, data = {}) => {
  if(!key || !getUserTokenStorage()) return;
  const userActionStoreData = (getBootPolicyUserActionStore() || {})[key];
  /* 如果之前没有存这个数据 初始话一次*/
  if(!userActionStoreData) {
    return setBootPolicyUserActionStore(key, {
      field: [id],
      ...data
    });
  }
  /* 有这个缓存 取查一次field这个字段里面有没有重复的id*/
  const field = userActionStoreData.field || [];
  /* 如果有重复的id 不存储*/
  if(field.includes(id)) return;
  /* 如果没有重复的id 存储*/
  field.push(id);
  setBootPolicyUserActionStore(key, {
    field,
    ...data
  });
};


/* 判断是否在问律师页面看过低价后时长是否大于5*/
export const isAskLawyerPageTime = () => {
  const time = getBootPolicyUserActionStore(BOOT_POLICY_USER_ACTION_STORE_ENUM.THE_NUMBER_OF_ASK_LAWYER_PAY_PAGE)?.time;
  /* dayjs判断time和当前时间差大于5分钟*/
  const maxTime = time && (dayjs(+new Date()).diff(dayjs(time), "minute") >= askLawyerPayPageTime);
  if(!maxTime) console.log("time和当前时间差小于" + askLawyerPayPageTime + "分钟");
  console.log("time和当前时间差大于" + askLawyerPayPageTime + "分钟", maxTime);
  return maxTime;
};

/* 判断是否在问律师页面看过低价场景*/
export const isAskLawyerPage = () => {
  /* 有没有到过问律师支付页面*/
  const isAskLawyer = !!getBootPolicyUserActionStore(BOOT_POLICY_USER_ACTION_STORE_ENUM.THE_NUMBER_OF_ASK_LAWYER_PAY_PAGE);
  if(!isAskLawyer) console.log("没有到过问律师支付页面");
  console.log("律师支付页面状态", isAskLawyer);
  return isAskLawyer && isAskLawyerPageTime();
};



/* 到达过问律师支付页面5分钟后流程*/
const  askLawyerPayPage = () => {
  /* 判断到达过问律师支付页面5分钟*/
  if(!isAskLawyerPage()) return Promise.resolve([]);
  /* 这里到了 需要判断问答 还是案源*/
  return caseSourceV2GetCaseOrZx().then((res) => {
    /** 0 无 1 案源 2 问答 */
    const type = Number((res.data || {}).type);
    console.log("案源状态", type);
    /* 如果是案源返回空数组*/
    if(type === 0 ) return [];
    return userActionInfo().then(({ data }) => {
      /* qaReplyNum律师回复问答数 paySuccessNum用户支付成功订单数 */
      const { paySuccessNum, qaReplyNum } = data;
      console.log("问答回复数", qaReplyNum);
      console.log("支付成功数", paySuccessNum);
      /* 如果是问答 需要判断无支付订单+无律师回复 推9.9元悬赏*/
      if(type === 2 && paySuccessNum === 0 && qaReplyNum === 0){
        console.log("问答 无支付订单+无律师回复");
        /* 首页只需要展示9.9  问律师页需要展示2个*/
        return [{
          ...twentyNineBlocksOfTwentyNineScenes(1),
          hidden: true,
        }, nineBlocksOfNineScenes];
      }
      /* 如果是案源 需要判断无支付订单 29.9元图文咨询sku*/
      if(type === 1 && paySuccessNum === 0){
        console.log("案源 无支付订单");
        return [twentyNineBlocksOfTwentyNineScenes(1)];
      }
      /* 没有流程返回空数组*/
      return  [];
    });
  });
};


/* 有留资流程*/
const   noLeaveInfoProcess = () => {
  const {
    [BOOT_POLICY_USER_ACTION_STORE_ENUM.THE_NUMBER_OF_LAWYERS_SENT_FAKE_IM]: theNumberOfLawyersSentFakeIm,
    [BOOT_POLICY_USER_ACTION_STORE_ENUM.THE_NUMBER_OF_LAWYERS_SENT_FAKE_IM_PHONE]: theNumberOfLawyersSentFakeImPhone,
  } = getBootPolicyUserActionStore();
  /* 仅1v1IM行为≥2次 29.9元图文咨询sku*/
  if(theNumberOfLawyersSentFakeIm?.num >= 2){
    console.log("仅1v1IM行为≥2次 29.9元图文咨询sku");
    return  [twentyNineBlocksOfTwentyNineScenes()];
  }
  /* 仅电话行为≥2次 49.9元电话咨询sku*/
  if(theNumberOfLawyersSentFakeImPhone?.num >= 2){
    console.log("仅电话行为≥2次 49.9元电话咨询sku");
    return  [fortyNineBlocksOfFortyNineScenes];
  }

  /* 如果(1v1IM行为+仅电话行为)≥2次 判断最近访问的哪个*/
  if(theNumberOfLawyersSentFakeImPhone?.num + theNumberOfLawyersSentFakeIm?.num >= 2){
    console.log("(1v1IM行为+仅电话行为)≥2次 判断最近访问的哪个");
    const bootPolicyConfig = [{
      ...twentyNineBlocksOfTwentyNineScenes(),
      time: theNumberOfLawyersSentFakeIm?.time,
    }, {
      ...fortyNineBlocksOfFortyNineScenes,
      time: theNumberOfLawyersSentFakeImPhone?.time,
    }];
    bootPolicyConfig.reduce((prev, next) => {
      if(prev.time > next.time) next.hidden = true;
      else prev.hidden = true;
      return next;
    });
    return  bootPolicyConfig;
  }
  return [];
};

/* 没有留资流程*/
const   leaveInfoProcess = () => {
  const {
    [BOOT_POLICY_USER_ACTION_STORE_ENUM.THE_NUMBER_OF_LAWYERS_SENT_FAKE_IM]: theNumberOfLawyersSentFakeIm,
    [BOOT_POLICY_USER_ACTION_STORE_ENUM.THE_NUMBER_OF_LAWYERS_SENT_FAKE_IM_PHONE]: theNumberOfLawyersSentFakeImPhone,
    [BOOT_POLICY_USER_ACTION_STORE_ENUM.THE_NUMBER_OF_VISITS_TO_TELEPHONE_CONSULTATION]: theNumberOfVisitsToTelephoneConsultation,
  } = getBootPolicyUserActionStore();
  /* 1v1IM行为1次 29.9元图文咨询sku*/
  if(theNumberOfLawyersSentFakeIm?.num >= 1){
    console.log("1v1IM行为1次 29.9元图文咨询sku");
    return [twentyNineBlocksOfTwentyNineScenes()];
  }
  /* 1v1电话行为1次 49.9元电话咨询sku*/
  if(theNumberOfLawyersSentFakeImPhone?.num >= 1){
    console.log("1v1电话行为1次 49.9元电话咨询sku");
    return [fortyNineBlocksOfFortyNineScenes];
  }
  /* 进入电话咨询1次 49.9元电话咨询sku*/
  if(theNumberOfVisitsToTelephoneConsultation?.num >= 1){
    console.log("进入电话咨询1次 49.9元电话咨询sku");
    return [fortyNineBlocksOfFortyNineScenes];
  }
  /* 没有命中规则返回空数组*/
  return [];
};



/*
* 一阶段： 没有走问律师支付页面
* 二阶段： 走过问律师支付页面
* */

/* 1.判断是否登录 获取初始登录状态是否有留资*/
/* 2.判读有没有到过问律师支付页面 走阶段二流程 判断留资是案源还是问答  */
/* 3.判断初始登录状态 有没有留资*/
export const getBootPolicyConfig = () => {
  return  new Promise(async (resolve) => {
    // if(!getUserTokenStorage()) return resolve([]);
    // if(askLawyerPayPageTime === -1){
    //   askLawyerPayPageTime = await  getCommonConfigKey({ paramName: "user_reduce_price_delay_minite_5" }).then(({ data }) => Number(data.paramValue));
    // }
    // console.log("间隔时间", askLawyerPayPageTime);
    // /* 有没有到过问律师支付页面*/
    // const isAskLawyer = !!getBootPolicyUserActionStore(BOOT_POLICY_USER_ACTION_STORE_ENUM.THE_NUMBER_OF_ASK_LAWYER_PAY_PAGE);
    // console.log("有没有到过问律师支付页面", isAskLawyer);
    // const leaveInfo =   getBootPolicyUserActionStore(BOOT_POLICY_USER_ACTION_STORE_ENUM.FIRST_LOGIN_LEAVE_INFO_STATUS);
    // /* 初始登录状态是否有留资*/
    // const isLeaveInfo =  leaveInfo && leaveInfo.state;
    // console.log("初始登录留资状态", isLeaveInfo);
    //
    // /* 1.判读有没有到过问律师支付页面 走阶段二流程 判断留资是案源还是问答  */
    // if(isAskLawyerPage()){
    //   console.log("到过问律师支付页面 并且时长到达5分钟 走阶段二流程");
    //   return askLawyerPayPage().then(resolve, reject);
    // }
    //
    // /* 优先返回缓存的场景值 访问过低价问律师支付页并且访问后时长小于5分钟 返回缓存的场景*/
    // /* 首页需要隐藏 这里只是为了 让问律师一直显示低价产品*/
    // const bootPolicyScene = getBootPolicyScene();
    // if(bootPolicyScene.length > 0) {
    //   console.log("优先返回缓存的场景值");
    //   return resolve(bootPolicyScene.map((item) => ({ ...item, hidden: item.hidden ? item.hidden : isAskLawyer && !isAskLawyerPageTime() })));
    // }
    //
    // /* 3.判断初始登录状态 有留资*/
    // if(isLeaveInfo){
    //   console.log("初始登录状态 有留资");
    //   const bootPolicyScene = leaveInfoProcess();
    //   setBootPolicyScene(bootPolicyScene);
    //   return  resolve(bootPolicyScene);
    // }
    // /* 4.判断初始登录状态 没有留资*/
    // if(!isLeaveInfo){
    //   console.log("初始登录状态 没有留资");
    //   const bootPolicyScene = noLeaveInfoProcess();
    //   setBootPolicyScene(bootPolicyScene);
    //   return  resolve(bootPolicyScene);
    // }
    /* 没有命中规则返回空数组*/
    return resolve([]);
  });
};

