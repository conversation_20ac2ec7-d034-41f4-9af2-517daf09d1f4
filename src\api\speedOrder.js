/** 案源咨询服务订单相关 */
import { requestCommon } from "@/libs/axios";

/* 案源咨询订单列表*/
export const speedOrderList = (data) => requestCommon.post("/order/orderCaseSource/orders", data);

/* 案源咨询订单状态查询 路径参数【订单编号】*/
export const speedOrderQuery = (orderNo) => requestCommon.post(`/order/orderCaseSource/query/${orderNo}`);

/* 去支付已有订单Id案源咨询获取订单服务详情*/
// export const speedOrderDetail = (orderId) => requestCommon.post(`/info/serviceManage/serviceOrderPrice/${orderId}`)
export const speedOrderDetail = (data) => requestCommon.post("/info/serviceManage/serviceOrderPrice", data);

// /* 案源咨询订单 支付 并 创建订单*/
// export const speedOrderMakePay = (data) => requestCommon.post('/order/orderCaseSource/pay', data)

/* 去支付（订单列表中的去支付）*/
// export const speedOrderGoPay = (data) => requestCommon.post('/order/orderCaseSource/goPay', data)
export const speedOrderGoPay = (data) => requestCommon.post("/order/orderCaseSource/pay", data);

/* 案源咨询服务评价评价*/
export const speedOrderAppraise = (data) => requestCommon.post("/info/caseSourceEvaluate/evaluation", data);

/* 获取付费服务价格*/
export const getServicePrice = (data) => requestCommon.post("/info/serviceManage/servicePrice", data);

/* 获取用户极速咨询列表*/
export const getSpeedSourcePay = (data) => requestCommon.post("/info/caseSourcePay/page", data);

/* 获取用户公共咨询(免费案源)列表*/
export const getCommonSourceFree = (data) => requestCommon.post("/info/caseSourceFree/page", data);

/* 获取用户深度案源列表*/
export const getCommonDeepSourceFree = (data) => requestCommon.post("/info/caseSource/page", data);

/* 公共付费案源新增*/
export const addCommonCase = (data) => requestCommon.post("/info/caseSourcePay/add", data);

/* 公共案源新增前校验 info*/
export const checkInfoAddCommonCase = (data) => requestCommon.post("/info/caseSourcePay/checkInfo", data);

/* 免费案源新增*/
export const addCommonCaseFree = (data) => requestCommon.post("/info/caseSourceFree/add", data);

/* 免费案源新增前校验 info*/
export const checkInfoAddCommonCaseFree = (data) => requestCommon.post("/info/caseSourceFree/checkInfo", data);

/* 案源订单-退款时限查询*/
export const refundTime = (data) => requestCommon.post("/order/orderCaseSourceRefund/refundTime", data);
