const  { minidev } =  require("minidev");

// minidev.login() 返回的 promise resolve 代表登录完成，如过登录失败，会 reject
const login = async () => {
  await minidev.login({
    project: "dist/build/mp-alipay",
    output: "./minicode_compile_dy"
  }, (loginTask) => {
    // Node.js API 方式可以通过第二个参数获取 loginTask 以便后续展示等
    loginTask.on("qrcode-generated", (qrcodeUrl) => {
      // 已获取登录图片，进行后续展示
      console.log(qrcodeUrl);
    });

    loginTask.on("polling", (remainingMs) => {
      console.log(`二维码过期时间: ${Math.floor(remainingMs / 1000)}s`);
    });

    loginTask.on("success", (remainingMs) => {
      console.log("完成授权");
    });

    loginTask.on("scan", (remainingMs) => {
      console.log("已扫描, 请在手机上确认");
    });

  });
};

login();
