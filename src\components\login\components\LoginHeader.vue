<template>
  <div class="position-relative">
    <img
      :src="headerSrc"
      alt=""
      class="w-full block"
      mode="widthFix"
    >
  </div>
</template>

<script>
export default {
  name: "LoginHeader",
  props: {
    index: {
      type: Number,
      default: 0,
      required: true
    }
  },
  computed: {
    headerSrc() {
      const pic = {
        1: require("@/components/login/imgs/header/Group8467.png"),
        2: require("@/components/login/imgs/header/Frame1321315711.png"),
      };

      return pic[this.index];
    }
  }
};
</script>

