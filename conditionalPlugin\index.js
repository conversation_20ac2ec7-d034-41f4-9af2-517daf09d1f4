const { ConcatSource } = require("webpack-sources");
const { getGroupedEntries, getPlatform } = require("./utils");
const { appPlatform } = require("./default");
const MagicString = require("magic-string");
const fs = require("node:fs");
const path = require("node:path");
const { Parser } = require("htmlparser2");
const { writeFileSync } = require("fs");

/** 以 || 分割字符串，并且去掉空格 */
function splitAndTrim(str) {
  if (!str || typeof str !== "string") return [];

  return str.split("||").map((s) => s.trim());
}

/**
 * 修改 mini.project.json 文件，添加 transpile 配置
 * @param {string} projectPath 项目路径
 */
const updateMiniProjectConfig = (projectPath) => {
  const configPath = path.resolve(projectPath, "mini.project.json");

  let config = {
    format: 2,
    compileOptions: {
      component2: true,
      transpile: {
        script: {
          ignore: ["node_modules/**"],
        },
      },
    },
  };

  // 写回文件
  try {
    writeFileSync(configPath, JSON.stringify(config, null, 2), "utf8");
    console.log("成功更新 mini.project.json 配置");
  } catch (error) {
    console.error("写入 mini.project.json 失败:", error.message);
    throw error;
  }
};


class ConditionalPlugin {
  constructor(options) {
    this.options = options;
    this.platform = this.options.platform || appPlatform;
  }

  apply(compiler) {
    const currPlatform = getPlatform();

    // const pagesJson = fs.readFileSync(path.resolve(__dirname, `./pages-json/pages.${currPlatform}.json`), "utf-8");

    // fs.writeFileSync(path.resolve(__dirname, "../src/pages.json"), pagesJson, "utf-8");

    // 在uni 编译后阶段调用
    compiler.hooks.emit.tapPromise("ConditionalPlugin", async (compilation) => {
      const assets = compilation.assets;
      const entries = Object.entries(assets);

      const groupedEntries = getGroupedEntries(entries);

      const htmlList = groupedEntries.html;

      // 处理 html
      for (let i = 0; i < htmlList.length; i++) {
        const [file, originalSource] = htmlList[i];

        const code = originalSource.source().toString();

        const s = new MagicString(code);

        // if 队列
        const ifQueue = [];

        const parser = new Parser({
          onopentag(name, attributes) {
            if (name === "if") {
              const tag = name;
              const trueList = splitAndTrim(attributes["t"]);
              const falseList = splitAndTrim(attributes["f"]);

              const trueString = trueList.find(item => item === currPlatform);
              const falseString = falseList.find(item => item === currPlatform);

              const openTagStart = parser.startIndex;
              const openTagEnd = parser.endIndex + 1;

              const ifObj = {
                tag,
                trueString,
                falseString,
                openTagStart,
                openTagEnd,
                trueList,
                falseList
              };

              ifQueue.push(ifObj);
            }
          },
          onclosetag(name) {
            if (ifQueue.length > 0 && name === "if") {
              const closeTagStart = parser.startIndex;
              const closeTagEnd = parser.endIndex + 1;

              const { trueString, falseString, openTagStart, openTagEnd, trueList, falseList } = ifQueue.pop();

              if (trueString) {
                // 如果匹配到有true，则将 opentag 删除，保留中间的内容
                s.remove(closeTagStart, closeTagEnd);
                s.remove(openTagStart, openTagEnd);
              } else {
                // 如果有t属性
                if (trueList.length > 0)
                  s.remove(openTagStart, closeTagEnd);
              }

              if (falseString && falseList.length > 0) {
                s.remove(openTagStart, closeTagEnd);
              }

              // falseList 有值 并且没有匹配到 falseString 则标签保留内容
              if (!falseString && falseList.length > 0) {
                s.remove(closeTagStart, closeTagEnd);
                s.remove(openTagStart, openTagEnd);
              }

              // 如果 没有 t 和 f 属性 则同样不渲染
              if (trueList.length === 0 && falseList.length === 0) {
                s.remove(openTagStart, closeTagEnd);
              }
            }
          }
        },
        {
          xmlMode: true
        });

        parser.write(s.original);
        parser.end();

        const result = s.toString();

        if (result !== code) {
          const source = new ConcatSource(result);
          compilation.updateAsset(file, source);
        }
      }
    });

    compiler.hooks.done.tap("ConditionalPlugin", () => {
      // 只要触发了更新，则重写 对应的 pages.json
      // const newPagesJson = fs.readFileSync(path.resolve(__dirname, "../src/pages.json"), "utf-8");

      // fs.writeFileSync(path.resolve(__dirname, `./pages-json/pages.${currPlatform}.json`), newPagesJson, "utf-8");

      // 修改 mini.project.json 配置
      const projectPath = path.resolve(__dirname, "../dist/dev/mp-alipay");
      updateMiniProjectConfig(projectPath);
    });
  }
}

module.exports = ConditionalPlugin;
