<template>
  <div class="flex spinning-box items-center">
    <div class="spinning-icon" />
    <div class="spinning-icon" />
    <div class="spinning-icon" />
  </div>
</template>

<script>
export default {
  name: "SubmitSpinItem",
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style lang="scss" scoped>
.spinning-box {
  width: 27px;
  height: 24px;
}

.spinning-icon {
  width: 5px;
  height: 5px;
  background: #eeeeee;
  opacity: 1;
  border-radius: 50%;

  &:nth-child(n) {
    margin-left: 4px;
  }

  &:nth-child(1) {
    margin-left: 0;
    animation: spin 1s linear infinite;
  }

  &:nth-child(2) {
    animation: spin 1s linear infinite;
    animation-delay: 0.33s;
  }

  &:nth-child(3) {
    animation: spin 1s linear infinite;
    animation-delay: 0.66s;
  }
}

@keyframes spin {
  0% {
    background: #a0cffb;
    opacity: 1;
  }

  50% {
    background: #a0cffb;
    opacity: 0.6;
  }

  100% {
    background: #a0cffb;
    opacity: 0.2;
  }
}
</style>
