export default  {
  props: {
    show: {
      type: Boolean,
      default: false,
      required: true
    },
    agreement: {
      type: Boolean,
      default: false,
      required: true
    },
    index: {
      type: Number,
      default: 0,
      required: true
    },
    zIndex: {
      type: Number,
      default: 99997
    }
  },
  computed: {
    check: {
      get() {
        return this.agreement;
      },
      set(val) {
        this.$emit("setAgreement", val);
      }
    }
  },
  methods: {
    btnHandleClick(flag, firstPopup) {
      this.$emit("btnHandleClick", {
        flag,
        firstPopup
      });
    },
    getPhoneNumberHandler(event) {
      this.$emit("getPhoneNumberHandler", event);
    },
    loginPopupClose() {
      this.$emit("loginPopupClose");
    },
    protocolClick(index) {
      this.$emit("protocolClick", index);
    },
    open() {
      this.$emit("open");
    }
  }
};
