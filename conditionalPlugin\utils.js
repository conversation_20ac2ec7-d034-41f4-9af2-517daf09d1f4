function groupBy(arr, cb) {
  if (!Array.isArray(arr)) {
    throw new TypeError("expected an array for first argument");
  }

  if (typeof cb !== "function") {
    throw new TypeError("expected a function for second argument");
  }

  const result = {};
  for (const item of arr) {
    const bucketCategory = cb(item);
    const bucket = result[bucketCategory];

    if (Array.isArray(bucket)) {
      result[bucketCategory].push(item);
    } else {
      result[bucketCategory] = [item];
    }
  }

  return result;
}

const defaultOptions = {
  cssMatcher: (file) => /.+\.(?:wx|ac|jx|tt|q|c)ss$/.test(file),
  htmlMatcher: (file) => /.+\.(?:(?:(?:wx|ax|jx|ks|tt|q)ml)|swan)$/.test(file),
  jsonMatcher: (file) => typeof file === "string" && file.endsWith(".json"),
  jsMatcher: (file) => {
    if (file.includes("node_modules")) {
      return false;
    }
    // remove jsx tsx ts case
    return /.+\.[cm]?js?$/.test(file);
  },
  wxsMatcher: (file) => {
    return false;
  },
};

function getGroupedEntries(entries) {
  const { cssMatcher, htmlMatcher, jsMatcher, wxsMatcher, jsonMatcher } = defaultOptions;
  return groupBy(entries, ([file]) => {
    if (cssMatcher(file)) {
      return "css";
    } else if (htmlMatcher(file)) {
      return "html";
    } else if (jsMatcher(file) || wxsMatcher(file)) {
      return "js";
    }
    else if (jsonMatcher(file)) {
      return "json";
    }
    else {
      return "other";
    }
  });
}

function getPlatform(platName){
  // 如果没有 UNI_SCRIPT 则表明是原版，默认给个值
  const UNI_SCRIPT = platName || process.env.UNI_SCRIPT || "origin";

  return  UNI_SCRIPT.split("-").pop();
}



module.exports = {
  getGroupedEntries,
  getPlatform,
};
