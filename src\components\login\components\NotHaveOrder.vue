<template>
  <div>
    <!-- 返回拦截弹窗 -->
    <app-popup
      :closeOnClickOverlay="false"
      :safeAreaInsetBottom="true"
      :show="show"
      :zIndex="9998"
      mode="center"
      @cancel="close"
    >
      <img
        v-if="!showContent"
        alt=""
        class="mx-auto w-[250px] h-[250px] block"
        src="@/components/login/imgs/123.gif"
        @load="animationEnd"
      >
      <div
        v-if="showContent"
        v-show="className"
        :class="[className]"
        class="w-[311px] h-[499px] position-relative border-box"
      >
        <img
          alt=""
          class="background-image"
          src="@/components/submit-discount-popup-new/img/<EMAIL>"
          @load="picOnLoad"
        >
        <img
          alt=""
          class="w-[20px] h-[20px] block absolute right-[20px] top-[15px]"
          src="@/components/submit-discount-popup-new/img/<EMAIL>"
          @click="close"
        >
        <div class="pt-[16px] pl-[25px]">
          <div class="flex items-center">
            <span class="font-bold text-[20px] text-[#FFFFFF]">限时福利</span>
            <div class="flex items-center ml-[8px]">
              <span class="text-[12px] text-[#FFFFFF]">每日限量</span>
              <span
                class="font-bold text-[16px] text-[#FFFFFF] w-[30px] h-[22px] bg-[rgba(0,0,0,0.16)] rounded-[3px] flex items-center justify-center"
              >100</span>
              <span class="text-[12px] text-[#FFFFFF]">份</span>
            </div>
          </div>
          <div class="flex items-center mt-[4px]">
            <div class="text-[14px] text-[#FFFFFF] mr-[4px]">
              福利倒计时
            </div>
            <div class="flex items-center ml-[4px]">
              <div
                class="w-[16px] h-[16px] bg-[#FFFFFF] rounded-[2px] font-bold text-[11px] text-[#FF8A36] flex items-center justify-center"
              >
                {{ minutes }}
              </div>
              <div class="mx-[4px] text-[11px] text-[#FFFFFF]">
                :
              </div>
              <div
                class="w-[16px] h-[16px] bg-[#FFFFFF] rounded-[2px] font-bold text-[11px] text-[#FF8A36] flex items-center justify-center"
              >
                {{ seconds }}
              </div>
            </div>
          </div>
        </div>
        <div class="flex justify-center mt-[299px]">
          <div class="relative flex items-center justify-center">
            <div
              class="text-[20px] text-[#666666] [text-decoration-line:line-through] flex items-center"
            >
              原价
              <div class="ml-[4px]">
                ¥{{ serviceInfo.originalPrice | amountFilter }}
              </div>
            </div>
            <img
              alt=""
              class="w-[22px] h-[22px] block mx-[5px]"
              src="@/components/submit-discount-popup-new/img/Frame1321315294.png"
            >
            <div class="font-bold text-[28px] text-[#F34747]">
              ¥{{ serviceInfo.servicePrice | amountFilter }}
            </div>
          </div>
        </div>
        <img
          alt=""
          class="w-[262px] block mx-auto mt-[15px]"
          mode="widthFix"
          src="@/components/submit-discount-popup-new/img/9SqIaj.png"
          @click="toPay"
        >
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import countDownMixin from "@/pages/rapid-consultation-confirm-order/my-consultation/js/countDownMixin";
import { amountFilter } from "@/libs/filter";
import { toConfirmOrder, whetherToLogIn } from "@/libs/tools";
import { getParalegalDataId, saveParalegalData } from "@/libs/paralegalData";
import store from "@/store";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";

export default {
  name: "LoginNotHaveOrder",
  components: { AppPopup },
  mixins: [countDownMixin],
  props: {
    value: {
      type: Boolean,
      default: false
    },
    serviceInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // https://lanhuapp.com/web/#/item/project/product?pid=f6f99c40-727f-4ace-bb83-999c3573b39a&image_id=3070c203-299f-4733-9d6c-e65852f1de12&tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=08d26ac5-281b-4f95-bda2-d77c204877cf&docId=3070c203-299f-4733-9d6c-e65852f1de12&docType=axure&pageId=63ad3b87c02443c8812d45014bee547e&parentId=17f42a9b-78c2-4756-83b8-b166c0883792
      className: "",
      /** 是否显示弹窗内容 */
      showContent: false
    };
  },
  computed: {
    /** 弹窗显示 */
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    },
    discountPrice() {
      return amountFilter(
        this.serviceInfo.originalPrice - this.serviceInfo.servicePrice
      );
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
        }
      },
      immediate: true
    }
  },
  methods: {
    /** 图片加载完成后触发 */
    picOnLoad() {
      this.className = "animation-scale";
    },
    /** 动画图片加载完毕 */
    animationEnd() {
      setTimeout(() => {
        this.showContent = true;
      }, 2300);
    },
    close() {
      this.show = false;
    },
    toPay() {
      buryPointChannelBasics({
        code: "LAW_APPLET_INDEX_PAGE_V2_POP_UP_LEFT_DATA_NO_PAY_TO_PAY_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      /* 先判断是否登录 */
      whetherToLogIn(async () => {
        const params = {
          info: "用户未输入问题描述",
          typeLabel: "综合咨询",
          typeValue: 26,
          type: 3
        };

        await saveParalegalData(params);

        const payFn = () => {
          store.commit("payState/SET_NO_FAIL_CALLBACK");

          toConfirmOrder({
            serviceCode: this.serviceInfo.serviceCode,
            synHistoryId: getParalegalDataId()
          });
        };

        payFn();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@keyframes scale {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}

.animation-scale {
  animation: scale 0.3s linear forwards;
}
</style>
