<template>
  <div class="top-img position-relative">
    <img
      alt=""
      class="background-image"
      src="@/assets/imgs/common/pay-swiper/top-bg-img.gif"
    >
    <div>
      <img
        v-for="(item, index) in images"
        :key="index"
        :src="item"
        alt=""
        class="swiper-item"
        mode="widthFix"
      >
    </div>
  </div>
</template>

<script>
export default {
  name: "PayFailTopSwiper",
  computed: {
    images() {
      return [
        require("@/assets/imgs/common/pay-swiper/1.gif"),
        require("@/assets/imgs/common/pay-swiper/2.gif"),
        require("@/assets/imgs/common/pay-swiper/3.gif")
      ];
    }
  }
};
</script>

<style lang="scss" scoped>
.top-img {
  width: 311px;
  height: 220px;
  box-sizing: border-box;
  padding-top: 50px;
  z-index: 1;
}

.swiper-item {
  display: block;
  width: 311px;
}
</style>
