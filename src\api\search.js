import { requestInfo } from "@/libs/axios";
import { IS_DEV } from "@/libs/config";
import { getClickId } from "@/libs/tools";

/* 搜索词联想*/
export const commonWordLike = (data) =>
  requestInfo.post("/article/common/wordLike", data);

/* 热词推荐*/
export const commonRecommend = (data) =>
  requestInfo.post("/article/common/recommend", data);

/* 文章分词分页查询*/
export const commonPage = (data) =>
  requestInfo.post("/article/common/page", data);

/** 豆包ai引擎调用猜你想问（非流式调用） */
export const unStreamGuessYouWant = (data) =>
  requestInfo.post("/aiEngineAsk/db/unStream/guessYouWant", data);

/** 豆包ai引擎调用猜你想问（非流式调用） */
export const aliGuessYouWant = (data) =>
  requestInfo.post("/aiEngineAsk/ali/guessYouWant", data);

/** 腾讯投放关键词 */
export const gdtKeywordText = (data) =>
  requestInfo.post("/tencent/gdt/keywordText", data);

/** 请求关键词 */
export async function gdtKeywordFn() {
  const clickId = getClickId();

  // 保底数据，如果获取不到关键词，则返回默认值
  const defaultData = {
    data: {
      guideSentence:
        "您好，本地律师在线，24小时提供法律咨询，您想咨询什么问题？",
      guideType: 10,
    },
  };

  if (IS_DEV) {
    const deliveryKeywords = uni.getStorageSync("delivery_keywords");

    deliveryKeywords && (defaultData.data.keywordText = deliveryKeywords);
  }

  // 如果没有点击id说明不是从广告点击进入的页面，则直接返回默认值
  if (!clickId) return defaultData;

  async function fetchKeyword(attempt = 1) {
    try {
      const res = await gdtKeywordText({ clickId });
      const keywordText = res.data.keywordText;

      // 如果获取不到关键词，则隔2秒递归获取
      if (!keywordText) {
        if (attempt > 2) {
          // 设置最大尝试次数
          return defaultData;
        }

        await new Promise((resolve) => setTimeout(resolve, 2000));
        return await fetchKeyword(attempt + 1);
      }

      return res;
    } catch (error) {
      console.error("请求关键词失败:", error);
      if (attempt < 2) {
        await new Promise((resolve) => setTimeout(resolve, 2000));
        return await fetchKeyword(attempt + 1);
      } else {
        return defaultData;
      }
    }
  }

  return await fetchKeyword();
}
