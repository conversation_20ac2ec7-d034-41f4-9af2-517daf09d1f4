<template>
  <div
    class="header"
    @click="$emit('click', $event)"
  >
    <!--    <img-->
    <!--      alt=""-->
    <!--      class="background-image"-->
    <!--      src="@/pages/index/imgs/header-box.png"-->
    <!--    />-->
    <img
      :src="headerImg"
      alt=""
      class="img"
    >
    <img
      v-if="isVip"
      alt=""
      class="vip"
      src="@/pages/index/imgs/vip.png"
    >
  </div>
</template>

<script>
export default {
  name: "AppLawyerHeader",
  props: {
    headerImg: {
      type: String,
      default: "",
    },
    isVip: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  position: relative;
  width: 42px;
  height: 42px;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;

  .img {
    border-radius: 50%;
    width: 39.5px;
    height: 39.5px;
  }

  .vip {
    width: 12px;
    height: 12px;
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 11;
  }
}
</style>
