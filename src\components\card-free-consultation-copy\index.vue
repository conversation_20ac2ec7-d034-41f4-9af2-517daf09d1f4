<template>
  <div>
    <if t="hjls">
      <div class="card-box">
        <div class="card-box-right">
          <if t="hjls">
            累计{{ lawyerInfoData.fansNum }}位用户推荐
          </if>
          <if f="hjls">
            {{ lawyerInfoData.fansNum }}位好评用户推荐
          </if>
        </div>
        <div class="box-top flex">
          <div>
            <img
              :src="lawyerInfoData.imgUrl"
              alt=""
              class="box-top-img"
            >
            <p class="chat-status flex flex-vertically-centered">
              <i class="badge" />咨询中
            </p>
          </div>
          <div class="mg-l-12">
            <div class="flex items-center justify-between">
              <div>
                <div class="box-top-title flex flex-align-center">
                  {{ lawyerInfoData.realName }}
                  <img
                    v-if="lawyerInfoData.isVip === 1"
                    alt=""
                    class="mg-l-4 box-top-title-icon"
                    src="../../pages/submit-question/imgs/v-icon.png"
                  >
                  <if t="hjls">
                    <p class="text-[12px] font-normal  text-[#9F6310] ml-[4px]">
                      {{ lawyerInfoData.workCityName }}
                    </p>
                  </if>
                </div>
                <p class="box-top-subtitle">
                  <span>{{ lawyerInfoData.lawyerOffice }}</span>
                </p>
              </div>
              <if t="hjls">
                <div class="flex items-center text-[13px] text-[#F5BE41] font-bold">
                  <img
                    alt=""
                    class="w-[18px] h-[18px]"
                    src="@/pages/lawyer-im/assets/star-selected.png"
                  >
                  {{ lawyerInfoData.lawyerScore }}
                </div>
              </if>
            </div>
            <div class="flex flex-align-center box-top-service flex-shrink-0">
              <if f="hjls">
                <p>服务</p>
                <p class="mg-l-4 mr-[12px]">
                  <span class="font-semibold text-[#333333]">{{
                    lawyerInfoData.serviceNum
                  }}</span>人
                </p>
              </if>
              <p class="">
                执业
              </p>
              <p class="mg-l-4">
                <span class="font-semibold text-[#333333]">{{
                  lawyerInfoData.workTime
                }}</span>年
              </p>
              <if t="hjls">
                <p class="ml-[16px]">
                  已解决
                  <span class="font-semibold ml-[4px] text-[#333333]">{{
                    lawyerInfoData.serviceNum
                  }}</span>人的法律纠纷
                </p>
              </if>
              <if f="hjls">
                <p class="mg-l-12">
                  好评率
                </p>
                <p class="mg-l-4 font-semibold text-[#333333]">
                  100%
                </p>
              </if>
            </div>
            <p class="box-top-good flex van-ellipsis text-ellipsis">
              <if t="hjls">
                <p
                  v-for="(i,index) in workFields"
                  :key="index"
                  class="px-[5px] mr-[5px] last-of-type:mr-[0] pt-[2px] pb-[1px] bg-[#F5F5F7] rounded-[4px] border-[1px] border-solid border-[#EEEEEE] text-[12px] text-[#666666]"
                >
                  {{ i }}
                </p>
              </if>
              <if f="hjls">
                擅长：{{ workField }}
              </if>
            </p>
            <div class="flex items-center space-x-[5px]">
              <div
                v-for="(item, index) in lawyerTags"
                :key="index"
                class="h-[20px] bg-[#F5F5F7] rounded-[4px] border-[1px] border-solid border-[#EEEEEE] text-[12px] text-[#666666] px-[5px] flex items-center justify-center mt-[6px]"
              >
                {{ item }}
              </div>
            </div>
          </div>
        </div>
        <div class="box-bottom flex flex-space-between">
          <div class="box-bottom-left flex flex-align-center">
            <img
              alt=""
              class="mg-l-14 box-bottom-left-icon"
              src="../../pages/lawyer-im/assets/bottom-icon.png"
            >
            <span class="mg-l-8">严选保障 · 隐私保护· 专业解答</span>
          </div>
          <div
            class="box-bottom-right flex flex-align-center"
            @click="jump"
          >
            <span>律师主页</span>
            <img
              alt=""
              src="../../pages/lawyer-im/assets/system_icon_rightarrow_black.svg"
            >
          </div>
        </div>
      </div>
    </if>
    <if f="hjls">
      <div>
        <div
          class="w-[343px] h-[147px] box-border position-relative pt-[87px] mt-[17px]"
          @click="jump"
        >
          <img
            src="@/assets/imgs/lpvkjs.png"
            alt=""
            class="background-image"
          >
          <img
            :src="lawyerInfoData.imgUrl"
            alt=""
            class="w-[66px] h-[66px] bg-[#C4C4C4] rounded-full block absolute -top-[6px] left-[12px]"
          >
          <div class="absolute top-[12px] left-[90px]">
            <div class="font-bold text-[18px] text-[#3887F5]">
              {{ shortLawyerName(lawyerInfoData.realName) }}律师
            </div>
            <div class="flex items-center space-x-[6px] mt-[11px]">
              <div
                v-for="(item, index) in lawyerTagsNew"
                :key="index"
                class="h-[23px] bg-[#FFFFFF] rounded-[4px] flex items-center justify-center box-border px-[4px] py-[3px]"
              >
                <img
                  :src="item.icon"
                  alt=""
                  class="w-[14px] h-[14px] block mr-[2px]"
                >
                <div class="text-[12px] text-[#666666]">
                  {{ item.field }}
                </div>
              </div>
            </div>
          </div>
          <div class="flex items-center">
            <div
              class="w-[114px] h-[48px] flex justify-center items-center flex-col"
            >
              <div class="font-bold text-[18px] text-[#333333]">
                98%
              </div>
              <div class="text-[12px] text-[#666666] mt-[2px]">
                好评率
              </div>
            </div>
            <i class="w-[1px] h-[32px] bg-[#EEEEEE]" />
            <div
              class="w-[114px] h-[48px] flex justify-center items-center flex-col"
            >
              <div class="font-bold text-[18px] text-[#333333]">
                {{ lawyerInfoData.serviceNum }}
              </div>
              <div class="text-[12px] text-[#666666] mt-[2px]">
                服务人数
              </div>
            </div>
            <i class="w-[1px] h-[32px] bg-[#EEEEEE]" />
            <div
              class="w-[114px] h-[48px] flex justify-center items-center flex-col"
            >
              <div class="font-bold text-[18px] text-[#333333]">
                {{ lawyerInfoData.workTime }}
              </div>
              <div class="text-[12px] text-[#666666] mt-[2px]">
                执业年限
              </div>
            </div>
          </div>
        </div>
        <img
          alt=""
          class="w-[343px] h-[32px] block mt-[8px]"
          src="@/assets/imgs/n5umht.png"
        >
      </div>
    </if>
  </div>
</template>

<script>
import { shortLawyerName } from "@/libs/filter";

export default {
  name: "CardFreeConsultationCopy",
  props: {
    /*   customData: {
     type: Object,
     default: () => ({}),
     },*/
    lawyerInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      avtar: [],
    };
  },
  computed: {
    /*    getData() {
     return this.customData?.customExts?.caseSourceV2
     ? this.customData?.customExts?.caseSourceV2
     : this.customData?.customExts
     },*/
    workField() {
      return this.workFields.join(" ");
    },
    workFields(){
      return this.lawyerInfoData?.workField || [];
    },
    /** 律师相关从业信息 */
    lawyerInfoData() {
      return this.lawyerInfo?.info || {};
    },
    lawyerTags() {
      return this.lawyerInfo?.info?.lawyerTags?.split(",").slice(0, 3) || [];
    },
    lawyerTagsNew() {
      return (
        this.lawyerInfo?.info?.lawyerTags?.split(",").slice(0, 3) || []
      ).map((item, index) => {
        const icons = [
          require("@/assets/imgs/pjcdfb.png"),
          require("@/assets/imgs/qz75wn.png"),
          require("@/assets/imgs/4csmf4.png")
        ];

        return {
          icon: icons[index],
          field: item
        };
      });
    },
  },
  methods: {
    shortLawyerName,
    jump() {
      this.$router.push(
        "/pages/lawyer-home/index?id=" + this.lawyerInfoData.id
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.card-box {
  position: relative;
  width: 343px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  padding: 12px;
  box-sizing: border-box;

  &-right {
    position: absolute;
    height: 21px;
    background: #fff3e0;
    border-radius: 0 8px 0 8px;
    box-sizing: border-box;
    right: 0;
    top: 0;
    font-size: 12px;
    font-weight: 400;
    color: #bd934c;
    padding: 2px 8px;
  }
}

.box-top {
  &-img {
    width: 52px;
    height: 52px;
    border-radius: 9px 9px 9px 9px;
    object-fit: cover;
  }

  &-title {
    font-size: 16px;
    font-weight: bold;
    color: #333333;

    &-icon {
      display: block;
      width: 12px;
      height: 12px;
    }
  }

  &-subtitle {
    margin-top: 8px;
    font-size: 12px;
    font-weight: 400;
    color: #666666;
  }

  &-service {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    margin-top: 8px;
  }

  &-good {
    width: 254px;
    font-size: 12px;
    font-weight: 400;
    color: #666666;
    margin-top: 6px;
  }
}
.chat-status{
  font-size: 12px;
  font-weight: 400;
  color: #22BF7E;
  padding-top: 4px;
  text-align: center;
  .badge{
    width: 5px;
    height: 5px;
    margin-right: 2px;
    background: #22BF7E;
    border-radius: 50%;
  }
}

.box-bottom {
  box-sizing: border-box;
  padding-top: 12px;
  border-top: 1px solid #eee;
  margin-top: 12px;

  &-left {
    width: 236px;
    height: 36px;
    background: linear-gradient(90deg, #f2f6ff 0%, #ffffff 100%, #ffffff 100%);
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    box-sizing: border-box;
    font-size: 12px;
    font-weight: 400;
    color: #2485ff;

    &-icon {
      width: 18px;
      height: 18px;
    }
  }

  &-right {
    font-size: 13px;
    font-weight: 400;
    color: #333333;

    image {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
