<!-- 数字滚动效果 -->

<template>
  <!-- 数字滚动 -->
  <view
    id="digitalDom"
    class="digital-scroll"
    :style="style"
  >
    <view
      v-for="(item, index) in digitalData"
      :key="index"
      :class="{ 'digital': true, 'digital-str': isNaN(item.num) }"
    >
      <!-- 符号显示 -->
      <view
        v-if="isNaN(item.num)"
        class="str-text"
      >
        {{ item.num }}
      </view>
      <!-- 滚动的列表 -->
      <view
        v-else
        class="scroll-num"
      >
        <view
          v-for="n in 10"
          :key="n"
          class="tra-num"
          :style="item.style"
        >
          {{ n }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { addStyle } from "@/uview-ui/libs/function";
import { pxToRpx } from "@/libs/tools";

export default {
  name: "GcmDigitalScroll",
  props: {
    // 数值
    value: {
      type: [Number, String],
      default: "8.88"
    },

    // 字体大小
    size: {
      type: [Number, String],
      default: "14"
    },

    // 字体颜色
    color: {
      type: String,
      default: "#333"
    },

    // 文字居中
    textAlign: {
      type: String,
      default: "center" // left, center, right
    }
  },

  data() {
    return {
      digitalData: []
    };
  },
  computed: {
    style() {
      return addStyle({
        fontSize: pxToRpx(this.size),
        color: this.color,
        justifyContent: this.textAlign === "center" ? "center" : this.textAlign === "right" ? "flex-end" : "flex-start"
      }, "string"); 
    }
  },

  watch: {
    value: {
      handler(val) {
        const digitalArr = String(val).split("");
        const dataList = [];
        digitalArr.forEach((num) => {
          const obj = {
            num: isNaN(num) ? num : Number(num),
            style: ""
          };
          dataList.push(obj);
        });
        this.digitalData = dataList;
        this.setScrollNum();
      },
      immediate: true
    }
  },
  methods: {
    // 滚动数字
    setScrollNum() {
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this);
        query
          .select("#digitalDom")
          .boundingClientRect((data) => {
            const defData = JSON.parse(JSON.stringify(this.digitalData));
            defData.forEach((item, index) => {
              // 设置移动距离
              item.style = `transform: translateY(-${item.num * data.height}px);`;
            });
            setTimeout(() => {
              this.digitalData = defData;
            }, 10);
          })
          .exec();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.digital-scroll {
	font-size: 68rpx;
	font-weight: bold;
	display: flex;
	align-items: center;

	--var-item-h: 1em;

	.digital {
		display: flex;
		justify-content: center;
		width: 0.6em; // 0.6em 是为了让文本间隔没那么宽
		height: var(--var-item-h);
		line-height: 1;
		overflow: hidden;

		.scroll-num {
			width: inherit;
			height: inherit;
			// 文本竖直排列
			// writing-mode: vertical-rl;
			// text-orientation: upright;

			.tra-num {
				width: inherit;
				height: inherit;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 1s;
			}
		}
	}

	.digital-str {
		width: auto;

		.str-text {
			width: inherit;
			height: inherit;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}
</style>
