<template>
  <lawyer-card-layout
    :data="data"
    :fromPage="fromPage1"
    :fromPage1="fromPage2"
    :imageConsultPoint="imageConsultPoint"
    :isShowFictitiousTime="isShowFictitiousTime"
    :phoneConsultPoint="phoneConsultPoint"
    :tabIndex="tabIndex"
    :theme="theme"
    :transformationPath="transformationPath"
    @handleClick="toFindLawyerDetail"
  />
</template>

<script>
import { toLawyerHome } from "@/libs/turnPages.js";
import { buryPointChannelBasics } from "@/api/burypoint.js";
import { BURY_POINT_CHANNEL_TYPE, FROM_PAGE, globalTransformationPath, POINT_CODE } from "@/enum/burypoint.js";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath.js";
import LawyerCardLayout from "@/components/lawyer-card-layout/index.vue";

export default {
  name: "FindlawyerItemPlus",
  components: { LawyerCardLayout },
  props: {
    tabIndex: {
      type: Number,
      default: null
    },
    data: {
      type: Object,
      default: () => ({})
    },
    theme: {
      type: String,
      default: "blue"
    },
    /** 图文咨询点击埋点 */
    imageConsultPoint: {
      type: String,
      default: POINT_CODE.LAW_APPLET_LAWYER_LIST_IMAGE_CONSULT_CLICK
    },
    /** 电话咨询点击埋点 */
    phoneConsultPoint: {
      type: String,
      default: POINT_CODE.LAW_APPLET_LAWYER_LIST_PHONE_CONSULT_CLICK
    },
    fromPage: {
      type: String,
      default: FROM_PAGE.ZLS_LSKP
    },
    fromPage1: {
      type: String,
      default: FROM_PAGE.ZLS_DHZX
    },
    fromPage2: {
      type: String,
      default: FROM_PAGE.ZLS_TWZX
    },
    /** 转化路径 */
    transformationPath: {
      type: Number,
      default: null
    },
    /** 是否显示假的回复时间 */
    isShowFictitiousTime: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    FROM_PAGE() {
      return FROM_PAGE;
    }
  },
  methods: {
    /** 跳转到律师主页 */
    toFindLawyerDetail(data) {
      buryPointTransformationPath.addDataSources({
        fromPage: this.fromPage
      });

      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_RECOMMEND_HEAD_CLICK,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      buryPointTransformationPath.new(
        globalTransformationPath.LAW_APPLET_FIND_LAWYER_HEAD_CLICK
      );
      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_FIND_LAWYER_HEAD_CLICK,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });
      toLawyerHome(data);
    }
  }
};
</script>

<style lang="scss" scoped></style>
