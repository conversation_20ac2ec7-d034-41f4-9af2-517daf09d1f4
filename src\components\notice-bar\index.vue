<template>
  <div
    v-if="state"
    :style="{backgroundColor:bgColor}"
    class="flex notice-bar flex-align-center"
  >
    <p
      :style="{color}"
      class="notice-bar-text"
    >
      {{ text }}
    </p>
    <u-icon
      v-if="mode==='closable'"
      :color="color"
      :size="iconSize"
      name="close"
      @click="$emit('handleClosable')"
    />
  </div>
</template>

<script>
export default {
  name: "NoticeBar",
  props: {
    iconSize: {
      type: String,
      default: "18"
    },
    text: {
      type: String,
      default: ""
    },
    bgColor: {
      type: String,
      default: "#EBF3FE"
    },
    color: {
      type: String,
      default: "#3887F5"
    },
    mode: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      state: true
    };
  },
};
</script>

<style lang="scss" scoped>
.notice-bar{
  padding: 8px 16px;
  .notice-bar-text{
    font-size: 14px;
  }
}
</style>
