<template>
  <div />
<!--  <van-popup  v-model="getShow"  position="center" :close-on-click-overlay="true" class="popup">
    <img class="top-img" src="@/assets/imgs/common/<EMAIL>" alt="">
    <div class="content">
      <p class="tittle">您对我们以下哪些服务内容不满意？</p>
      <div class="select-content">
        <div class="select" :class="{'active':activeIndex===index}" v-for="(i,index) in list" :key="i.value" @click="activeIndex=index">
          <span class="subscript"></span>
          <p>{{i.label}}</p>
          <textarea v-model="content" v-if="activeIndex===index&&i.type==='textarea'" placeholder="请将问题或意见填写在这里"></textarea>
        </div>
      </div>
      <div class="button" @click="handleSubmit">提交</div>
    </div>
    <img class="close-img" @click="closed" src="@/assets/imgs/common/<EMAIL>" alt="">
  </van-popup>-->
</template>

<script>
/*
import { Popup } from 'vant'
import {isNull} from "@/libs/basics-tools";
import {feedbackSave} from "@a";
import {pointType} from "@api/common";
export default {
  name: "index",
  components:{
    'van-popup': Popup,
  },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    getShow: {
      set(value){
        this.$emit('input', value)
      },
      get(){
        return this.value
      }
    }
  },
  data() {
    return {
      activeIndex:0,
      content:'',
      list: [{
        value:1,
        label:'A.没有我想要的服务？'
      },{
        value:2,
        label:'B.咨询价格较高？'
      },{
        value:3,
        label:'C.咨询流程较为繁琐？'
      },{
        value:4,
        label:'D.其他',
        type:'textarea'
      }]
    }
  },
  methods: {
    handleSubmit() {
      const  data = this.list[this.activeIndex]
      let content = data.label
      /!*判断是不是其他*!/
      if(data.type==='textarea'){
        if(isNull(this.content)){
          this.$toast('请把不满意告诉我们，我们将以最真诚的态度来解决您的诉求')
          return false
        }
        content = this.content
      }
      /!*提交意见返回*!/
      feedbackSave({
        type:2,
        content
      }).then(()=>{
        this.$toast('提交成功，谢谢您的反馈')
        this.closed()
      })
    },
    closed(){

      this.$emit('input',false)
    }
  },
  watch: {
    getShow(newValue) {
      if(!newValue){
        Object.assign(this.$data, this.$options.data())
      }else{
        pointType({
          code: 'REGISTER_CHANNEL_PAGE_VISIT',
          point:40
        })
      }
    }
  },
}
*/
</script>

<style lang="scss" scoped>
/*.popup{
  background: none;
  .top-img{
    height: 84px;
    width: 332px;
    display: block;
  }
  .content{
    margin-top: -18px;
    border-radius: 16px 16px 16px 16px;
    background: white;
    position: relative;
    z-index: 1;
    width: 332px;
    padding: 18px 0;
    .tittle{
      font-size: 15px;
      font-weight: bold;
      color: #323233;
      text-align: center;
      padding-bottom: 13px;
    }
    .select-content{
      padding: 0 42px;
      .select{
        padding-top: 12px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        &.active{
          .subscript{
            background: #F4CA6A;
            border: none;
          }
          p{
            font-size: 15px;
            font-weight: 400;
            color: #323232;
          }
        }
        textarea{
          box-sizing: border-box;
          width: 100%;
          height: 81px;
          padding: 12px 14px;
          background: #F8F8F8;
          border-radius: 6px 6px 6px 6px;
          resize: none;
          border: none;
          font-size: 12px;
          margin-top: 9px;
        }
        p{

          font-size: 15px;
          font-weight: 400;
          color: #797979;
          padding-left: 17px;
        }
        .subscript{
          display: block;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          box-sizing: border-box;
          border: 1px solid #989898;
        }
      }
    }
    .button{
      width: 271px;
      margin: 16px auto 0;
      text-align: center;
      line-height: 40px;
      background: linear-gradient(180deg, #FED47D 0%, #FBB756 100%);
      border-radius: 22px 22px 22px 22px;
      font-size: 17px;
      font-weight: normal;
      color: #47180B;
    }
  }
  .close-img{
    width: 35px;
    height: 35px;
    display: block;
    margin: 0 auto;
    padding-top: 22px;
  }
}*/
</style>
