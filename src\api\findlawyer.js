import { requestCommon } from "@/libs/axios.js";

/** 
 * 律师列表
 * https://showdoc.imlaw.cn/web/#/5/1862
 */
export const lawyerListV3 = (data) => requestCommon.post("/info/lawyer/findLawyer/pageList", data);

/** 找律师列表 */
export const findLawyerByCodePageList = (data) => requestCommon.post("/info/lawyer/findLawyerByCode/pageList", data);

/** 获取平台的付费服务列表 */
export const serviceManageByService = (data) => requestCommon.post("/info/serviceManage/byService", data);


/** 通过城市名称获取城市信息 */
export const currencyGetAddressByName = (data) => requestCommon.post("/core/currency/getAddressByName", data);


/** 所有律师案例列表 */
export const lawyerLawCasePageAll = (data) => requestCommon.post("/lawyer/lawyerLawCase/pageAll", data);

/** 首页优选律师功能 */
export const preferredLawyer = (data) => requestCommon.post("/info/lawyer/mainPage/new/preferredLawyer", data);
