<template>
  <div>
    <!-- #ifdef MP-WEIXIN -->
    <app-popup
      :closeOnClickOverlay="false"
      :show="show"
      showCancelButton
      mode="bottom"
      :zIndex="99999"
      bgColor="transparent"
      @cancel="handleCancel"
    >
      <div class="overflow-hidden rounded-t-[16px] bg-[#FFFFFF]">
        <div class="position-relative w-[375px] h-[269px] pt-[209px] box-border">
          <img
            class="background-image"
            alt=""
            src="@/pages/submit-question/lawsuit/success/img/Frame1321315892.png"
          >
          <div
            class="w-[343px] h-[44px] bg-[#07C160] rounded-[40px] flex items-center justify-center mx-auto"
            @click="handleClickSubscribe"
          >
            <div class="font-bold text-[16px] text-[#FFFFFF]">
              点击订阅
            </div>
          </div>
        </div>
        <u-safe-bottom />
      </div>
    </app-popup>
    <!-- #endif -->
  </div>
</template>

<script>
import { buryPointChannelBasics } from "@/api/burypoint";
import AppPopup from "@/components/app-components/app-popup/index.vue";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import { requestSubscribeMessage } from "@/libs/tools.js";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "ResultSubmitPopup",
  components: { USafeBottom, AppPopup },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    wechatTmplIdList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  methods: {
    handleClickSubscribe() {
      buryPointChannelBasics({
        code: "LAW_APPLET_SUIT_CASE_ADD_SUBSCRIBE_BUTTON_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
      });

      requestSubscribeMessage({
        wechatTmplIdList: this.wechatTmplIdList,
      }).then(() => {
        this.show = false;
      });
    },
    handleCancel() {
      this.show = false;
    },
  },
};
</script>
