<template>
  <div class="pd-tp-12 pd-rt-12 pd-lt-12">
    <div class="find-lawyer-home-type-container">
      <div class="title flex flex-align-center flex-space-between">
        <p class="title-text flex-1">
          按类型找律师
        </p>
        <div
          class="to-all flex flex-align-center"
          @click="goodAtTypeState = true"
        >
          全部类型
          <img
            alt=""
            class="arrow"
            src="../../../pages/index/imgs/arrow.png"
          >
        </div>
      </div>
      <div class="types pd-tp-12 flex flex-wrap flex-space-between">
        <div
          v-for="i in showList"
          :key="i.value"
          class="type flex flex-column flex-align-center flex-space-center"
          @click="handleCLick(i)"
        >
          <img
            :src="i.ext.icon"
            alt=""
            class="icon"
          >
          <p class="name">
            {{ i.label }}
          </p>
        </div>
      </div>
    </div>
    <app-popup
      :round="16"
      :show="goodAtTypeState"
      :zIndex="99999999"
      bgColor="transparent"
      mode="top"
      @close="goodAtTypeState = false"
    >
      <div class="goodat-box">
        <!--        <div class="select-button flex flex-space-between flex-align-center">-->
        <!--          <div class="select-button-placeholder" />-->
        <!--          <div class="button-title">-->
        <!--            全部类型-->
        <!--          </div>-->
        <!--          <div>-->
        <!--            <div-->
        <!--              class="button-cancel"-->
        <!--              @click="goodAtTypeState = false"-->
        <!--            >-->
        <!--              <img-->
        <!--                alt=""-->
        <!--                class="button-cancel-icon"-->
        <!--                src="@/pages/submit-question/imgs/cancellable.svg"-->
        <!--              >-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </div>-->
        <div class="lawyerTypes-box">
          <lawyer-types
            :list="list"
            @getCaseType="handleCLick"
          />
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import LawyerTypes from "@/components/findlawyer/find-lawyer-types/index.vue";

export default {
  name: "FindLawyerHomeType",
  components: { AppPopup, LawyerTypes },

  props: {
    list: {
      type: Array,
      default: () => {
        return [];
      },
      desc: "展示的列表"
    }
  },

  data() {
    return {
      goodAtTypeState: false
    };
  },
  computed: {
    showList() {
      return this.list.filter(item => item.ext.hot === 1);
    }
  },
  methods: {
    handleCLick({ value, label }) {
      let params = "";
      if (value) {
        params = `?typeLabel=${label}&typeValue=${value}`;
      }
      uni.navigateTo({
        url: `/pages/lawyer-home/find-lawyer/index${params}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.find-lawyer-home-type-container {
  padding: 12px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  .title {
    .title-text {
      font-size: 18px;
      font-weight: bold;
      color: #333333;
    }
    .to-all {
      font-size: 13px;
      font-weight: 400;
      color: #999999;
      .arrow {
        width: 16px;
        height: 16px;
      }
    }
  }
  .types {
    .type {
      width: 76px;
      height: 68px;
      .icon {
        width: 24px;
        height: 24px;
      }
      .name {
        font-size: 14px;
        font-weight: 400;
        color: #222222;
        padding-top: 8px;
      }
    }
  }
}

.goodat-box {
  background: #f5f5f7;
  border-radius: 0 0 16px 16px;
}
.lawyerTypes-box {
  border-radius: 0 0 16px 16px;
  padding-top: 30px;
}

.select-button {
  height: 48px;
  padding: 0 16px;
  border-bottom: 0.5px solid #eeeeee;

  &-placeholder {
    width: 24px;
  }

  .button {
    &-cancel {
      font-size: 14px;
      font-weight: 400;
      color: #999999;

      &-icon {
        display: block;
        width: 24px;
        height: 24px;
      }
    }

    &-title {
      font-size: 16px;
      font-weight: bold;
      color: #000000;
    }

    &-define {
      font-size: 14px;
      font-weight: 400;
      color: $theme-color;
    }
  }
}
</style>
