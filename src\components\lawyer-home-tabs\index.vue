<template>
  <div class="law-tabs">
    <p
      v-for="(item, index) in list"
      :class="{ 'law-tabs__item--active': activeIndex === index }"
      class="law-tabs__item"
      @click="handleClick(index)"
    >
      {{ item.name }}
    </p>
  </div>
</template>

<script>
export default {
  name: "LawyerHomeTabs",
  props: {
    /** 标签数组，元素为对象，如[{name: '推荐'}]   */
    list: {
      type: Array,
      default: () => [],
    },
    value: {
      type: Number,
      default: 0,
      required: true,
    },
  },
  watch: {
    activeIndex: {
      handler(val) {
        this.$emit("change", val);
      },
      immediate: true,
    },
  },
  computed: {
    /** 选中的标签 */
    activeIndex: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  methods: {
    /** 点击标签 */
    handleClick(index) {
      this.activeIndex = index;
      this.$emit("click", {
        index,
        item: this.list[index],
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.law-tabs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding: 2px;
  height: 34px;
  background: #f5f5f7;
  border-radius: 8px 8px 8px 8px;
  opacity: 1;

  &__item {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 13px;
    font-weight: 400;
    color: #666666;

    &--active {
      background: #ffffff;
      border-radius: 6px;
      opacity: 1;
      font-weight: bold;
      color: #333333;
    }
  }
}
</style>
