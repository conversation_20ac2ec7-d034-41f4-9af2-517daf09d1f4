<template>
  <div class="h-[15px] flex items-center justify-between px-[12px]">
    <swiper
      :autoplay="true"
      :disableTouch="true"
      :vertical="true"
      circular
      class="h-full w-full"
    >
      <swiper-item
        v-for="(item, index) in data"
        :key="index"
      >
        <div class="flex items-center">
          <div class="text-[11px] text-[#999999]">
            {{ item.time }}
          </div>
          <div class="text-[11px] text-[#666666] ml-[4px]">
            {{ item.content }}
          </div>
        </div>
      </swiper-item>
    </swiper>
    <div class="text-[11px] text-[#666666] shrink-0">
      今日律师已回复
      <if t="hjls">
        <span class="text-[#50B08C]">7215</span>
      </if>
      <if f="hjls">
        <span class="text-[#E4393C]">7215</span>
      </if>
      人
    </div>
  </div>
</template>

<script>

export default {
  name: "LoginSwiper",
  data() {
    return {
      data: [
        { time: "1分钟前", content: "吴律师刚刚联系了成都**5567用户" },
        { time: "2分钟前", content: "周律师刚刚联系了湖南**3556用户" },
        { time: "2分钟前", content: "李律师刚刚联系了广州**6489用户" },
        { time: "3分钟前", content: "段律师刚刚联系了吉林**2578用户" },
        { time: "4分钟前", content: "岳律师刚刚联系了甘肃**1845用户" },
        { time: "4分钟前", content: "付律师刚刚联系了湖北**7383用户" },
        { time: "5分钟前", content: "鲁律师刚刚联系了山西**2839用户" },
        { time: "6分钟前", content: "杨律师刚刚联系了西安**2899用户" },
        { time: "8分钟前", content: "肖律师刚刚联系了北京**9377用户" },
        { time: "9分钟前", content: "陈律师刚刚联系了上海**3192用户" },
      ]
    };
  }
};
</script>
