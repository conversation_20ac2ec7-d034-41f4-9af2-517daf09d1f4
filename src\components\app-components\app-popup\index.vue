<template>
  <div
    v-if="show"
    class="popup"
    @click.stop="handleStop"
  >
    <!-- 遮罩层 -->
    <div
      v-if="overlay"
      :style="{ zIndex: zIndex - 1 }"
      class="popup-marks"
      @click="handleClose"
      @touchmove.stop.prevent="handlePreventMove"
    />
    <div
      v-if="mode === 'center'"
      :class="classes"
      :style="centerStyle"
      class="popup-content-center"
    >
      <img
        v-if="showCancelButton"
        alt=""
        class="popup-content-center-cancel"
        src="@/components/app-components/app-popup/img/cancel-icon2.png"
        @click="handleCancel"
      >
      <img
        v-if="lowCancelButton"
        alt=""
        class="popup-content-center-cancel"
        src="@/components/app-components/app-popup/img/low-close.png"
        @click="handleCancel"
      >
      <slot />
    </div>
    <div
      v-if="mode === 'bottom'"
      :class="classes"
      :style="bottomStyle"
      class="popup-content-bottom"
    >
      <div
        v-if="enableSwipeCollapse"
        class="w-full h-[36px] flex items-center justify-center absolute top-0 left-0 z-10"
        @touchstart="handleTouchStart"
        @touchmove.stop="handleTouchMove"
        @touchend="handleTouchEnd"
      >
        <div class="w-[48px] h-[5px] bg-[#CCCCCC] rounded-[3px]" />
      </div>
      <img
        v-if="showCancelButton"
        alt=""
        class="absolute -top-[40px] right-[16px] block w-[24px] h-[24px] z-10"
        src="@/components/app-components/app-popup/img/cancel-icon2.png"
        @click="handleCancel"
      >
      <slot />
    </div>
    <div
      v-if="mode === 'top'"
      :class="classes"
      :style="topStyle"
      class="popup-content-top"
    >
      <slot />
    </div>
  </div>
</template>

<script>
import appPopupProps from "@/components/app-components/app-popup/appPopupProps.js";
import { addStyle } from "@/uview-ui/libs/function/index.js";
import { pxToScreenPx } from "@/libs/tools";

// 定义类名，通过给元素动态切换类名，赋予元素一定的css动画样式
const getClassNames = name => ({
  enter: `${name}-enter`,
  leave: `${name}-leave`
});

export default {
  name: "AppPopup",
  mixins: [appPopupProps],
  data() {
    return {
      visible: false,
      transform: "",
      /** 应用的类名 */
      classes: "",
      /** 键盘高度 */
      keyboardHeight: 0,
      /** 滑动相关状态 */
      touchStartY: 0,
      touchCurrentY: 0,
      isCollapsed: false,
      isDragging: false,
      dragOffset: 0,
    };
  },
  computed: {
    tabsHeight() {
      return this.$store.getters.getTabsHeight;
    },
    bottomStyle() {
      // 计算弹窗的transform值
      let translateY = 0;
      if (this.enableSwipeCollapse && this.mode === "bottom") {
        if (this.isCollapsed) {
          const systemInfo = uni.getSystemInfoSync();
          const safeAreaBottom = systemInfo.safeAreaInsets?.bottom || 0;
          const popupHeight = pxToScreenPx(this.collapseDistance) + safeAreaBottom;
          if (this.isDragging) {
            // 收缩状态下拖拽：基础收缩距离 + 拖拽偏移
            translateY = popupHeight + this.dragOffset;
          } else {
            // 收缩状态下静止：固定收缩距离
            translateY = popupHeight;
          }
        } else if (this.isDragging) {
          // 展开状态下拖拽：实时跟随手指
          translateY = Math.max(0, this.dragOffset);
        }
      }

      const baseStyle = {
        borderRadius: `${this.round}px ${this.round}px 0 0`,
        zIndex: this.zIndex,
        backgroundColor: this.bgColor,
        bottom: this.keyboardHeight ? this.keyboardHeight + "px" : 0,
        touchAction: this.enableSwipeCollapse ? "none" : "auto",
        ...this.popupStyle
      };

      // 添加transform
      if (translateY > 0) {
        baseStyle.transform = `translateY(${translateY}px)`;
        baseStyle.transition = this.isDragging ? "none" : "transform 0.3s ease-out";
      } else {
        baseStyle.transition = "transform 0.3s ease-out";
      }

      return addStyle(baseStyle, "string");
    },
    centerStyle() {
      const systemInfo = uni.getSystemInfoSync();

      const { windowHeight } = systemInfo;

      return addStyle(
        {
          borderRadius: `${this.round}px`,
          zIndex: Number(this.zIndex),
          // 这里时为了处理自定义底部栏的问题，让除开底部栏的高度后居中
          top: (windowHeight - this.tabsHeight) / 2 + "px",
          ...addStyle(this.popupStyle)
        },
        "string"
      );
    },
    topStyle() {
      const isAnimation = this.animation
        ? {}
        : {
          transform: "translate3d(0, 0, 0)"
        };

      return addStyle(
        {
          borderRadius: `${this.round * 2}rpx ${this.round * 2}rpx 0 0`,
          zIndex: this.zIndex,
          backgroundColor: this.bgColor,
          ...this.popupStyle,
          ...isAnimation
        },
        "string"
      );
    },
    /** 判断是哪个动画 */
    position() {
      if (this.mode === "center") {
        return "fade";
      }

      if (this.mode === "bottom") {
        return "slide-up";
      }
      if (this.mode === "top") {
        return "slide-down";
      }

      return "";
    }
  },
  watch: {
    show: {
      handler(val) {
        if (!this.animation) return;

        const classNames = getClassNames(this.position);

        const sysInfo = uni.getSystemInfoSync();

        const heightDiff = sysInfo.screenHeight - sysInfo.windowHeight;

        /** 监听键盘变化 */
        const listener = res => {
          console.log(res, "键盘高度变化");
          if (!this.listenKeyboard) return;

          const diff = res.height - heightDiff;

          this.keyboardHeight = diff > 0 ? diff : 0;
        };

        if (val) {
          // 监听键盘变化
          // #ifdef MP-WEIXIN
          uni?.onKeyboardHeightChange?.(listener);
          // #endif

          this.visible = true;
          // 重置滑动状态
          this.isCollapsed = this.enableSwipeCollapse ? this.initialCollapsed : false;
          this.isDragging = false;
          this.dragOffset = 0;

          // #ifdef MP-BAIDU
          // 这里不知道为什么，百度小程序不进下面的 this.$nextTick 所以做差异化处理
          this.classes = classNames.enter;
          // #endif
          this.$nextTick(() => {
            this.classes = classNames.enter;
            this.$emit("open");
          });
        } else {
          // 取消监听键盘变化
          // #ifdef MP-WEIXIN
          uni?.offKeyboardHeightChange?.(listener);
          // #endif

          this.classes = classNames.leave;

          setTimeout(() => {
            // this.$emit("close");
            this.visible = false;
          }, 300);
        }
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      if (this.closeOnClickOverlay) this.$emit("close");
    },
    /** 阻止后面的点击事件 */
    handleStop(e) {
      e.stopPropagation();
    },
    /** 点击右上角 X 按钮时触发 */
    handleCancel() {
      this.$emit("cancel");
    },

    /** 阻止默认的触摸移动事件，防止滚动穿透 */
    handlePreventMove(e) {
      // 只有在非拖拽状态下才阻止，拖拽状态下由具体的拖拽处理逻辑控制
      if (!this.isDragging) {
        e.preventDefault && e.preventDefault();
        e.stopPropagation && e.stopPropagation();
      }
    },

    /** 触摸开始 */
    handleTouchStart(e) {
      if (!this.enableSwipeCollapse || this.mode !== "bottom") return;

      this.touchStartY = e.touches[0].clientY;
      this.touchCurrentY = e.touches[0].clientY;
      this.isDragging = true;
      this.dragOffset = 0;

      // 阻止默认行为和事件冒泡
      e.preventDefault && e.preventDefault();
      e.stopPropagation && e.stopPropagation();
    },

    /** 触摸移动 */
    handleTouchMove(e) {
      if (!this.enableSwipeCollapse || this.mode !== "bottom" || !this.isDragging) return;

      this.touchCurrentY = e.touches[0].clientY;
      const deltaY = this.touchCurrentY - this.touchStartY;

      // 只响应向下的滑动（展开状态）或向上的滑动（收缩状态）
      if (this.isCollapsed) {
        // 收缩状态，只允许向上滑动（负值）来展开
        this.dragOffset = Math.min(0, deltaY);
      } else {
        // 展开状态，只允许向下滑动（正值）来收缩
        this.dragOffset = Math.max(0, deltaY);
      }

      // 强制阻止默认行为，防止页面滚动
      e.preventDefault && e.preventDefault();
      e.stopPropagation && e.stopPropagation();
    },

    /** 触摸结束 */
    handleTouchEnd(e) {
      if (!this.enableSwipeCollapse || this.mode !== "bottom" || !this.isDragging) return;

      this.isDragging = false;
      const deltaY = this.touchCurrentY - this.touchStartY;
      const threshold = 50; // 滑动阈值

      if (Math.abs(deltaY) > threshold) {
        if (deltaY > 0 && !this.isCollapsed) {
          // 向下滑动超过阈值，收缩弹窗
          this.collapsePopup();
        } else if (deltaY < 0 && this.isCollapsed) {
          // 向上滑动超过阈值，展开弹窗
          this.expandPopup();
        } else {
          // 未达到阈值，恢复原状
          this.resetDragOffset();
        }
      } else {
        // 未达到阈值，恢复原状
        this.resetDragOffset();
      }

      e.preventDefault && e.preventDefault();
      e.stopPropagation && e.stopPropagation();
    },

    /** 收缩弹窗 */
    collapsePopup() {
      this.isCollapsed = true;
      this.dragOffset = 0;
      this.$emit("collapse", true);
    },

    /** 展开弹窗 */
    expandPopup() {
      this.isCollapsed = false;
      this.dragOffset = 0;
      this.$emit("collapse", false);
    },

    /** 重置拖拽偏移 */
    resetDragOffset() {
      this.dragOffset = 0;
    }
  }
};
</script>

<style lang="scss" scoped>
.popup {
  z-index: 999;

  &-marks {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    /* 阻止触摸滚动穿透 */
    touch-action: none;
  }

  &-content {
    &-center {
      position: fixed;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 311px;
      z-index: 1000;
      transition: opacity 0.3s;
      /* 阻止触摸滚动穿透 */
      touch-action: none;

      &-cancel {
        position: absolute;
        top: -40px;
        right: 0;
        width: 24px;
        height: 24px;
      }
    }

    &-bottom {
      //background-color: #fff;
      position: fixed;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1000;
      transition: all 0.3s;
      /* 阻止触摸滚动穿透 */
      touch-action: none;

      &-cancel {
        position: absolute;
        top: -40px;
        right: 16px;
        width: 24px;
        height: 24px;
      }
    }

    &-top {
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
      z-index: 1000;
      transform: translate3d(0, -100%, 0);
      /* 阻止触摸滚动穿透 */
      touch-action: none;
    }
  }
}

.slide-up-enter {
  transform: translate3d(0, 0, 0);
}

.slide-up-leave {
  transform: translate3d(0, 100%, 0);
}

.fade-enter {
  opacity: 1;
}

.fade-leave {
  opacity: 0;
}

@keyframes down {
  0% {
    transform: translate3d(0, -100%, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}

.slide-down-enter {
  animation: down 0.3s linear forwards;
}

.slide-down-leave {
  transform: translate3d(0, 0, 0);
}
</style>
