<template>
  <login-layout>
    <search-input
      v-model="searchInput"
      @search="goResult"
    />
    <div class="px-[12px] mt-[12px]">
      <search-empty
        v-if="isArticleAndQaMessageSearchResult"
        @toAskLawyer="toAskLawyer"
      />
      <div
        v-if="lawyerListV3Data.total > 0"
        class="rounded-[8px] bg-[#FFFFFF] mt-[12px] overflow-hidden"
      >
        <div
          class="py-[12px] px-[12px] border-0 border-b-[0.5px] border-solid border-[#EEEEEE] flex items-center justify-between bg-[linear-gradient(180deg,_#FFF6E3_0%,_#FFFFFF_100%)]"
        >
          <div class="text-[18px] text-[#333333] font-bold flex items-center">
            <img
              alt=""
              class="w-[24px] h-[24px] block"
              src="@/pages/ask-details/search/img/j2QhFlK8.png"
            >
            <div class="ml-[4px]">
              推荐律师
            </div>
          </div>
          <div
            class="flex items-center"
            @click="
              toAllLawyer({
                typeValue,
                sort: 2
              })
            "
          >
            <div class="text-[13px] text-[#999999]">
              查看更多
            </div>
            <img
              alt=""
              class="w-[16px] h-[16px] block"
              src="@/pages/index/imgs/arrow.png"
            >
          </div>
        </div>
        <div
          v-for="item in lawyerListV3Data.records"
          :key="item.id"
          class="border-0 last:border-b-0 border-b-[0.5px] border-solid border-[#EEEEEE] px-[12px]"
          @click="clickLawyerBurialPoint"
        >
          <findlawyer-item-plus
            :data="item"
            imageConsultPoint="LAW_APPLET_SEARCH_RESULT_PAGE_GRAPHIC_CONSULT_CARD_CLICK"
            phoneConsultPoint="LAW_APPLET_SEARCH_RESULT_PAGE_PHONE_CONSULT_CARD_CLICK"
          />
        </div>
      </div>
      <div
        v-if="articleTotal > 0"
        class="rounded-[8px] bg-[#FFFFFF] mt-[12px] overflow-hidden"
      >
        <div
          class="py-[12px] px-[12px] border-0 border-b-[0.5px] border-solid border-[#EEEEEE] flex items-center justify-between"
        >
          <div class="text-[18px] text-[#333333] font-bold flex items-center">
            <img
              alt=""
              class="w-[24px] h-[24px] block"
              src="@/pages/ask-details/search/img/ZdzF2Vgo.png"
            >
            <div class="ml-[4px]">
              法律文章
            </div>
          </div>
          <div
            v-if="articleV2LawyerPageSearchData.total > 3"
            class="flex items-center"
            @click="toMoreArticle"
          >
            <div class="text-[13px] text-[#999999]">
              查看更多
            </div>
            <img
              alt=""
              class="w-[16px] h-[16px] block"
              src="@/pages/index/imgs/arrow.png"
            >
          </div>
        </div>
        <div
          v-for="item in articleV2LawyerPageSearchData.records"
          :key="item.id"
          class="border-0 [&:not(:last-child)]:border-b-[0.5px] border-solid border-[#EEEEEE]"
          @click="clickArticleBurialPoint"
        >
          <essay-item :data="item" />
        </div>
      </div>
      <div
        v-if="qaMessageTotal > 0"
        class="rounded-[8px] bg-[#FFFFFF] mt-[12px] overflow-hidden"
      >
        <div
          class="py-[12px] px-[12px] border-0 border-b-[0.5px] border-solid border-[#EEEEEE] flex items-center justify-between"
        >
          <div class="text-[18px] text-[#333333] font-bold flex items-center">
            <img
              alt=""
              class="w-[24px] h-[24px] block"
              src="@/pages/ask-details/search/img/RfZd3vIT.png"
            >
            <div class="ml-[4px]">
              优质问答
            </div>
          </div>
          <div
            v-if="qaMessage2cPageSearchData.total > 3"
            class="flex items-center"
            @click="toMoreAnswer"
          >
            <div class="text-[13px] text-[#999999]">
              查看更多
            </div>
            <img
              alt=""
              class="w-[16px] h-[16px] block"
              src="@/pages/index/imgs/arrow.png"
            >
          </div>
        </div>
        <div
          v-for="item in qaMessage2cPageSearchData.records"
          :key="item.id"
          class="border-0 [&:not(:last-child)]:border-b-[0.5px] border-solid border-[#EEEEEE]"
          @click="clickQaMessageBurialPoint"
        >
          <lawyer-card :data="item" />
        </div>
      </div>
      <img
        v-if="bannerUrl.imageUrl"
        :src="bannerUrl.imageUrl"
        alt=""
        class="w-[351px] mt-[12px]"
        mode="widthFix"
        @click="jumpToPage(bannerUrl.addressUrl)"
      >
      <u-safe-bottom />
    </div>
    <telephone-consultation-popup />
  </login-layout>
</template>

<script>
import LawyerCard from "@/pages/index/component/lawyerCard.vue";
import FindlawyerItemPlus from "@/components/findlawyer/findlawyer-item-plus/index.vue";
import EssayItem from "@/components/essay-item/index.vue";
import SearchEmpty from "@/pages/ask-details/search/components/SearchEmpty.vue";
import { articleV2LawyerPageSearch, qaMessage2cPageSearch } from "@/api/common";
import { lawyerListV3 } from "@/api/findlawyer";
import { consultationAliContent } from "@/api";
import searchData from "@/pages/ask-details/search/searchData";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { getLocation } from "@/libs/getLocation";
import {
  toAllLawyer,
  toAskLawyer,
  toMoreAnswer,
  toMoreArticle
} from "@/libs/turnPages";
import SearchInput from "@/components/SearchInput.vue";
import { advertListPosition } from "@/api/lawyer";

import LoginLayout from "@/components/login/login-layout.vue";
import TelephoneConsultationPopup from "@/components/telephone-consultation-popup/index.vue";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";

export default {
  name: "SearchResult",
  components: {
    TelephoneConsultationPopup,
    LoginLayout,
    SearchInput,
    USafeBottom,
    SearchEmpty,
    EssayItem,
    FindlawyerItemPlus,
    LawyerCard
  },
  data() {
    return {
      /** 问答数据 */
      qaMessage2cPageSearchData: {},
      /** 文章数据 */
      articleV2LawyerPageSearchData: {},
      /** 律师数据 */
      lawyerListV3Data: [],
      typeValue: "",
      searchInput: searchData.searchText || "",
      /** 定位城市数据 */
      locationData: {},
      /** 广告位数据 */
      bannerUrl: {}
    };
  },
  computed: {
    /** 法律文章+一问多答搜索匹配到结果数<=0 */
    isArticleAndQaMessageSearchResult() {
      return this.articleTotal <= 0 && this.qaMessageTotal <= 0;
    },
    /** 法律文章数量 */
    articleTotal() {
      return this.articleV2LawyerPageSearchData?.records?.length || 0;
    },
    /** 一问多答数量 */
    qaMessageTotal() {
      return this.qaMessage2cPageSearchData?.records?.length || 0;
    }
  },
  mounted() {
    this.getData();
    this.getLocation();
    this.getBannerData();
  },
  methods: {
    toAllLawyer,
    toMoreArticle,
    toMoreAnswer,
    /** 点击搜索按钮 */
    goResult(value) {
      searchData.searchText = value;

      this.getData();
      this.getRecommendLawyer();
    },
    /** 请求数据 */
    getData() {
      const keyword = searchData.searchText;

      qaMessage2cPageSearch({
        keyword,
        currentPage: 1,
        pageSize: 3
      }).then(res => {
        this.qaMessage2cPageSearchData = res.data;
      });

      articleV2LawyerPageSearch({
        keyword,
        currentPage: 1,
        pageSize: 3
      }).then(res => {
        this.articleV2LawyerPageSearchData = res.data;
      });
    },
    /** 城市定位 */
    async getLocation() {
      try {
        const local = await getLocation();

        this.locationData = {
          cityCode: local.cityCode,
          provinceCode: local.provinceCode
        };

        this.getRecommendLawyer();
      } catch (e) {
        console.log(e);
        this.locationData = {};
        this.getRecommendLawyer();
      }
    },
    /** 获取推荐律师 */
    async getRecommendLawyer() {
      const keyword = searchData.searchText;

      const type = await consultationAliContent({
        description: keyword
      });
      const params = {
        /** 页码 */
        currentPage: 1,
        /** 每页条数 */
        pageSize: 3,
        specialityCode: type.data.typeValue,
        // 权重分排序
        sort: 1,
        ...this.locationData
      };

      this.typeValue = type.data.typeValue;

      lawyerListV3(params).then(res => {
        this.lawyerListV3Data = res.data;
      });
    },
    /** 获取广告位数据 */
    getBannerData() {
      advertListPosition({ positionId: 179 }).then(({ data }) => {
        this.bannerUrl = data?.[0]?.advertContents?.[0] || {};
      });
    },
    /** 点击跳转页面 */
    jumpToPage(addressUrl) {
      buryPointChannelBasics({
        code: "LAW_APPLET_SEARCH_RESULT_PAGE_BOTTOM_CARD_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });

      uni.navigateTo({
        url: addressUrl
      });
    },
    toAskLawyer() {
      console.log("toAskLawyer");

      toAskLawyer({
        typeValue: this.typeValue,
        question: searchData.searchText
      });
    },
    /** 点击文章埋点 */
    clickArticleBurialPoint() {
      buryPointChannelBasics({
        code: "LAW_APPLET_SEARCH_RESULT_PAGE_ARTICLE_CARD_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });
    },
    /** 点击问答埋点 */
    clickQaMessageBurialPoint() {
      buryPointChannelBasics({
        code: "LAW_APPLET_SEARCH_RESULT_PAGE_QA_MESSAGE_CARD_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });
    },
    /** 点击律师埋点 */
    clickLawyerBurialPoint() {
      buryPointChannelBasics({
        code: "LAW_APPLET_SEARCH_RESULT_PAGE_LAWYER_CARD_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });
    }
  }
};
</script>
