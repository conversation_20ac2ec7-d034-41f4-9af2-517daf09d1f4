<template>
  <div>
    <consult-bottom-popup
      :position="position"
      clickCode="LAW_APPLET_INDEX_BOTTOM_TIP_TO_CONSULT_CLICK"
      uvCode="LAW_APPLET_INDEX_BOTTOM_TIP_PAGE"
    />
    <!-- #ifdef MP-WEIXIN -->
    <div
      v-if="isLogin"
      :style="[containerStyle]"
      class="share-container"
    >
      <div
        v-if="showShare"
        class="share flex flex-space-between flex-align-center"
      >
        <div class="flex flex-align-center">
          <div>
            <u-icon
              class="close-icon"
              color="#fff"
              name="close"
              size="14"
              @click="iconClick"
            />
          </div>
          <p class="share-text">
            点此关注公众号，及时获取问题进度
          </p>
        </div>
        <div
          class="share-button"
          @click="follow"
        >
          去关注
        </div>
      </div>
      <u-safe-bottom v-if="showSafe" />
    </div>
    <!-- #endif -->
  </div>
</template>

<script>
import { toOfficialAccountMini } from "@/libs/turnPages.js";
import UIcon from "@/uview-ui/components/u-icon/u-icon.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint.js";
import { buryPointChannelBasics } from "@/api/burypoint.js";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath";
import { whetherToLogIn } from "@/libs/tools";
import { bindOnHook } from "@/libs/hooks";
import { getGzhInfo } from "@/api/index.js";
import ConsultBottomPopup from "@/components/submit-official-account-mini/ConsultBottomPopup.vue";

export default {
  name: "SubmitOfficialAccountMini",
  components: { ConsultBottomPopup, USafeBottom, UIcon },
  props: {
    position: {
      type: String,
      default: "bottom",
      validator(value) {
        return ["none", "bottom", "top"].includes(value);
      }
    },
    /** 是否显示安全距离 */
    showSafe: {
      type: Boolean,
      default: false
    },
    /** uv埋点 */
    uv: {
      type: String,
      default: "",
      required: true
    },
    /** 点击埋点 */
    clickUv: {
      type: String,
      default: "",
      required: true
    },
    /** 埋点路径 */
    path: {
      type: Number,
      default: 0,
      required: true
    }
  },
  data() {
    return {
      showShare: true
    };
  },
  computed: {
    isLogin() {
      return !!this.$store.getters["user/getToken"];
    },
    containerStyle() {
      let style = {};

      switch (this.position) {
      case "bottom":
        style = {
          position: "fixed",
          left: 0,
          right: 0,
          bottom: this.$store.getters.getPageBottom
        };
        break;

      case "top":
        style = {
          position: "fixed",
          left: 0,
          right: 0,
          top: 0
        };
        break;

      default:
        style = {};
      }

      return uni.$u.addStyle(style);
    }
  },
  mounted() {
    bindOnHook.call(this, "onShow", () => {
      this.init();
    });

    this.init();
  },
  methods: {
    init() {
      buryPointTransformationPath.add(this.path);

      buryPointChannelBasics({
        code: this.uv,
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
        type: 1
      });

      // #ifdef MP-WEIXIN
      getGzhInfo().then(({ data }) => {
        if (data.gzhStatus === 0) {
          this.showShare = true;
        }
        if (data.gzhStatus === 1) {
          this.showShare = false;
        }
      });
      // #endif
    },
    iconClick() {
      this.showShare = false;
      this.$emit("iconClick");
    },
    /** 点击关注 */
    follow() {
      buryPointChannelBasics({
        code: this.clickUv,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      whetherToLogIn(() => {
        toOfficialAccountMini();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.share-container {
  z-index: 9990;
}

.share {
  padding: 0 16px;
  width: 375px;
  height: 42px;
  opacity: 1;
  background: rgba(0, 0, 0, 0.6);
  box-sizing: border-box;
}

.share-text {
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
  margin-left: 12px;
}

.share-button {
  flex-shrink: 0;
  background: linear-gradient(180deg, #74d68f 0%, #49bf7f 100%);
  width: 67px;
  height: 26px;
  line-height: 26px;
  text-align: center;
  border-radius: 18px;
  margin-left: auto;
  font-size: 13px;
  font-weight: 400;
  color: #ffffff;
}
</style>
