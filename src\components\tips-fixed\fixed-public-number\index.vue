<template>
  <div>
    <!-- #ifdef MP-WEIXIN -->
    <div
      :style="[containerStyle]"
      class="share-container"
    >
      <div
        v-if="showShare"
        class="share flex flex-space-between flex-align-center"
      >
        <div class="flex flex-align-center">
          <div>
            <u-icon
              class="close-icon"
              color="#fff"
              name="close"
              size="14"
              @click="iconClick"
            />
          </div>
          <p class="share-text">
            关注公众号，及时接收最新回复消息
          </p>
        </div>
        <div
          class="share-button"
          @click="follow"
        >
          去关注
        </div>
      </div>
      <u-safe-bottom v-if="showSafe" />
    </div>
    <!-- #endif -->
  </div>
</template>

<script>
import { toFollowPublic, toFollowPublicPage } from "@/libs/turnPages.js";
import { getGzhInfo } from "@/api/index.js";
import UIcon from "@/uview-ui/components/u-icon/u-icon.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { BURY_POINT_CHANNEL_TYPE, POINT_CODE } from "@/enum/burypoint.js";
import { buryPointChannelBasics } from "@/api/burypoint.js";

export default {
  name: "FixedPublicNumber",
  components: { USafeBottom, UIcon },
  props: {
    position: {
      type: String,
      default: "bottom",
      validator(value) {
        return ["none", "bottom", "top"].includes(value);
      }
    },
    /** 是否显示安全距离 */
    showSafe: {
      type: Boolean,
      default: false
    },
    /** 是否跳转到公众号文章页 */
    isArticle: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showShare: false
    };
  },
  computed: {
    containerStyle() {
      let style = {};

      switch (this.position) {
      case "bottom":
        style = {
          position: "fixed",
          left: 0,
          right: 0,
          bottom: 0
        };
        break;

      case "top":
        style = {
          position: "fixed",
          left: 0,
          right: 0,
          top: 0
        };
        break;

      default:
        style = {};
      }

      return uni.$u.addStyle(style);
    }
  },
  mounted() {
    buryPointChannelBasics({
      code: "LAW_APPLET_GO_FOLLOW_PUBLIC_PAGE",
      behavior: BURY_POINT_CHANNEL_TYPE.CK,
      type: 1
    });

    // #ifdef MP-WEIXIN
    getGzhInfo().then(({ data }) => {
      if (data.gzhStatus === 0) {
        this.showShare = true;
      }
      if (data.gzhStatus === 1) {
        this.showShare = false;
      }
    });
    // #endif
  },
  methods: {
    iconClick() {
      this.showShare = false;
      this.$emit("iconClick");
    },
    /** 点击关注 */
    follow() {
      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_GO_FOLLOW_PUBLIC_BUTTON_CLICK,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      if (this.isArticle) {
        toFollowPublicPage();
        return;
      }

      toFollowPublic();
    }
  }
};
</script>

<style lang="scss" scoped>
.share-container {
  z-index: 9999;
}

.share {
  padding: 0 16px;
  width: 375px;
  height: 42px;
  opacity: 1;
  background: rgba(0, 0, 0, 0.6);
  box-sizing: border-box;
}

.share-text {
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
  margin-left: 12px;
}

.share-button {
  flex-shrink: 0;
  background: linear-gradient(180deg, #74d68f 0%, #49bf7f 100%);
  width: 67px;
  height: 26px;
  line-height: 26px;
  text-align: center;
  border-radius: 18px;
  margin-left: auto;
  font-size: 13px;
  font-weight: 400;
  color: #ffffff;
}
</style>
