const { upload } = require("swan-toolkit");
const getMiniConfig = require("./config");
const { writeFile } = require("fs");
const path = require("path");

async function uploadFile({ token, PROJECT_PATH, isDev, version }) {
  upload({
    projectPath: PROJECT_PATH,
    minSwanVersion: "3.800.2",
    releaseVersion: version,
    token,
    // game: true,
    base64: true,
    sourcemap: true,
  })
    .then((res) => {
      console.log(res);
      // 小程序本次发布版本的预览二维码图片的 base64 值
      const schemeUrlBase64 = res.schemeUrlBase64;

      const base64Data = schemeUrlBase64.replace(/^data:image\/png;base64,/, "");

      const picName = `百度${isDev ? "测试" : "生产"}二维码.png`;

      // 将 schemeUrlBase64 转化作为图片保存到./img
      writeFile(
        path.resolve(__dirname, `../img/${picName}`),
        base64Data,
        "base64",
        function (err) {
          if (err) {
            console.log(err);
          }
        }
      );
    })
    .catch((err) => {
      console.log(err);
    });
}

module.exports = ({ FILE_NAME, PROJECT_PATH, isDev, version }) => {
  uploadFile({
    PROJECT_PATH,
    isDev,
    ...getMiniConfig[FILE_NAME],
    version
  });
};
