<template>
  <div class="bg-[#FFFFFF] rounded-[12px] px-[16px] py-[16px] mx-[12px]">
    <img
      class="block w-[163px] h-[34px] mx-auto"
      alt=""
      src="@/pages/submit-question/lawsuit/published/detail/img/biaoti.png"
    >
    <!-- 表格内容 -->
    <div class="border-[1px] border-[#EEEEEE] border-solid rounded-[8px] mt-[8px]">
      <div
        v-for="(item, index) in tableData"
        :key="index"
        class="flex border-b border-[#EEEEEE]"
      >
        <!-- 标签 -->
        <div
          class="w-[85px] text-[14px] text-[#666666] px-[14px] py-[10px] border-0 border-r-[1px] border-solid border-[#EEEEEE] box-border shrink-0 whitespace-nowrap"
          :class="[index === tableData.length - 1 ? 'border-b-0' : 'border-b-[1px]']"
        >
          {{ item.label }}
        </div>

        <!-- 值内容 -->
        <div class="flex-1">
          <!-- 附件类型 -->
          <div
            v-if="item.type === 'attachments'"
            class="px-[14px] py-[10px] border-0 border-b-[1px] border-solid border-[#EEEEEE]"
          >
            <div class="text-[14px] text-[#333333]">
              {{ item.value.text }}
            </div>

            <!-- 文件列表 -->
            <div
              v-for="(file, fileIndex) in item.value.files"
              :key="fileIndex"
              class="h-[44px] bg-[#FFFFFF] rounded-[6px] border-[1px] border-solid border-[#EEEEEE] box-border flex items-center py-[6px] pl-[12px] mt-[8px]"
            >
              <img
                class="w-[32px] h-[32px] mr-[8px] block"
                :src="getFileIcon(file.type)"
                alt=""
              >
              <div class="text-[14px] text-[#666666]">
                {{ file.name }}
              </div>
            </div>

            <!-- 图片列表 -->
            <div
              v-if="item.value.images.length > 0"
              class="flex flex-wrap items-center gap-x-[8px] mt-[8px] gap-y-[8px] overflow-x-auto "
            >
              <img
                v-for="(image, imageIndex) in item.value.images"
                :key="imageIndex"
                class="w-[44px] h-[44px] rounded-[8px]"
                :src="image"
                alt=""
              >
            </div>
          </div>

          <!-- 紧急程度类型 -->
          <div
            v-else-if="item.type === 'urgency'"
            class="rounded-[4px] text-[14px] px-[14px] py-[10px]"
          >
            <div class="bg-[#FCF1ED] rounded-[4px] text-[14px] text-[#EB4738] px-[6px] py-[2px] box-border inline-block">
              {{ item.value }}
            </div>
          </div>

          <!-- 默认文本显示 -->
          <div
            v-else
            class="text-[14px] text-[#333333] px-[14px] py-[10px] border-0 border-b-[1px] border-solid border-[#EEEEEE] break-all"
          >
            {{ formatValue(item.value, item.type) || "-" }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { dataDictionary } from "@/api";
import { amountFilter } from "@/libs/filter";

export default {
  name: "LawsuitDetailTable",
  props: {
    data: {
      type: Object,
      default: () => ({}),
      required: true,
    },
  },
  data() {
    return {
      caseProgressTypeList: {},
      urgencyLevelTypeList: {},
    };
  },
  computed: {
    tableData() {
      const latestSuitCase = this.data || {};

      return [
        {
          label: "案件类型",
          value: latestSuitCase.typeLabel,
        },
        {
          label: "所在地区",
          value: this.getLocationText(latestSuitCase),
        },
        {
          label: "涉案金额",
          value: latestSuitCase.amount || 0,
          type: "price",
        },
        {
          label: "案件进度",
          value: this.caseProgressTypeList[latestSuitCase.caseProgress],
        },
        {
          label: "补充资料",
          value: this.getAttachmentsData(latestSuitCase),
          type: "attachments",
        },
        {
          label: "预算费用",
          value: latestSuitCase.budget || 0,
          type: "price",
        },
        {
          label: "发布时间",
          value: latestSuitCase.createTime,
        },
        {
          label: "案件详情",
          value: latestSuitCase.info,
        },
        {
          label: "案件诉求",
          value: latestSuitCase.caseDemand,
        },
        {
          label: "紧急程度",
          value: this.urgencyLevelTypeList[latestSuitCase.urgencyLevel],
          type: "urgency",
        },
      ];
    },
  },
  mounted() {
    this.getTypeList();
  },
  methods: {
    getTypeList() {
      dataDictionary({ groupCode: "LAWSUIT_CASE_PROGRESS" }).then(({ data = {} }) => {
        const type = {};

        data.forEach(item => {
          type[Number(item.value)] = item.label;
        });

        this.caseProgressTypeList = type;
      });

      dataDictionary({ groupCode: "LAWSUIT_URGENCY_LEVEL" }).then(({ data = {} }) => {
        const type = {};

        data.forEach(item => {
          type[Number(item.value)] = item.label;
        });

        this.urgencyLevelTypeList = type;
      });
    },
    /**
     * 获取地区文本
     */
    getLocationText(suitCase) {
      if (!suitCase) return "";

      const province = suitCase.provinceName || "";
      const region = suitCase.regionName || "";
      return province && region ? `${province} ${region}` : (province || region || "");
    },
    /**
     * 获取附件数据
     */
    getAttachmentsData(suitCase) {
      if (!suitCase) {
        return {
          text: "无证据",
          files: [],
          images: []
        };
      }

      const hasEvidence = suitCase.evidence === 1;
      const files = [];
      const images = [];

      // 处理音频文件
      if (suitCase.addDataAudio) {
        const audioUrls = suitCase.addDataAudio.split(",").filter(url => url.trim());
        audioUrls.forEach((url, index) => {
          files.push({
            name: `音频${index + 1}.mp3`,
            type: "audio",
            url: url.trim()
          });
        });
      }

      // 处理视频文件
      if (suitCase.addDataVideo) {
        const videoUrls = suitCase.addDataVideo.split(",").filter(url => url.trim());
        videoUrls.forEach((url, index) => {
          files.push({
            name: `视频${index + 1}.mp4`,
            type: "video",
            url: url.trim()
          });
        });
      }

      // 处理图片
      if (suitCase.addDataPic) {
        const imageUrls = suitCase.addDataPic.split(",").filter(url => url.trim());
        images.push(...imageUrls.map(url => url.trim()));
      }

      return {
        text: hasEvidence ? "有证据" : "无证据",
        files,
        images
      };
    },

    /**
     * 格式化显示值
     */
    formatValue(value, type) {
      if (value === null || value === undefined) {
        return "";
      }

      switch (type) {
      case "price":
        // 后端传递的金额是以分为单位，需要转换为元
        return `¥${amountFilter(value)}`;
      default:
        return String(value);
      }
    },

    /**
     * 获取文件图标
     */
    getFileIcon(type) {
      const iconMap = {
        audio: require("@/pages/submit-question/lawsuit/published/detail/img/icon_yinping.png.png"),
        video: require("@/pages/submit-question/lawsuit/published/detail/img/icon_shiping.png.png"),
      };
      return iconMap[type] || iconMap.default;
    },
  },
};
</script>

<style lang="scss" scoped></style>
