<template>
  <div>
    <u-sticky :zIndex="9999">
      <div class="bg-[#FFFFFF] py-[8px]">
        <lawyer-search-input
          v-model="searchData"
          @search="handleSearch"
        />
      </div>
    </u-sticky>
    <div class="px-[12px]">
      <!--  没有数据     -->
      <div
        v-if="isArrNull(lawyerList) && isRequest"
        class="mt-[12px]"
      >
        <search-empty
          desc="平台精选专题律师为您提供多种解答"
          tips="没有找到想问的律师，试试直接问律师"
          @toAskLawyer="toAskLawyer"
        />
      </div>
      <div v-else>
        <div
          v-for="item in lawyerList"
          :key="item.id"
          class="mt-[12px] rounded-[8px] overflow-hidden bg-[#FFFFFF] px-[12px]"
        >
          <findlawyer-item-plus
            :data="item"
            imageConsultPoint="LAW_APPLET_SEARCH_RESULT_PAGE_GRAPHIC_CONSULT_CARD_CLICK"
            phoneConsultPoint="LAW_APPLET_SEARCH_RESULT_PAGE_PHONE_CONSULT_CARD_CLICK"
          />
        </div>
      </div>

      <!-- 推荐律师     -->
      <div
        v-if="!isArrNull(lawyerListV3Data)"
        class="rounded-[8px] bg-[#FFFFFF] mt-[12px] overflow-hidden"
      >
        <div
          class="py-[12px] px-[12px] border-0 border-b-[0.5px] border-solid border-[#EEEEEE] flex items-center justify-between bg-[linear-gradient(180deg,_#FFF6E3_0%,_#FFFFFF_100%)]"
        >
          <div class="text-[18px] text-[#333333] font-bold flex items-center">
            <img
              alt=""
              class="w-[24px] h-[24px] block"
              src="@/pages/ask-details/search/img/j2QhFlK8.png"
            >
            <div class="ml-[4px]">
              推荐律师
            </div>
          </div>
          <div
            class="flex items-center"
            @click="
              toAllLawyer({
                sort: 2
              })
            "
          >
            <div class="text-[13px] text-[#999999]">
              查看更多
            </div>
            <img
              alt=""
              class="w-[16px] h-[16px] block"
              src="@/pages/index/imgs/arrow.png"
            >
          </div>
        </div>
        <div
          v-for="item in lawyerListV3Data"
          :key="item.id"
          class="border-0 border-b-[0.5px] last:border-b-0 border-solid border-[#EEEEEE] px-[12px]"
          @click="clickLawyerBurialPoint"
        >
          <findlawyer-item-plus
            :data="item"
            imageConsultPoint="LAW_APPLET_SEARCH_RESULT_PAGE_GRAPHIC_CONSULT_CARD_CLICK"
            phoneConsultPoint="LAW_APPLET_SEARCH_RESULT_PAGE_PHONE_CONSULT_CARD_CLICK"
          />
        </div>
      </div>
    </div>
    <telephone-consultation-popup />
  </div>
</template>

<script>
import USticky from "@/uview-ui/components/u-sticky/u-sticky.vue";
import { getLocalLawyer } from "@/libs/tools.js";
import SearchEmpty from "@/pages/ask-details/search/components/SearchEmpty.vue";
import FindlawyerItemPlus from "@/components/findlawyer/findlawyer-item-plus/index.vue";
import { isArrNull } from "@/libs/basics-tools.js";
import { toAllLawyer, toAskLawyer } from "@/libs/turnPages.js";
import TelephoneConsultationPopup from "@/components/telephone-consultation-popup/index.vue";
import { lawyerListV3 } from "@/api/findlawyer.js";
import LawyerSearchInput from "@/components/LawyerSearchInput.vue";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";

export default {
  name: "LawyerSearch",
  components: {
    LawyerSearchInput,
    TelephoneConsultationPopup,
    FindlawyerItemPlus,
    SearchEmpty,
    USticky
  },
  data() {
    return {
      query: {
        currentPage: 1,
        pageSize: 20
      },
      /* 推荐律师列表*/
      lawyerListV3Data: [],
      isLastPage: false,
      /* 搜索律师列表 */
      lawyerList: [],
      /* 搜索参数*/
      searchData: {
        searchType: "lawyerName",
        searchText: ""
      },
      /** 是否请求过 */
      isRequest: false
    };
  },
  mounted() {
    buryPointChannelBasics({
      code: "LAW_APPLET_FIND_LAWYER_SEARCH_RESULT_PAGE",
      behavior: BURY_POINT_CHANNEL_TYPE.CK,
      type: 1
    });
    getLocalLawyer({
      currentPage: 1,
      pageSize: 3
    }).then(res => {
      this.lawyerListV3Data = res.data.records || [];
    });
    this.resetData();
    this.getData();
  },
  onReachBottom() {
    this.query.currentPage++;
    this.getData();
  },
  methods: {
    toAskLawyer,
    toAllLawyer,
    isArrNull,
    /** 请求数据 */
    getData() {
      const keyword = this.searchData.searchText;

      if (this.isLastPage) {
        return;
      }

      lawyerListV3({
        ...(this.searchData.searchType
          ? { [this.searchData.searchType]: keyword }
          : {}),
        ...this.query
      })
        .then(res => {
          this.lawyerList = [...this.lawyerList, ...res.data.records];

          if (this.lawyerList.length >= res.data.total) {
            this.isLastPage = true;
          }
        })
        .finally(() => {
          this.isRequest = true;
        });
    },
    /** 点击律师埋点 */
    clickLawyerBurialPoint() {},
    /** 重置数据 */
    resetData() {
      this.lawyerList = [];
      this.query.currentPage = 1;
      this.isLastPage = false;
    },
    handleSearch() {
      this.resetData();
      this.getData();
    }
  }
};
</script>
