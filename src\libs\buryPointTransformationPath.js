import store from "@/store/index.js";

class BuryPointTransformationPath {
  /**
   * 添加数据来源
   * @param fromPage
   */
  addDataSources({ fromPage }) {
    this._setDataSources({ fromPage });
  }

  /**
   * 设置数据来源
   * @param fromPage
   * @private
   */
  _setDataSources({ fromPage }) {
    uni.setStorageSync("fromPage", fromPage);
  }

  /**
   * 获取数据来源
   * @param clear 是否清除数据来源
   * @return {any}
   */
  getDataSources({ clear = false } = {}) {
    if (clear) {
      this.clearDataSources();
    }

    return uni.getStorageSync("fromPage");
  }

  /** 清除数据来源 */
  clearDataSources() {
    uni.removeStorageSync("fromPage");
  }

  /**
   * 往转化路径链路列表中添加一个数据
   * @param {number} transformationPath
   */
  add(transformationPath) {
    this._setOneTransformationPath(transformationPath);
    this._returnTransformationPath(transformationPath);
  }

  /**
   * 单个转化路径，没有转化路径链路时使用
   * @param {number} transformationPath
   */
  one(transformationPath) {
    this.clear();
    this._setOneTransformationPath(transformationPath);
  }

  /**
   * 初始化转化路径链路，当前页面为转化路径链路源头的时候使用
   * @param {number} transformationPath
   */
  new(transformationPath) {
    this.clear();
    this.add(transformationPath);
  }

  /**
   * 清空转化路径链路列表
   * 因为该方法集成在了其它的方法中，所以一般情况下不会再外部表用
   */
  clear() {
    uni.removeStorageSync("transformationPathList");
    store.commit("SET_TRANSFORMATION_PATH_LIST", []);
  }

  /**
   * 获取转化路径列表
   */
  get() {
    // 从Vuex中获取转化路径列表，使用深拷贝
    const transformationPathListStore = JSON.parse(
      JSON.stringify(store.getters?.getTransformationPathList || [])
    );

    // 从缓存中获取转化路径列表
    const transformationPathListLocal = JSON.parse(
      uni.getStorageSync("transformationPathList") || "[]"
    );

    // 首先从Vuex中获取转化路径列表，如果获取到为空，则尝试从本地缓存中获取
    let transformationPathList =
      transformationPathListStore?.length > 0
        ? transformationPathListStore
        : transformationPathListLocal;

    if (!Array.isArray(transformationPathList))
      transformationPathList = [transformationPathList];

    return transformationPathList;
  }

  /**
   * 设置单个转化路径，为了兼容老版本的埋点
   * @param {number} transformationPath
   * @private
   */
  _setOneTransformationPath(transformationPath) {
    store.commit("SET_TRANSFORMATION_PATH", transformationPath);
  }

  /**
   * 将转化路径添加到转化路径列表
   * @param {number} transformationPath
   * @private
   */
  _returnTransformationPath(transformationPath) {
    const transformationPathList = this.get();

    const index = transformationPathList?.indexOf(transformationPath);

    // 如果不存在，则添加 transformationPath 到 transformationPathList
    // 如果存在，则将transformationPath 后面的元素都删除掉
    if (index !== -1) {
      transformationPathList.length = index + 1;
    } else {
      transformationPathList.push(transformationPath);
    }

    this._setTransformationPathList(transformationPathList);
  }

  /**
   * 设置转化路径列表
   * @param transformationPathList
   * @private
   */
  _setTransformationPathList(transformationPathList) {
    const transformationPathListStr = JSON.stringify(transformationPathList);

    // 将转化路径列表存储到本地缓存，主要是为了应付页面刷新问题
    uni.setStorageSync("transformationPathList", transformationPathListStr);

    // 将转化路径列表存储到Vuex
    store.commit("SET_TRANSFORMATION_PATH_LIST", transformationPathList);
  }
}

/**
 * 埋点功能相关的集成对象
 * @version 2.1.6
 * <AUTHOR>
 * @Description 为了兼容老版本的埋点，并且新版本又增加了埋点链路概念，所以写了该埋点对象
 * @Date 2022/6/8
 * @FilePath libs\buryPointTransformationPath.js
 */
const buryPointTransformationPath = new BuryPointTransformationPath();

export default buryPointTransformationPath;
