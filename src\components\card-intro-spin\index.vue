<template>
  <div>
    <div
      v-if="spin"
      class="flex font14"
    >
      <div v-if="spinningText">
        {{ spinningText }}
      </div>
      <div class="flex">
        <div class="spinning-icon">
          .
        </div>
        <div class="spinning-icon">
          .
        </div>
        <div class="spinning-icon">
          .
        </div>
      </div>
    </div>
    <slot v-else />
  </div>
</template>

<script>
export default {
  name: "CardIntroSpin",
  props: {
    /** 是否为加载中状态 */
    spinning: {
      type: Boolean,
      default: false,
    },
    /** 加载中额外文字 */
    spinningText: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      spin: true,
    };
  },
  created() {
    this.spin = this.spinning;
  },
  mounted() {
    setTimeout(() => {
      this.spin = false;
    }, 2000);
  },
  methods: {
    /** 关闭加载中动画 */
    closeSpin() {
      this.spin = false;
    },
  },
};
</script>

<style lang="scss" scoped>

.spinning-icon {
  opacity: 0;

  &:nth-child(1) {
    margin-left: 0;
    animation: spin 1s linear infinite;
  }

  &:nth-child(2) {
    animation: spin 1s linear infinite;
    animation-delay: 0.33s;
  }

  &:nth-child(3) {
    animation: spin 1s linear infinite;
    animation-delay: 0.66s;
  }
}

@keyframes spin {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
</style>
