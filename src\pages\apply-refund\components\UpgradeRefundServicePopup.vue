<template>
  <div>
    <app-popup
      :show="show"
      :zIndex="9998"
      mode="center"
      showCancelButton
      @cancel="show = false"
    >
      <div
        class="w-[311px] h-[409px] box-border position-relative px-[16px] pt-[205px]"
      >
        <img
          alt=""
          class="background-image"
          src="../../apply-refund/after-sales/img/bg(1).png"
        >
        <img
          alt=""
          class="w-[279px] h-[113px] block"
          src="../../apply-refund/after-sales/img/Frame1321315901.png"
        >
        <div class="flex items-center mt-[20px]">
          <img
            alt=""
            class="w-[132px] h-[44px] block mr-[15px]"
            src="../../rapid-consultation-confirm-order/my-consultation/assets/Frame18417.png"
            @click="clickRefund"
          >
          <img
            alt=""
            class="w-[132px] h-[44px] block"
            src="../../rapid-consultation-confirm-order/my-consultation/assets/Frame8418.png"
            @click="clickUpgrade"
          >
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";

export default {
  name: "UpgradeRefundServicePopup",
  components: { AppPopup },
  props: {
    value: {
      type: Boolean,
      default: true,
      required: true
    }
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
  methods: {
    /** 点击退款按钮 */
    clickRefund() {
      this.$emit("refund");
    },
    /** 点击升级按钮 */
    clickUpgrade() {
      this.$emit("upgrade");
    }
  }
};
</script>
