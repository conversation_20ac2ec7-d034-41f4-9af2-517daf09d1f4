﻿const path = require("path");

/**
 * 这个key值对应的是 package.json 中的 FILE_NAME
 * 支持对象或数组格式，数组格式可以配置多个上传目标
 */
const getMiniConfig = {
  "mp-weixin-test": [
    {
      appid: "wxf2af270eb52577d7",
      privateKeyPath: path.resolve(
        __dirname,
        "./private.wxf2af270eb52577d7.key"
      ),
    },
    {
      appid: "wx6719a40e4eface31",
      privateKeyPath: path.resolve(
        __dirname,
        "./private.wx6719a40e4eface31.key"
      ),
    },
  ],
  "mp-weixin": [
    {
      appid: "wxf2af270eb52577d7",
      privateKeyPath: path.resolve(
        __dirname,
        "./private.wxf2af270eb52577d7.key"
      ),
    },
    {
      appid: "wx6719a40e4eface31",
      privateKeyPath: path.resolve(
        __dirname,
        "./private.wx6719a40e4eface31.key"
      ),
    },
  ],
  "mp-wx-hjls": {
    appid: "wxc61f5b76b5d211f5",
    privateKeyPath: path.resolve(__dirname, "./private.hjls.key"),
  },
  "mp-wx-hjls-test": {
    appid: "wxc61f5b76b5d211f5",
    privateKeyPath: path.resolve(__dirname, "./private.hjls.key"),
  },
};

module.exports = getMiniConfig;
