<template>
  <app-form-item-layout
    :label="label"
    :labelStyle="labelAddStyle"
    @click="handleClick"
  >
    <app-address-base
      v-model="data"
      :isDefaultCity="isDefaultCity"
      :visible.sync="show"
    />
  </app-form-item-layout>
</template>

<script>
import AppFormItemLayout from "@/components/app-components/app-form-list/app-form-item-layout.vue";
import AppAddressBase from "@/components/app-components/app-form-list/app-address/app-address-base.vue";

export default {
  name: "AppAddress",
  components: { AppAddressBase, AppFormItemLayout },
  props: {
    value: {
      type: [String, Object],
      default: "",
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
    /** 是否默认当前城市 */
    isDefaultCity: {
      type: Boolean,
      default: false,
    },
    labelStyle: {
      type: [Object, String],
      default: () => ({}),
    },
  },
  data() {
    return {
      show: false,
    };
  },
  computed: {
    data: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
    labelAddStyle(){
      return {
        ...uni.$u.addStyle(this.labelStyle),
        color: this.data.cityName ? "#333" : "#CCCCCC",
      };
    },
    label() {
      return (
        (this.data.provinceName || "") + (this.data.cityName || "") ||
        this.placeholder
      );
    },
  },
  methods: {
    handleClick() {
      this.show = true;
    },
  },
};
</script>
