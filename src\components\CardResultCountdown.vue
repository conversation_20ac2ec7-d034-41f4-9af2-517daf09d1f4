<template>
  <div class="countdown flex flex-align-center">
    <div
      :style="showStyle"
      class="minute"
    >
      {{ minute }}
    </div>
    <div class="mg-l-4 mg-r-4">
      :
    </div>
    <div
      :style="showStyle"
      class="second"
    >
      {{ second }}
    </div>
  </div>
</template>

<script>
import { addStyle } from "@/uview-ui/libs/function";

export default {
  name: "CardResultCountdown",
  props: {
    time: {
      type: Number,
      default: 0,
      required: true
    },
    boxStyle: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    minute() {
      // 补足两位数
      return Math.floor(this.time / 60)
        .toString()
        .padStart(2, "0");
    },
    second() {
      // 补足两位数
      return (this.time % 60).toString().padStart(2, "0");
    },
    showStyle() {
      return addStyle(this.boxStyle, "string");
    }
  }
};
</script>

<style lang="scss" scoped>
.countdown {
  font-size: 12px;
  font-weight: bold;
  color: #eb4738;
}

.minute,
.second {
  width: 24px;
  height: 24px;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 4px;
  opacity: 1;
  border: 0.5px solid #eb4738;
  text-align: center;
  line-height: 24px;
}
</style>
