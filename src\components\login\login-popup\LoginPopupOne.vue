<template>
  <div>
    <app-popup
      :closeOnClickOverlay="false"
      :duration="0"
      :round="0"
      :safeAreaInsetBottom="false"
      :show="show"
      :zIndex="zIndex"
      bgColor="transparent"
      mode="bottom"
      @open="open"
    >
      <div class="flex items-center pr-[16px] justify-end mb-[16px]">
        <img
          alt=""
          class="w-[24px] h-[24px] block"
          src="@/pages/submit-question/imgs/apK8Dj.png"
          @click="loginPopupClose"
        >
      </div>
      <div
        class="w-[375px] bg-[#FFFFFF] rounded-tl-[16px] rounded-br-none rounded-tr-[16px] rounded-bl-none py-[24px]"
      >
        <div class="font-bold text-[18px] text-[#333333] text-center">
          绑定手机号
        </div>
        <button
          class="w-[327px] h-[52px] bg-[#07C160] rounded-[8px] text-[16px] text-[#FFFFFF] flex items-center justify-center box-border mx-auto mt-[32px]"
          open-type="getPhoneNumber"
          @click="btnHandleClick(false, true)"
          @getphonenumber="getPhoneNumberHandler"
        >
          手机号快捷登录
        </button>
        <div
          class="text-[13px] text-[#999999] flex items-center justify-center mt-[16px]"
        >
          <img
            v-if="check"
            alt=""
            class="w-[16px] h-[16px] block"
            src="@/components/login/imgs/yQSE27.png"
            @click="check = false"
          >
          <img
            v-else
            alt=""
            class="w-[16px] h-[16px] block"
            src="@/components/login/imgs/O1JT6i.png"
            @click="check = true"
          >
          <span class="ml-[4px]">
            绑定手机号代表同意
          </span>
          <span
            class="text-[#07C160]"
            @click="protocolClick(1)"
          >
            《隐私政策》
          </span>
          <span
            class="text-[#07C160]"
            @click="protocolClick(2)"
          >
            《用户服务协议》
          </span>
        </div>
        <u-safe-bottom />
      </div>
    </app-popup>
  </div>
</template>
<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import LoginPopupMixin from "@/components/login/mixins/LoginPopupMixin";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "LoginPopupOne",
  components: { USafeBottom, AppPopup },
  mixins: [LoginPopupMixin]
};
</script>
