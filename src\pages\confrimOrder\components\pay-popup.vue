<template>
  <!-- <van-popup v-model="getShow" round position="bottom" close-on-popstate :close-on-click-overlay="true" class="popup-order pc-max-w pc-max-w-fixed-center"> -->
  <u-popup
    :show="getShow"
    @close="close"
    @open="open"
  >
    <view class="card-over">
      <p class="desc">
        送你一张首次咨询优惠券，立减{{
          (serverData.originalPrice - serverData.servicePrice) | amountFilter
        }}元
      </p>
      <view class="order-list">
        <view class="order-list-item">
          <p class="order-name">
            选择服务
          </p>
          <p class="order-desc">
            {{ serverData.serviceName }}
          </p>
        </view>

        <view class="order-list-item">
          <p class="order-name">
            服务单价
          </p>
          <p class="order-desc order-red">
            <span>¥</span>{{ serverData.originalPrice | amountFilter }}
          </p>
        </view>

        <view class="order-list-item">
          <p class="order-name">
            特惠返现
          </p>
          <p class="order-desc order-red">
            <span>-</span><span>¥</span>{{
              (serverData.originalPrice - serverData.servicePrice)
                | amountFilter
            }}
          </p>
        </view>
      </view>
      <view class="button">
        <ui-button @click.native="handle">
          <p class="button-text">
            <!-- 仅需支付 ¥{{ serverData.servicePrice | amountFilter }} <span>（优享{{ serviceTime }}咨询服务）</span> -->
            仅需支付 ¥{{ serverData.servicePrice | amountFilter }}
            <span>（即获得极速响应）</span>
          </p>
        </ui-button>
      </view>
      <p
        class="tip"
        @click="onClick"
      >
        <!-- 不需要此服务，去发起留言咨询 -->
        不需要此服务
      </p>
    </view>
  </u-popup>
  <!-- </van-popup> -->
</template>

<script>
// import { Popup } from 'vant'
import UiButton from "@/components/layout/button";
import {
  caseSourceV2Save,
  createOrder,
  getCommonConfigKey,
  orderPay,
  serviceManegeInfo
} from "@/api";
import { getServiceCasePaySetTime, isPc } from "@/libs/tools";
import { TRANSFORMATION_PATH } from "@/enum/burypoint";
import UPopup from "@/uview-ui/components/u-popup/u-popup.vue";

export default {
  name: "Index",
  components: {
    UPopup,
    // 'van-popup': Popup,
    UiButton
  },
  props: {
    callBack: {
      type: Function,
      default: () => true
    },
    value: {
      type: Boolean,
      default: false
    }
    // serviceTypeCode: {
    //   type: [String, Number],
    //   default: ''
    // }
  },
  data() {
    return {
      serviceTime: "", // 服务时间
      serviceCode: "",
      serverData: {}, // 服务信息
      serviceTypeCode: "" // 2.1.0更改为获取的serviceCode处理
    };
  },
  computed: {
    getShow: {
      set(value) {
        this.$emit("input", value);
      },
      get() {
        return this.value;
      }
    }
  },
  watch: {
    getShow(val) {
      /* 服务弹窗code获取*/
      val && this.initInfo();
    }
  },
  methods: {
    onClick() {
      this.$emit("input", false);
      this.$emit("onClick");
    },
    // 弹窗数据
    async initInfo() {
      // 获取服务时间
      getServiceCasePaySetTime().then(({ data }) => {
        this.serviceTime = data;
      });
      // law_pay_return_interceptor_default_service_code 支付返回时默认拦截弹窗服务
      // law_pay_case_source_quickly_default_service_code 案源加急付费平台服务Code
      getCommonConfigKey({
        paramName: "law_pay_return_interceptor_default_service_code"
      }).then(async ({ data }) => {
        if (data.paramValue) {
          this.serviceCode = data.paramValue;
          this.serviceTypeCode = data.paramValue;
          try {
            let res = await serviceManegeInfo(data.paramValue);
            if (res.data) {
              this.serverData = res.data;
            }
          } catch (error) {
            console.log(error);
          }
        }
      });
      // 页面访问
    },
    async handle() {
      const query = { serverCode: this.serviceCode, isDistributed: true };
      try {
        // 案源入库
        let resp = await caseSourceV2Save(query);

        if (resp) {
          // 创建订单
          // let respOrder =  await createOrder({ serviceCode: resp.data.serverCode, businessId: resp.data.id, pay_position: TRANSFORMATION_PATH[Number(this.serviceTypeCode)] })
          const params = {
            serviceCode: this.serviceCode,
            businessId: resp.data.id,
            pay_position: TRANSFORMATION_PATH[Number(this.serviceTypeCode)]
          };
          if (isPc()) {
            this.$emit("handlePcClick", this.serviceCode, resp.data.id, params);
            return false;
          }
          // 还是用查到的服务code不用保存后的服务code
          let respOrder = await createOrder(params);
          if (respOrder) {
            // 缓存案源id对应的订单id
            localStorage.setItem(resp.data.id, respOrder.data.orderId);
            // localStorage.setItem('popServiceTypeCode', JSON.stringify(this.serviceTypeCode))
            this.toPayWeixin(respOrder.data.orderId);
          }
        }
      } catch (error) {
        console.log(error);
      }
    },
    async toPayWeixin(orderId) {
      try {
        let params = {
          orderId,
          payType: 2
        };
        let resp = await orderPay(params);
        if (!resp.data.free) {
          window.location.href = JSON.parse(resp.data.payResultMsg).h5_url;
        } else {
          this.$toast("此订单免费！");
        }
      } catch (error) {
        console.log(error);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.popup-order {
  border-radius: 20px 20px 0 0;
}
.header {
  width: 101%;
  height: 108px;
  margin-left: -0.5px;
}
.card-over {
  overflow-x: hidden;
}
.desc {
  font-size: 16px;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: bold;
  color: #333333;
  line-height: 24px;
  text-align: center;
  width: 303px;
  padding: 32px 0;
  margin: 0 auto;
  span,
  label {
    color: #f2af30;
  }
}
.order-list {
  width: 303px;
  margin: 0 auto;
  padding-bottom: 12px;
  .order-list-item {
    display: flex;
    justify-content: space-between;
    padding-bottom: 16px;
    .order-name {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
    }
    .order-desc {
      font-size: 15px;
      font-weight: 400;
      color: #333333;
      span,
      label {
        padding-right: 6px;
      }
    }
    .order-red {
      color: #eb4738;
    }
  }
}
.button {
  width: 311px;
  height: 44px;
  margin: 0 auto;
  ::v-deep button {
    background: linear-gradient(90deg, #fa700d 0%, #eb4738 100%);
  }
}
.button-text {
  display: flex;
  align-items: center;
  font-size: 16px;
  label,
  span {
    font-size: 12px;
  }
}

.tip {
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  padding: 12px 0 20px;
  text-align: center;
}
</style>
