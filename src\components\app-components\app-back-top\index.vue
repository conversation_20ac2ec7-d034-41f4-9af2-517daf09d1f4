<template>
  <u-back-top
    :bottom="bottom"
    :duration="duration"
    :icon="icon"
    :iconStyle="iconStyle"
    :mode="mode"
    :right="right"
    :scrollTop="scrollTop"
    :text="text"
    :top="top"
    :zIndex="zIndex"
  >
    <slot>
      <img
        alt=""
        class="back-top"
        src="@/pages/lawyer-home/img/back-to-top.png"
      >
    </slot>
  </u-back-top>
</template>

<script>
import props from "@/uview-ui/components/u-back-top/props.js";
import UBackTop from "@/uview-ui/components/u-back-top/u-back-top.vue";

export default {
  name: "AppBackTop",
  components: { UBackTop },
  mixins: [props]
};
</script>

<style lang="scss" scoped>
.back-top {
  position: fixed;
  right: 10px;
  bottom: calc(135px + constant(safe-area-inset-bottom, 0));
  bottom: calc(135px + env(safe-area-inset-bottom, 0));
  width: 40px;
  height: 40px;
  border-radius: 50%;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.12);
}
</style>
