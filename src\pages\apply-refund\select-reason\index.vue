<template>
  <app-popup
    :round="16"
    :show="show"
  >
    <div class="select-reason">
      <!-- 标题部分 -->
      <div class="select-reason-title">
        <p
          class="cancel"
          @click="handleCancel"
        >
          取消
        </p>
        <p class="title">
          申请原因
        </p>
        <p
          class="confirm"
          @click="handleConfirm"
        >
          确定
        </p>
      </div>
      <div class="select-reason-body">
        <div
          v-for="item in reasonArr"
          :key="item.value"
          class="item flex flex-align-center"
          @click="selectFun(item)"
        >
          <if f="hjls">
            <img
              v-if="refundLabelId === item.value"
              alt=""
              class="icon"
              src="@/pages/apply-refund/assets/check.png"
            >
          </if>
          <if t="hjls">
            <img
              v-if="refundLabelId === item.value"
              alt=""
              class="icon"
              src="@/pages/lawyer-home/pay-lawyer-guide/img/22.png"
            >
          </if>
          <img
            v-if="!(refundLabelId === item.value)"
            alt=""
            class="icon"
            src="@/pages/apply-refund/assets/no-check.png"
          >
          <p class="msg font14">
            {{ item.label }}
          </p>
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import { dataDictionary } from "@/api";
import { refundTypeEnum } from "@/pages/apply-refund/js/refundInfo.js";

export default {
  name: "SelectReason",
  components: { AppPopup },
  props: {
    /** 控制弹窗的显示隐藏 */
    value: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      reasonArr: [],
      refundLabelId: "",
      reasonData: { refundLabelId: "" }
    };
  },
  computed: {
    show: {
      set(value) {
        this.$emit("input", value);
      },
      get() {
        return this.value;
      }
    }
  },
  watch: {
    type: {
      handler(val) {
        if(!val) return;

        let groupCode;

        if (val === refundTypeEnum.REFUND) {
          groupCode = "USER_REFUND_LABEL";
        } else {
          groupCode = "USER_CHANGE_LAWYER_REASON_LABEL";
        }

        dataDictionary({ groupCode }).then(data => {
          console.log("USER_REFUND_LABEL:", data);
          this.reasonArr = data.data;
        });
      },
      immediate: true
    }
  },
  async created() {},
  methods: {
    selectFun(item) {
      this.refundLabelId = item.value;
      this.reasonData = item;
    },
    /** 点击确定按钮 */
    handleConfirm() {
      this.$emit("input", false);
      this.$emit("reasonConfirm", this.reasonData);
    },
    /** 点击取消按钮 */
    handleCancel() {
      this.$emit("input", false);
    }
  }
};
</script>

<style lang="scss" scoped>
.select-reason {
  overflow: hidden;

  &-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 14px;

    .confirm {
      font-size: 14px;
      font-weight: 400;
      color: #3887f5;
    }

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #222222;
    }

    .cancel {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
    }
  }

  &-body {
    padding: 0px 0 20px 16px;
    border-top: 1px solid #ececec;

    .item {
      height: 44px;

      .icon {
        width: 16px;
        height: 16px;
        margin-right: 16px;
      }
    }

    .msg {
      color: #333;
      height: 44px;
      line-height: 44px;
    }
  }
}
</style>
