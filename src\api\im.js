import { requestCommon, requestCore } from "@/libs/axios";

// 获取置顶用户列表
export const imLoadTopImUser = data => requestCore.post("/im/loadTopImUser", data);

/* 置顶用户*/
export const imAddTopImUser = data => requestCore.post("/im/addTopImUser", data);

/* 取消置顶*/
export const imDelTopImUser = data => requestCore.post("/im/delTopImUser", data);

/* im敏感词监测*/
export const imCheckWorks = data => requestCore.post("/im/checkWorks", data);

/* 获取聊天页中所有订单的状态.*/
export const getBatchServiceOrderByIds = data => requestCommon.post("/order/getBatchServiceOrderByIds", data);

/** 同步问答接口 */
export const caseSourceV2HistorySynWd = (data) => requestCommon.post("/info/caseSourceV2/history/synWd", data);

/** im敏感词监测 */
export const wordsCheckDoCheck = (data) => requestCommon.post("/info/wordsCheck/doCheck", {
  strategyId: 17325,
  ...data
});
/** 保存历史咨询信息 */
export const caseSourceV2HistoryAddInfo = (data) => requestCommon.post("/info/caseSourceV2/history/addInfo", data);

/** 更新历史消息 */
export const caseSourceV2HistoryUpdateInfo = (data) => requestCommon.post("/info/caseSourceV2/history/updateInfo", data);

/** 获取历史咨询数据 */
export const caseSourceV2HistoryGetInfo = () => requestCommon.post("/info/caseSourceV2/history/getInfo");


/* 用户发送第一条消息出发信息.*/
export const caseSourceServerV2UserFirstMsgTime = (caseSourceServerV2Id) => requestCommon.post(`/info/caseSourceServerV2/userFirstMsgTime/${caseSourceServerV2Id}`);

/** 根据服务code 获取服务价格相关信息 */
export const infoPayServerCode = (data) => requestCommon.post("/info/serviceManege/info/payServerCode", data);

/* 真im里面通过服务id检验*/
export const wordsCheckByServiceId = (data) => requestCommon.post("/info/wordsCheck/byServiceId", data);

/* 获取律师已开启的一对一付费服务列表*/
export const serviceManegeGetLawyerAllServiceList = (data) => requestCommon.post("/info/serviceManege/getLawyerAllServiceList", data);

/** 问答采纳 客户端调用 */
export const qaMessageAcceptedQaMessageReply = (data) => requestCommon.post("/info/qaMessage/acceptedQaMessageReply", data);

/* 合同浏览下载量新增*/
export const lawyerContractOption = (data) => requestCommon.post("/info/lawyerContract/option", data);


/** im消息自定义关键词回复 */
export const userFirstMsgCheck = (data) => requestCommon.post("/info/caseSourceServerV2/userFirstMsgCheck", data);

/** im聊天创建 */
export const imCreate = (data) => requestCommon.post("/core/im/create", data);

/**
 * 用户端官司比价修改获取手机号状态
 * https://showdoc.imlaw.cn/web/#/5/3915
 */
export const lawsuitCaseUpdateServerPhone = (data) => requestCommon.post("/core/lawsuitCase/updateServerPhone", data);
