<template>
  <div class="steps">
    <template v-for="(item, index) in stepName">
      <span
        :key="item"
        class="flex flex-align-center item"
      >
        <span
          :class="[textClass(index)]"
          class="steps-text"
        >{{ item }}</span>
        <img
          v-if="index !== stepName.length - 1"
          :src="iconImage(index)"
          alt=""
          class="steps-image mg-l-4"
        >
      </span>
    </template>
  </div>
</template>

<script>
import doneArrow from "./assets/sysarrow.svg";
import undoneArrow from "./assets/system_icon_rightarrow_grey.svg";

export default {
  name: "MyLitigateStep",
  props: {
    stepName: {
      type: Array,
      required: true,
      default: () => [],
    },
    /** 当前步骤对应的索引值 */
    active: {
      type: Number,
      default: 0,
    },
  },
  methods: {
    /** 文字class */
    textClass(index = 0) {
      if (index < this.active) {
        return "steps-text--done";
      }
      return "";
    },
    /** icon图标 */
    iconImage(index = 0) {
      // if (index < this.active - 1) {
      if (index < this.active) {
        return doneArrow;
      }
      return undoneArrow;
    },
  },
};
</script>

<style lang="scss" scoped>
.steps {
  padding: 0 17px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  font-weight: 400;
  line-height: 14px;
  width: 100%;
  height: 37px;
  background: #EBF3FE;
  border-radius: 8px 8px 8px 8px;

  &-text {
    color: #666666;

    &--done {
      color: $theme-color;
    }
  }

  &-image {
    width: 12px;
    height: 12px;
  }
}
</style>
