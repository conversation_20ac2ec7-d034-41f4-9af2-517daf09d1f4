<template>
  <view class="pay-card">
    <view class="pay-card-top">
      <span
        v-show="!norefund"
        :class="['right-text', serviceData.type === 2 && 'right-text1']"
      >{{ filterText(serviceData.type) }}</span>
      <view class="flex">
        <!-- <img :src="serviceData.icon" alt="" class="dicon" /> -->
        <img
          alt=""
          class="dicon"
          src="../imgs/heigou.jpg"
        >
        <view class="flex-1 flex flex-column flex-space-between">
          <!-- <span class="titlename font16">{{ serviceData.serviceName  }}</span> -->
          <span class="titlename font16">静态静态</span>
          <view class="flex box">
            <view class="flex info">
              <!-- <view v-if="serviceData.serviceNum && serviceData.unitLabel" v-show="!norefund" class="sub-t font12"> -->
              <view class="sub-t font12">
                规格:
                {{ serviceData.serviceNum + serviceData.unitLabel }}
              </view>
              <!-- 赞赏 围观 -->
              <view
                v-show="norefund"
                class="sub-t font12"
              >
                x1
              </view>
              <view class="price flex-1 flex-space-end">
                <p
                  v-show="!norefund"
                  class="origin-price"
                >
                  原价
                  <!-- <span>¥{{ serviceData.originalPrice | amountFilter }}</span> -->
                  <span class="span">¥{{ 12000 | amountFilter }}</span>
                </p>
                <p class="servicePrice-price">
                  ¥{{ serviceData.servicePrice | amountFilter }}
                </p>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    serviceData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {};
  },
  computed: {
    // 是赞赏和围观 不能退款
    norefund() {
      return this.serviceData.type === 4 || this.serviceData.type === 5;
    },
  },
  methods: {
    filterText(val) {
      let obj = {
        1: "限时特惠",
        2: "极速服务",
        3: "限时优惠",
      };
      if (obj[val]) return obj[val];
      else return "限时优惠";
    },
  },
};
</script>

<style lang="scss" scoped>
.pay-card {
  background: #fff;
  box-sizing: border-box;
  font-size: 13px;
  border-radius: 10px 10px 10px 10px;
  border: 1px solid #f5f5f5;
  margin-left: 16px;
  margin-bottom: 12px;
  width: 343px;
  position: relative;
  .right-text {
    position: absolute;
    right: 0px;
    top: 0px;
    font-size: 12px;
    color: #fff;
    width: 82px;
    height: 34px;
    line-height: 24px;
    text-indent: 25px;
    background-image: url("@/assets/common/<EMAIL>");
    background-size: 100% 100%;
  }
  .right-text1 {
    background-image: url("@/assets/common/Subtract@2x(2).png");
    background-size: 100% 100%;
  }
  &-top {
    padding: 12px;
  }

  .dicon {
    width: 44px;
    height: 44px;
    margin-right: 12px;
    border-radius: 8px;
  }

  .titlename {
    font-weight: bold;
    color: #333333;
    line-height: 19px;
    margin-bottom: 0;
  }
  .box {
    width: 100%;
  }
  .info {
    width: 260px;
    justify-content: space-between;

    .sub-t {
      color: #999999;
      display: flex;
      align-items: flex-end;
    }

    .price {
      display: flex;
      align-items: center;

      .origin-price {
        color: #999999;
        font-size: 12px;
        margin-right: 4px;
        .span {
          text-decoration: line-through;
        }
      }

      .servicePrice-price {
        // color: #EB4738;
        // font-weight: bold;
        color: #333;
        font-size: 16px;
      }
    }
  }

  .sub-price {
    font-size: 16px;
    color: #333;
    width: 100%;
    text-align: right;
    // color: #EB4738;
    // font-weight: bold;
  }
}
</style>
