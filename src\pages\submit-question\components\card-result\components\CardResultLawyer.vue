<template>
  <div class="position-relative overflow-hidden bg-[#FFFFFF] rounded-[8px] -ml-[4px] w-[351px] box-border">
    <if f="hjls">
      <img
        alt=""
        class="w-[40px] h-[40px] block absolute left-0 top-0"
        src="@/pages/submit-question/components/card-result/img/3211.png"
      >
    </if>
    <if t="hjls">
      <img
        alt=""
        class="w-[40px] h-[40px] block absolute left-0 top-0"
        src="@/pages/submit-question/components/card-result/img/1123.png"
      >
    </if>
    <div
      class="pt-[16px] pb-[12px] px-[12px] flex items-center"
    >
      <div class="font-bold text-[16px] text-[#333333] flex items-baseline">
        推荐<span class="text-[#EB4738]">{{
          aiMatchData.category || resultData.typeLabel
        }}</span>专业律师
      </div>
    </div>
    <div class="px-[12px]">
      <swiper
        :autoplay="true"
        :interval="3000"
        circular
        class="h-[228px] w-full box-border"
        @change="change"
      >
        <swiper-item
          v-for="(itemList, index) in lawyerList"
          :key="index"
        >
          <div class="flex items-center justify-between">
            <div
              v-for="(item, itemIndex) in itemList"
              :key="itemIndex"
              class="w-[158px] h-[228px] rounded-8px position-relative flex-shrink-0"
              @click="pay"
            >
              <img
                :src="item"
                alt=""
                class="background-image"
              >
              <div class="text-[12px] text-[#CC6626] w-[148px] box-border absolute top-[100px] left-[10px]">
                <div>
                  擅长解决：
                </div>
                <div>
                  {{ aiMatchData.tag || resultData.typeLabel }}
                </div>
              </div>
              <div
                class="w-[138px] h-[30px] rounded-[68px] flex items-center pl-[6px] box-border font-bold text-[13px] text-[#FFFFFF] absolute absolute-center bottom-[10px]"
              >
                <img
                  alt=""
                  class="background-image"
                  src="@/pages/submit-question/components/card-result/img/pic/btn.png"
                >
                <div>¥{{ serviceInfo.servicePrice | amountFilter }}</div>
                <div class="ml-[4px]">
                  立即购买
                </div>
              </div>
            </div>
          </div>
        </swiper-item>
      </swiper>
    </div>
    <div class="w-full h-[34px] flex items-center justify-center">
      <app-indicator
        :list="lawyerList"
        :value="currentIndex"
        color="#F78C3E"
      />
    </div>
  </div>
</template>

<script>
import buryPointTransformationPath from "@/libs/buryPointTransformationPath";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import AppIndicator from "@/components/app-components/AppIndicator.vue";

import { payServiceCommon } from "@/libs/payServiceCommon";
import { getServicePositionPayInfo, POSITION_ENUM } from "@/libs/getPageInfo";

export default {
  name: "CardResultLawyer",
  components: { AppIndicator },
  props: {
    resultData: {
      type: Object,
      default: () => ({}),
      required: true
    },
    currentData: {
      type: Object,
      default: () => ({})
    },
    aiMatchData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      lawyerList: [
        [
          require("@/pages/submit-question/components/card-result/img/lawyer/1.png"),
          require("@/pages/submit-question/components/card-result/img/lawyer/2.png")
        ],
        [
          require("@/pages/submit-question/components/card-result/img/lawyer/3.png"),
          require("@/pages/submit-question/components/card-result/img/lawyer/4.png")
        ],
        [
          require("@/pages/submit-question/components/card-result/img/lawyer/5.png"),
          require("@/pages/submit-question/components/card-result/img/lawyer/6.png")
        ]
      ],
      currentIndex: 0,
      serviceInfo: {}
    };
  },
  mounted() {
    getServicePositionPayInfo({
      position: POSITION_ENUM.LAWYER_CARD
    }).then(data => {
      this.serviceInfo = data;
    });
  },
  methods: {
    async pay() {
      buryPointTransformationPath.add(1065);

      buryPointChannelBasics({
        code: "LAW_APPLET_RECOMMEND_RESULT_CONSULT_PAGE_LAWYER_CARD_BUY_CLICK",
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
        type: 1
      });

      buryPointChannelBasics({
        code: "LAW_APPLET_ASK_LAWYER_RESULT_PAGE_FAKE_LAWYER_CARD_CLICK",
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
        type: 1
      });

      payServiceCommon({
        serviceCode: this.serviceInfo.serviceCode
      });
    },
    change(e) {
      this.currentIndex = e.detail.current;
    }
  }
};
</script>
