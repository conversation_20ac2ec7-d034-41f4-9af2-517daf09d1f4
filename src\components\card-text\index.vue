<template>
  <submit-chatroom-layout
    :theme="theme"
    class="text-box"
  >
    <submit-spin
      :spinning="data.spinning"
      :asyncName="data.asyncName"
    >
      <div class="text">
        <p
          v-if="!data.asyncName"
          class="hh"
          v-html="data.msg"
        />
        <p
          v-else
          class="hh"
          v-html="asyncText"
        />
      </div>
    </submit-spin>
  </submit-chatroom-layout>
</template>

<script>
import SubmitSpin from "@/components/submit-spin/index.vue";
import SubmitChatroomLayout from "@/components/submit-chatroom-layout/index.vue";

export default {
  name: "CardText",
  components: {
    SubmitChatroomLayout,
    SubmitSpin,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    theme: {
      type: String,
      default: "white",
    },
    currentData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    asyncText() {
      if (this.data.asyncName) {
        return this.currentData.asyncResult?.[this.data.asyncName]?.text;
      }

      return "";
    }
  },
  mounted() {
    if (this.data.scroll) {
      setTimeout(() => {
        this.scrollToTop();
      }, 1000);
    }
  },
  methods: {
    /** 滚动到顶部 */
    scrollToTop() {
      const query = uni.createSelectorQuery?.().in(this).select(".text-box");
      query
        ?.boundingClientRect((res) => {
          if (res) {
            console.log(res.top, "res.top");
            uni.pageScrollTo({
              scrollTop: res.top,
              duration: 100,
            });
          }
        })
        .exec();
    },
  },
};
</script>

<style lang="scss" scoped>
.text {
  font-size: 14px;
  word-break: break-all;
}

.hh {
  word-break: break-all;
  white-space: pre-warp;
}
</style>
