export default {
  data() {
    return {
      statusBarHeight: uni.getSystemInfoSync().statusBarHeight
    };
  },
  computed: {
    navigationBarHeight() {
      const { height, top } = uni.getMenuButtonBoundingClientRect();

      console.log(
        this.statusBarHeight,
        top,
        height,
        "height, top, this.statusBarHeight"
      );

      return (top - this.statusBarHeight) * 2 + height;
    },
    placeholderHeight() {
      // #ifdef MP-WEIXIN
      return this.navigationBarHeight + this.statusBarHeight;
      // #endif
      // #ifndef MP-WEIXIN
      return 0;
      // #endif
    }
  }
};
