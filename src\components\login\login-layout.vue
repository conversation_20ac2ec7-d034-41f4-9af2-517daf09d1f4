<template>
  <div>
    <slot />
    <login-one-click
      :closeOnClickOverlay="closeOnClickOverlay"
      :needTransformationPath="needTransformationPath"
      :show="show"
      :showSafe="showSafe"
    />
    <app-custom-tab-bar />
  </div>
</template>

<script>
import LoginOneClick from "@/components/login/login-one-click.vue";
import AppCustomTabBar from "@/components/app-components/AppCustomTabBar/index.vue";

export default {
  name: "LoginLayout",
  components: { AppCustomTabBar, LoginOneClick },
  props: {
    needTransformationPath: {
      type: [Boolean, String],
      default: false
    },
    /** 点击遮罩是否关闭弹窗 */
    closeOnClickOverlay: {
      type: Boolean,
      default: true
    },
    /** 是否显示安全区域 */
    showSafe: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    show: {
      get() {
        return this.$store.getters["popup-state/getLoginPopupState"];
      },
      set(val) {
        this.$store.commit("popup-state/SET_LOGIN_POPUP_STATE", val);
      }
    }
  },
  watch: {
    show(val) {
      if (val) {
        uni.hideKeyboard();
      }
    }
  },
  destroyed() {
    this.show = false;
  }
};
</script>

<style scoped></style>
