<template>
  <div
    class="grid grid-rows-4 grid-flow-col grid-cols-2 gap-y-[16px] gap-x-[7px]"
  >
    <div
      v-for="(item, index) in dataSearch"
      :key="index"
      class="flex items-center"
      @click="handleClick(item)"
    >
      <div
        :class="[indexColor(index)]"
        class="text-[16px] px-[4px] shrink-0"
      >
        {{ index + 1 }}
      </div>
      <div class="ml-[4px] text-[14px] text-[#46474B] text-ellipsis">
        {{ item.remark }}
      </div>
      <img
        v-if="index < 3"
        alt=""
        class="w-[16px] h-[16px] block ml-[4px] shrink-0"
        src="@/pages/ask-details/img/fire.png"
      >
    </div>
  </div>
</template>

<script>
import { dataDictionary } from "@/api";

export default {
  name: "SearchTopic",
  props: {
    groupCode: {
      type: String,
      default: "C_SS_SSY_DJSS"
    }
  },
  data() {
    return {
      dataSearch: []
    };
  },
  watch: {
    groupCode: {
      handler(val) {
        if (val) this.getData();
      },
      immediate: true
    }
  },
  methods: {
    /** 序号颜色 */
    indexColor(index) {
      switch (index) {
      case 0:
        return "text-F34747";
      case 1:
        return "text-FF7A25";
      case 2:
        return "text-FFA700";
      default:
        return "text-[#999999]";
      }
    },
    /** 请求数据 */
    getData() {
      dataDictionary({
        groupCode: this.groupCode
      }).then(res => {
        this.dataSearch = res.data;
      });
    },
    handleClick(item) {
      this.$emit("goResult", item.remark);
    }
  }
};
</script>

<style lang="scss" scoped></style>
