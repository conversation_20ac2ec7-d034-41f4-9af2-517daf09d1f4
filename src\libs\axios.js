import CryptoJS from "crypto-js";
import { axiosBaseHeadersConfig } from "./config";
import { getUserTokenStorage } from "@/libs/token";
import store from "@/store/index.js";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath.js";
import { WebIM } from "@/plugins/im.js";

const UUID = uni.getSystemInfoSync().deviceId;
/* 请求参数*/
const requestConfig = {
  key: "bc37cc63-9d14-4013-8255-9168f24ceacd",
  secret: "imlaw1dmgbsj2cm9",
  iv: "cdkjytgsrj754921",
};

/* MD5加密*/
const getMD5 = (rawStr) => {
  // encrypt
  return CryptoJS.MD5(rawStr).toString();
};

/* 加密*/
export const cryptoEncrypt = (word) => {
  let key = requestConfig.secret;
  let iv = requestConfig.iv;

  key = CryptoJS.enc.Utf8.parse(key);
  iv = CryptoJS.enc.Utf8.parse(iv);

  /* let srcs = CryptoJS.enc.Utf8.parse(word);*/
  // 加密模式为CBC，补码方式为PKCS5Padding（也就是PKCS7）
  let encrypted = CryptoJS.AES.encrypt(word, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  // 返回base64
  return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
};

uni.addInterceptor("request", {
  invoke(config) {
    // console.log(config.url,config.data)
    Object.keys(axiosBaseHeadersConfig).forEach((key) => {
      config.header[key] = axiosBaseHeadersConfig[key];
    });
    config.header["deviceId"] = UUID;


    config.header["token"] = "";

    // app的头部信息注入
    // const appToken = getUserTokenStorage();
    // if (appToken) {
    // 	Object.keys(appToken).forEach((key) => {
    // 		if (appToken[key] && key != 'city') config.header[key] = appToken[key]
    // 	})
    // }
    let token = getUserTokenStorage() || store.getters["user/getToken"];
    if (token) {
      config.header["token"] = token;
    }
    const transformationPathList = buryPointTransformationPath.get();
    if(Array.isArray(transformationPathList) && transformationPathList.length > 0){
      config.header["transformationPathArray"] = transformationPathList.join(",");
    }
    config.header["transformationPath"] = store.getters["getTransformationPath"];
    // 埋点透传参数
    const buryPointQuery = uni.getStorageSync("buryPointQuery") || {};
    config.header["extra"] = JSON.stringify({
      ...buryPointQuery,
    });

    try {
      const channelId = uni.getStorageSync("channelId");
      if (channelId) {
        config.header["channelId"] = channelId;
      }

      const situationId = uni.getStorageSync("situationId");
      if(situationId) {
        config.header["situationId"] = situationId;
      }
    } catch (error) {}
    /* post 需要验签*/
    if (config.method === "post") {
      // console.log(config.url,config.data)
      config.header["Content-Type"] = "application/x-www-form-urlencoded";
      let stamp = new Date().getTime();
      const params = { ...(config.data || {}), stamp };
      // 数据+签名加密
      const base64 = params
        ? encodeURI(cryptoEncrypt(JSON.stringify({ ...params, stamp })))
        : "";
      const sign = getMD5(`${base64}${requestConfig.key}${stamp}`);
      config.header["sign"] = sign;
      if (params) {
        config.data = "data=" + base64;
      }
    }
  },
  success(args) {
    // 请求成功后，修改code值为1
  },
  fail(err) {
    console.log("interceptor-fail", err);
  },
  complete(res) {},
});

export const middlewareAxiosBacics = (baseUrl, method) => {
  return (url, params, header) => {
    return new Promise((resolve, reject) => {
      console.log(params, "============= " + url);
      return uni.request({
        url: baseUrl + url,
        data: params,
        header: {
          Accept: "application/json",
          "Content-Type": "application/json",
          "X-Requested-With": "XMLHttpRequest",
          ...header,
        },
        method: method,
        sslVerify: true,
        success: ({ data, statusCode, header }) => {
          console.log(data, "请求返回体", url);
          // console.log(url,params,data)
          if (data.code === 0) {
            resolve(data);
          } else if (data.code === 401) {
            store.dispatch("user/clearToken");
            store.commit("im/SET_IM_USER_INFO", {});
            WebIM.close();
            uni.showToast({
              title: data.message,
              duration: 2000,
              icon: "none",
              mask: true,
            });
            setTimeout(() => {
              uni.switchTab({ url: "/pages/mine/index" });
            }, 1000);
            reject(data);
          } else {
            uni.showToast({
              title: data.message,
              duration: 2000,
              icon: "none",
              mask: true,
            });
            reject(data);
          }
        },
        fail: (error) => {
          uni.showToast({
            title: "请检查网络",
            icon: "none",
            duration: 2000,
          });
          reject(error);
        },
      });
    });
  };
};

export const middlewareAxios = (baseUrl) => {
  return {
    post: middlewareAxiosBacics(baseUrl, "post"),
  };
};
export const requestInfo = middlewareAxios(
  process.env.VUE_APP_ENV_BASE_URL + process.env.VUE_APP_ENV_SEVER_ROUTE_INFO
);

export const requestCore = middlewareAxios(
  process.env.VUE_APP_ENV_BASE_URL + process.env.VUE_APP_ENV_SEVER_ROUTE_CORE
);

export const requestCms = middlewareAxios(
  process.env.VUE_APP_ENV_BASE_URL + process.env.VUE_APP_ENV_SEVER_ROUTE_CMS
);

export const requestLawyer = middlewareAxios(
  process.env.VUE_APP_ENV_BASE_URL + process.env.VUE_APP_ENV_SEVER_ROUTE_LAWYER
);

export const requestCommon = middlewareAxios(process.env.VUE_APP_ENV_BASE_URL);
