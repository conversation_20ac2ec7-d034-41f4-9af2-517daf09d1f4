<template>
  <div
    class="w-[351px] bg-[#FFFFFF] rounded-[8px] box-border mx-auto py-[12px]"
  >
    <div class="flex items-center mb-[12px] pl-[12px]">
      <div class="text-[18px] text-[#333333] leading-[21px] font-bold">
        精选服务
      </div>
      <div class="ml-[8px] text-[13px] text-[#999999]">
        一站式服务
      </div>
    </div>
    <div class="flex px-[8px] items-center">
      <img
        alt=""
        class="w-[164px] h-[138px] block"
        src="@/pages/rapid-consultation-confirm-order/mall/img/Frame1321315691.png"
        @click="toFeaturedAnswersPage"
      >
      <div class="ml-[8px]">
        <img
          alt=""
          class="w-[164px] h-[65px] block"
          src="@/pages/rapid-consultation-confirm-order/mall/img/Frame1321315789.png"
          @click="toHotTopic"
        >
        <img
          alt=""
          class="w-[164px] h-[65px] block mt-[8px]"
          src="@/pages/rapid-consultation-confirm-order/mall/img/pCyU6OF2.png"
          @click="toContractTemplatesPage"
        >
      </div>
    </div>
  </div>
</template>

<script>
import {
  toContractTemplatesPage,
  toFeaturedAnswersPage,
  toHotTopic
} from "@/libs/turnPages";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";

export default {
  name: "HomeHotTopic",
  methods: {
    toFeaturedAnswersPage() {
      buryPointChannelBasics({
        code: "LAW_APPLET_INDEX_PAGE_FEATURED_ANSWER_CLICK",
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
        type: 1
      });

      toFeaturedAnswersPage();
    },
    toHotTopic() {
      buryPointChannelBasics({
        code: "LAW_APPLET_CONSULT_TOPIC_PAGE",
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
        type: 1
      });
      toHotTopic();
    },
    toContractTemplatesPage() {
      buryPointChannelBasics({
        code: "LAW_APPLET_INDEX_PAGE_CONTRACT_DOWNLOAD_CLICK",
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
        type: 1
      });
      toContractTemplatesPage();
    }
  }
};
</script>
