<template>
  <app-popup
    :closeOnClickOverlay="true"
    :overlay="appOverlay"
    :round="16"
    :show="visible"
    mode="bottom"
    @close="clickOverlay"
  >
    <div class="container-popup">
      <div
        v-if="!hiddenSelectValue && showTitle"
        class="select-button flex flex-space-between flex-align-center"
      >
        <div class="select-button-placeholder" />
        <div class="button-title">
          {{ titleText }}
        </div>
        <div>
          <div
            v-show="cancellable"
            class="button-cancel"
            @click="clickCancel"
          >
            <img
              alt=""
              class="button-cancel-icon"
              src="@/pages/submit-question/imgs/cancellable.svg"
            >
          </div>
        </div>
      </div>
      <!-- 选项 -->
      <div
        v-if="!hiddenSelectValue"
        :class="{ 'select-list--vertical': layout === 'vertical' }"
        class="select-list"
      >
        <div
          v-for="item in dataSource"
          :key="item.value"
          :class="{
            'select-list-item--selected': item.value === selectValue.typeValue,
          }"
          class="select-list-item flex flex-align-center flex-space-center"
          @click="handleSelect(item)"
        >
          <span>{{ item.label }}</span>
        </div>
      </div>
      <div
        v-else
        class="click-button flex flex-align-center flex-space-center"
      >
        <app-button @click="buttonClick">
          请选择咨询类型
        </app-button>
      </div>
    </div>
    <u-safe-bottom />
  </app-popup>
</template>

<script>
import AppButton from "@/components/app-components/app-button/index.vue";
import props from "@/components/options-popup-select/props.js";
import AppPopup from "@/components/app-components/app-popup/index.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "OptionsPopupSelect",
  components: { USafeBottom, AppPopup, AppButton },
  mixins: [props],
  data() {
    return {
      /** 选中的值 */
      selectValue: this.value,
      /** 是否隐藏选择的内容 */
      hiddenSelectValue: false,
    };
  },
  computed: {
    appOverlay() {
      if (this.hiddenSelectValue) return false;

      return this.overlay;
    },
  },
  watch: {
    value(val) {
      this.selectValue = val;
    },
    /** 是否隐藏选择的内容事件 */
    hiddenSelectValue(val) {
      this.$emit("hiddenSelectValue", val);
    },
  },
  methods: {
    /** 控制弹窗显影 */
    setVisible(visible) {
      this.$emit("update:visible", visible);
    },
    /** 点击item时触发 */
    handleSelect(value) {
      this.selectValue = {
        typeLabel: value.label,
        typeValue: value.value,
      };

      this.clickOK();
    },
    /** 点击确定按钮 */
    clickOK() {
      this.$emit("input", this.selectValue);
      this.$emit("confirm", this.selectValue);
      this.setVisible(false);
    },
    /** 点击取消按钮 */
    clickCancel() {
      if (this.clickToSwitch) {
        this.hiddenSelectValue = true;
        return;
      }

      this.setVisible(false);
    },
    /** 关闭时重新设置一次值 */
    closed() {
      this.selectValue = this.value;
      this.setVisible(false);
    },
    /** 点击蒙层时触发 */
    clickOverlay() {
      if (this.clickToSwitch) {
        this.hiddenSelectValue = true;
      }
    },
    buttonClick() {
      if (this.clickToSwitch) this.hiddenSelectValue = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.select-button {
  height: 48px;
  padding: 0 16px;
  border-bottom: 0.5px solid #eeeeee;

  &-placeholder {
    width: 24px;
  }

  .button {
    &-cancel {
      font-size: 14px;
      font-weight: 400;
      color: #999999;

      &-icon {
        display: block;
        width: 24px;
        height: 24px;
      }
    }

    &-title {
      font-size: 16px;
      font-weight: bold;
      color: #000000;
    }

    &-define {
      font-size: 14px;
      font-weight: 400;
      color: $theme-color;
    }
  }
}

.select-list {
  padding: 0 20px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-column-gap: 24px;
  grid-row-gap: 20px;
  margin-top: 22px;
  margin-bottom: 12px;

  &--vertical {
    grid-template-columns: repeat(1, 180px);
    justify-content: center;
  }

  &-item {
    background: #f5f5f5;
    color: #333333;
    border-radius: 20px 20px 20px 20px;
    opacity: 1;
    font-size: 13px;
    font-weight: 400;
    height: 38px;

    &--selected {
      background: $theme-color;
      color: #ffffff;
    }
  }
}

.click-button {
  padding: 12px 0;
}
</style>
