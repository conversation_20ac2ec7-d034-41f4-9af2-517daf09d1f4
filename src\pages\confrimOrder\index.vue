<template>
  <view class="confirm-order">
    <NoticeListBar
      v-if="!norefund"
      class="notice-box"
      noticeIcon="noticeListBar-yellow"
    />
    <!-- </view> -->
    <view class="wrap">
      <view class="wrap-box">
        <PayCard :serviceData="serviceData" />
        <p :class="['title', 'font16']">
          支付方式
        </p>
        <view class="payway-box">
          <view
            v-for="(item, index) in payList"
            :key="index + 'payList'"
            class="payway flex flex-space-between flex-align-center"
            @click="selectPayWay(item)"
          >
            <view class="flex flex-align-center">
              <img
                :src="item.icon"
                alt=""
                class="wxpayicon"
              >
              <span>{{ item.title }}</span>
            </view>
            <img
              :src="
                require(`@/assets/common/${
                  payWay !== Number(item.code) ? 'zx_nocheck' : 'payselect1'
                }.png`)
              "
              class="payselect"
            >
          </view>
        </view>
        <!-- 1v1 -->
        <view
          v-if="serviceData.type === 1"
          class="onevone font12"
        >
          您正在购买<img
            alt=""
            class="img"
            src="./imgs/headImg.png"
            srcset=""
          >{{ lawyerData.realName }}律师的服务,支付成功后立即进入一对一咨询
        </view>
        <!-- 加急案源付费服务 -->
        <view
          v-else-if="serviceData.type === 2"
          class="other font12"
        >
          <!-- 你正在购买平台加急服务，支付成功可享受金牌律师优先为你解决问题 -->
          您正在购买平台极速服务，支付成功可享受平台精选律师优先为您解决问题
        </view>
        <!-- 公共咨询付费类型 -->
        <view
          v-else-if="serviceData.type === 3"
          class="other font12"
        >
          支付成功后，平台将立即为您安排律师，开始1对1咨询服务
        </view>
        <!-- 赞赏 -->
        <view
          v-else-if="serviceData.type === 4"
          class="other font12"
        >
          温馨提示：您正在购买问答赞赏服务，当前服务不支持退款哦~
        </view>
        <!-- 围观 -->
        <view
          v-else-if="serviceData.type === 5"
          class="other font12"
        >
          温馨提示：您正在购买问答围观服务，当前服务不支持退款哦~
        </view>
      </view>
      <!-- 返回拦截 -->
      <!-- 1v1和公共付费咨询 -->
      <PayPopupOneToOne
        v-model="dialogOneToOneShow"
        @onClick="onClickPayPopupOneToOne"
      />
      <!-- 加急案源付费服务 -->
      <PayPopup
        v-model="dialogShow"
        @handlePcClick="toCreateOrder"
        @onClick="onClickPayPopup"
      />
      <!-- 赞赏围观 -->
      <!-- <van-dialog
        v-model="dialogPraiseOrWatch"
        :show-cancel-button="true"
        cancel-button-color="#262B2F"
        cancel-button-text="确定离开"
        class-name="mixed-refund-dialog"
        confirm-button-color="#3887F5"
        confirm-button-text="继续支付"
        get-container=".perfection-wrap"
        message="系统会将您的订单保留一段时间，请尽快支付哦~"
        :title="praiseOrWatchTitle"
        @cancel="cancelPraiseOrWatch"
        @confirm="dialogPraiseOrWatch=true"
      /> -->
    </view>
    <view class="handle-bottom flex flex-space-between flex-align-center">
      <view class="info">
        <p class="line flex flex-align-center font14">
          合计<span>¥{{ serviceData.servicePrice | amountFilter }}</span>
        </p>
        <p
          v-if="!norefund"
          class="youhui font12"
        >
          已优惠 ¥{{
            (serviceData.originalPrice - serviceData.servicePrice)
              | amountFilter
          }}
        </p>
      </view>
      <view
        class="bt"
        @click="toPayNow"
      >
        立即支付
      </view>
    </view>
  </view>
</template>
<script>
/**
 * type 0-其他服务(历史服务), 1-1V1服务, 2-案源付费服务,3-1v1公共咨询服务4赞赏 5围观
 */

import NoticeListBar from "@/pages/rapid-consultation-confirm-order/components/notice-list-bar/index.vue";
import PayCard from "./components/pay-card";
// import { Dialog } from 'vant'
import {
  BURY_POINT_CHANNEL_TYPE,
  POINT_CODE,
  TRANSFORMATION_PATH,
} from "@/enum/burypoint";
import { newServiceCode, pageRouter } from "@/libs/config";
import { toPay, isPc } from "@/libs/tools";
import { interceptMinxs } from "@/mixins/intercept";
import { createOrder, getCommonConfigKey, serviceManegeInfo } from "@/api";
import { oneLawyer } from "@/api/lawyer";
import PayPopupOneToOne from "./components/pay-popup-onetoone.vue";
import PayPopup from "./components/pay-popup.vue";

import { getReturnRouterStorage } from "@/libs/token";
import { isObjNull } from "@/libs/basics-tools.js";

export default {
  name: "ConfrimOrder",
  components: {
    NoticeListBar,
    PayCard,
    // [Dialog.Component.name]: Dialog.Component,
    PayPopupOneToOne,
    PayPopup,
  },
  mixins: [interceptMinxs],
  data() {
    return {
      newServiceCode,
      backNum: 0,
      /* 1v1拦截弹窗和公共付费*/
      dialogOneToOneShow: false,
      /* 加急案源付费服务拦截弹窗*/
      dialogShow: false,
      /** 围观和赞赏*/
      dialogPraiseOrWatch: false,
      lawyerData: {}, // 律师信息
      serviceData: { type: 1 }, // 服务信息
      pageRouter,
      // payList: [], // 支付方式
      payList: [
        {
          title: "微信官方支付",
          icon: "https://oss.imlaw.cn/test/image/2022/05/18/02f67859e44f49a68ce0eedc329b48df.png",
          code: 11,
        },
      ],
      payWay: 2, // 默认微信官方
    };
  },
  computed: {
    // 是赞赏和围观 不能退款
    norefund() {
      return this.serviceData.type === 4 || this.serviceData.type === 5;
    },
    // 赞赏围观返回拦截标题
    praiseOrWatchTitle() {
      return this.serviceData.type === 4
        ? "还差一点就可以成功赞赏律师了"
        : "还差一点就可以成功围观优质问答了";
    },
  },
  mounted() {
    /* 获取支付方式 */
    getCommonConfigKey({ paramName: "FL_PAY_TYPE_LIST" }).then(({ data }) => {
      if (data.paramValue) {
        this.payList = JSON.parse(data.paramValue);
        this.payWay = Number(this.payList[0].code) || 2;
      }
    });
    this.addListenerBack();
    this.init();
  },
  methods: {
    filterUndefined(val) {
      if (val && val !== "undefined") {
        return val;
      } else {
        return null;
      }
    },
    /** 选择支付方式 */
    selectPayWay(item) {
      this.payWay = Number(item.code);
    },

    async init() {
      this.buryPointPage();
      /** lawyerId 1v1时需传以获取最新的卡片信息 */
      const { serviceCode, lawyerId = null } = this.$route.query;
      let resp = await serviceManegeInfo(
        serviceCode,
        this.filterUndefined(lawyerId)
      );
      this.serviceData = resp.data;
      this.pcData.servicePrice = this.serviceData.servicePrice;
      this.serviceData.type === 1 && this.getLawyerData();
      localStorage.setItem(
        "transformationPath",
        this.$store.getters["getTransformationPath"]
      );
    },
    /** 获取律师信息 */
    async getLawyerData() {
      try {
        const { lawyerId } = this.$route.query;
        const obj = await oneLawyer({ id: lawyerId });
        this.lawyerData = obj.data;
      } catch (error) {
        console.log(error);
      }
    },
    /* 返回拦截事件*/
    backFn() {
      this.backNum++;
      if (this.backNum === 2) {
        if (!this.norefund) {
          this.$router.replace(getReturnRouterStorage());
        } else {
          this.$router.go(0);
        }
      } else {
        if (this.serviceData.type === 2) {
          // 加急案源付费服务
          this.dialogShow = true;
        } else if (this.serviceData.type === 1 || this.serviceData.type === 3) {
          // 1v1和公共付费咨询
          this.dialogOneToOneShow = true;
        } else if (this.norefund) {
          // 赞赏和围观
          this.dialogPraiseOrWatch = true;
        }
      }
    },
    cancelPraiseOrWatch() {
      // 赞赏点击确定离开跳转详情；原型 B-3-问答详情-查看自己-b05
      // 围观点击确定离开跳转详情；原型 B-3-问答详情-查看他人-b01
      // this.$router.replace(getReturnRouterStorage())
      this.$router.go(-1);
    },
    /* 添加返回监听*/
    addListenerBack() {
      history.pushState(null, null, document.URL);
      window.addEventListener("popstate", this.backFn);
      this.$on("hook:destroyed", () => {
        window.removeEventListener("popstate", this.backFn);
      });
    },
    // 1v1返回拦截
    onClickPayPopupOneToOne() {},
    // 返回拦截留言咨询
    onClickPayPopup() {
      this.$router.push(
        `${pageRouter.CASE_DETAILS + this.$route.query.businessId}`
      );
    },
    // 2.1.6点击支付按钮埋点
    payBury() {
      buryPointChannelBasics({
        // 2.1.6
        code: POINT_CODE["LAW_PAGE_ORDER_SURE_PAY_SURE_PAY_SUBMIT"],
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
        extra: {
          serverCode: Number(this.$route.queryserviceCode),
        },
      });
    },
    toPayNow() {
      const { serviceCode, businessId = "", orderId } = this.$route.query;
      if (orderId) {
        this.toPayMethods(orderId);
      } else {
        this.toCreateOrder(serviceCode, businessId);
      }
    },
    async toCreateOrder(serviceCode, businessId, params = {}) {
      try {
        const { type = 1 } = this.serviceData;
        // const typeParam = type === 3 || type === 1 ? 2 : 1
        let typeParam = 1;
        switch (type) {
        case 1:
        case 3:
          typeParam = 2;
          break;
        case 2:
          typeParam = 1;
          break;
        case 4:
          typeParam = 3;
          break;
        case 5:
          typeParam = 4;
          break;
        }
        const { lawyerId, synHistoryId } = this.$route.query;
        // const lawyerId = this.$route.query.lawyerId || ''
        // const synHistoryId = this.$route.query.synHistoryId || ''
        let respOrder = await createOrder(
          isObjNull(params)
            ? {
              serviceCode,
              businessId: this.filterUndefined(businessId),
              type: typeParam,
              lawyerId: this.filterUndefined(lawyerId),
              synHistoryId: this.filterUndefined(synHistoryId),
              pay_position: TRANSFORMATION_PATH[Number(serviceCode)],
            }
            : params
        );
        if (respOrder) {
          // 缓存案源id对应的订单id
          businessId &&
            localStorage.setItem(businessId, respOrder.data.orderId);
          this.toPayMethods(respOrder.data.orderId);
        }
      } catch (error) {
        console.log(error);
      }
    },
    async toPayMethods(orderId) {
      this.pcData.orderId = orderId;
      let params = {
        orderId,
        payType: this.payWay,
        redirectPath: "/pay-state-new/pay-loading",
        isPc: isPc(),
      };
      isPc()
        ? toPay(params).then((data) => {
          this.pcDialog = true;
          let url = JSON.parse(data.payResultMsg);
          this.pcData.pcPayUrl = url.code_url;
        })
        : toPay(params);
    },
  },
};
</script>

<style lang="scss" scoped>
.img {
  width: 18px;
  height: 18px;
}
.confirm-order {
  height: 100vh;
  display: flex;
  flex-direction: column;
  // height:  calc(100vh - constant(safe-area-inset-bottom, 0) - constant(safe-area-inset-top, 0));
  // height:  calc(100vh - env(safe-area-inset-bottom,0) - env(safe-area-inset-top, 0));
  // overflow-y: auto;
  box-sizing: border-box;
}

.onevone {
  line-height: 18px;
  padding: 0 16px;
  margin-top: 16px;
  img {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    margin: 0 2px;
    margin-bottom: -5px;
  }
}

.other {
  color: #333;
  line-height: 18px;
  padding: 0 16px;
  margin-top: 16px;
}

.notice-box {
  height: 40px;
  background: #FCF8F0;
}

::v-deep .van-notice-bar {
  color: #F78C3E;
  padding-left: 20px;
  font-size: 13px;
}

.wrap {
  width: 100%;
  box-sizing: border-box;
  padding-top: 16px;
  flex: 1;
  .title {
    font-weight: bold;
    color: #000000;
    line-height: 19px;
    margin: 16px 0 12px 16px;
  }
}
.payway-box {
  width: 343px;
  background: #fff;
  margin-left: 16px;
  border-radius: 8px;
  padding: 0 16px;
  box-sizing: border-box;
  .payway {
    height: 44px;
    font-size: 14px;
    color: #46474b;
    line-height: 16px;

    .wxpayicon {
      width: 18px;
      height: 18px;
      margin-right: 5px;
      margin-bottom: 3px;
    }

    .payselect {
      width: 16px;
      // margin-right: 3px;
      height: 16px;
    }
  }
}
.handle-bottom {
  width: 375px;
  position: fixed;
  bottom: 0px;
  background: #fff;
  padding: 0 16px;
  box-sizing: border-box;
  height: 60px;
  .info {
    .line {
      color: #333;
      &:nth-child(2) {
        margin-top: 6px;
      }
      span {
        color: #EB4738;
        font-size: 18px;
        font-weight: bold;
        margin-left: 4px;
        margin-bottom: -3px;
      }
    }
    .youhui {
      color: #666666;
      transform: scale(0.9);
      margin-left: -3px;
    }
  }

  .bt {
    width: 218px;
    height: 44px;
    background: linear-gradient(90deg, #fa700d 0%, #EB4738 100%);
    border-radius: 22px 22px 22px 22px;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    line-height: 44px;
    text-align: center;
  }
}
.mixed-refund-dialog {
  ::v-deep .van-dialog__header {
    color: #333;
    font-size: 16px;
    padding-top: 20px;
  }
  ::v-deep .van-dialog__message {
    font-size: 15px;
    color: #999;
    text-align: center;
  }
}
</style>
