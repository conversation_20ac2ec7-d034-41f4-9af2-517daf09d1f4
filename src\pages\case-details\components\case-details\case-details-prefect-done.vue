<template>
  <div>
    <p class="case-title">
      完善信息
    </p>
    <!-- 详情界面才进行显示 -->
    <case-details-info :dataSource="data" />
  </div>
</template>

<script>
import CaseDetailsInfo from "@/pages/case-details/components/case-details/case-details-info.vue";
// import { postListCaseSourceV2Result } from '@/api'

/**
 * 完善案件资料已完成
 * <AUTHOR>
 * @Version 2.1.6
 * @Description 该版本改动信息
 * @Date 2022/4/28
 * @FilePath components\case-details\case-details-prefect-done.vue
 */
export default {
  name: "CaseDetailsPrefectDone",
  components: { CaseDetailsInfo },
  props: {
    /** 需要展示的数据 */
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      /** 当前案例详情列表 */
      caseSourceData: [],
    };
  },
  // onLoad(options) {
  //    postListCaseSourceV2Result(options.id).then((res) => {
  //     this.caseSourceData = res.data || []
  //     console.log('caseSourceData:', this.caseSourceData)
  //   })
  // },
};
</script>

<style lang="scss" scoped>
.case-title {
  font-size: 16px;
  font-weight: bold;
  color: #222222;
  margin-bottom: 12px;
  margin-top: 16px;
}
</style>
