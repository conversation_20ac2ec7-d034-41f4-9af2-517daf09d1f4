<template>
  <div>
    <div class="after-sales position-relative">
      <if f="hjls">
        <img
          alt=""
          class="background-image"
          src="@/pages/apply-refund/after-sales/img/bg.png"
        >
      </if>
      <if t="hjls">
        <img
          alt=""
          class="background-image"
          src="@/pages/apply-refund/after-sales/img/Frame3225.png"
        >
      </if>
      <div
        class="top-button"
        @click="clickChangeLawyer"
      >
        我要换律师
      </div>
      <div
        class="bottom-button"
        @click="clickRefund"
      >
        我要退款
      </div>
    </div>
    <upgrade-refund-service-popup
      v-model="upgradeRefundServicePopup"
      @refund="toRefund"
      @upgrade="clickChangeLawyer"
    />
  </div>
</template>

<script>
import { toChangeLawyer, toRefund } from "@/libs/turnPages";
import {
  orderUserGetOrderById,
  refundInfoNew,
  userChangeLawyerApplyDetail
} from "@/api/order.js";
import { isObjNull } from "@/libs/basics-tools.js";
import { buryPointChannelBasics } from "@/api/burypoint";
import {
  BURY_POINT_CHANNEL_TYPE,
  globalTransformationPath,
  POINT_CODE
} from "@/enum/burypoint";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath";
import UpgradeRefundServicePopup from "@/pages/apply-refund/components/UpgradeRefundServicePopup.vue";

export default {
  name: "ApplyRefundAfterSales",
  components: { UpgradeRefundServicePopup },
  data() {
    return {
      routerParams: {},
      orderInfo: {},
      orderById: {},
      /** 订单退款信息 */
      refundInfo: {},
      /** 退款挽留弹窗 */
      upgradeRefundServicePopup: false
    };
  },
  onLoad(query) {
    buryPointTransformationPath.add(
      globalTransformationPath.LAW_APPLET_AFTER_SALE_PAGE
    );

    buryPointChannelBasics({
      code: POINT_CODE.LAW_APPLET_AFTER_SALE_PAGE,
      type: 1,
      behavior: BURY_POINT_CHANNEL_TYPE.CK
    });

    this.routerParams = query;
    this.getOrderInfo();
    this.getOrderUserGetOrderById();
    this.getRefundInfo();
  },
  methods: {
    /** 点击我要换律师 */
    clickChangeLawyer() {
      buryPointChannelBasics({
        code: "LAW_APPLET_RECEIVED_ORDER_REFUND_POPUP_PAGE_UPGRADE_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_AFTER_SALE_PAGE_TOP_CHANGE_LAWYER_CLICK,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      if (!isObjNull(this.refundInfo)) {
        this.$toast("您已申请过退款");
        return;
      }

      if (!isObjNull(this.orderInfo) || this.orderById?.parentOrderId) {
        this.$toast("您已换过律师啦");
        return;
      }

      toChangeLawyer(this.routerParams);
    },
    /** 点击我要退款 */
    clickRefund() {
      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_AFTER_SALE_PAGE_TOP_REFUND_CLICK,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      if (!isObjNull(this.refundInfo)) {
        this.$toast("您已申请过退款");
        return;
      }

      buryPointChannelBasics({
        code: "LAW_APPLET_RECEIVED_ORDER_REFUND_POPUP_PAGE",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      this.upgradeRefundServicePopup = true;
    },
    /** 退款按钮 */
    toRefund() {
      buryPointChannelBasics({
        code: "LAW_APPLET_RECEIVED_ORDER_REFUND_POPUP_PAGE_REFUND_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      this.upgradeRefundServicePopup = false;

      toRefund(this.routerParams);
    },
    /** 获取订单信息 */
    getOrderInfo() {
      userChangeLawyerApplyDetail({
        orderId: this.routerParams.orderId
      }).then(({ data = {} }) => {
        this.orderInfo = data;
      });
    },
    /** 获取订单是否可以退款 */
    getRefundInfo() {
      refundInfoNew({ orderId: this.routerParams.orderId }).then(
        ({ data = {} }) => {
          this.refundInfo = data;
        }
      );
    },
    getOrderUserGetOrderById() {
      orderUserGetOrderById({
        orderId: this.routerParams.orderId
      }).then(({ data = {} }) => {
        this.orderById = data;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.after-sales {
  width: 375px;
  height: 630px;
}

.after-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 311px;
  height: 48px;
  border-radius: 46px;
  opacity: 1;
  border: 1px solid #3887f5;
  font-size: 16px;
  font-weight: bold;
  color: #3887f5;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.top-button {
  @extend .after-button;
  top: 283px;
}

.bottom-button {
  @extend .after-button;
  top: 523px;
}
</style>
