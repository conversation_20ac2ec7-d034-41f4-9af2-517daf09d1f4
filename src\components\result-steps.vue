<template>
  <div class="progress-rate">
    <p
      v-for="(i, index) in progressList"
      :key="index"
      :class="{ active: index === activeIndex }"
      class="flex flex-align-center"
    >
      {{ i }}
      <img
        v-if="index !== progressList.length - 1"
        alt=""
        class="progress-rate-right"
        src="@/pages/submit-question/imgs/<EMAIL>"
      >
    </p>
  </div>
</template>

<script>
export default {
  name: "ResultSteps",
  props: {
    activeIndex: {
      type: Number,
      default: 1,
    },
    progressList: {
      type: Array,
      default: () => ["描述问题", "匹配律师"],
    },
  },
};
</script>

<style lang="scss" scoped>
.progress-rate {
  display: flex;
  align-items: center;

  font-size: 12px;
  font-weight: 400;
  color: #999999;

  .active {
    color: $theme-color;
  }
}

.progress-rate-right {
  width: 12px;
  height: 12px;
}
</style>
