<template>
  <div
    v-if="
      !isArrNull(guessList) ||
        (currentTab === 'history' &&
          historyTextList &&
          historyTextList.length > 0)
    "
    class="box-border"
  >
    <!-- Tab切换区域 -->
    <div class="flex items-center justify-between mb-[12px]">
      <div class="flex items-center">
        <div
          class="text-[12px]"
          :class="[
            currentTab === 'guess' ? 'text-[#333333]' : 'text-[#999999]',
          ]"
          @click="switchTab('guess')"
        >
          猜你想问
        </div>
        <div
          class="text-[12px] ml-[16px]"
          :class="[
            currentTab === 'history' ? 'text-[#333333]' : 'text-[#999999]',
          ]"
          @click="switchTab('history')"
        >
          历史咨询
        </div>
      </div>
      <div
        v-if="hasChangeBatch"
        class="flex items-center cursor-pointer"
        @click="changeBatch"
      >
        <img
          alt=""
          class="w-[16px] h-[16px] block mr-[4px]"
          src="../pages/rapid-consultation-confirm-order/special-session/img/<EMAIL>"
        >
        <div class="text-[12px] text-[#666666] font-normal">
          换一批
        </div>
      </div>
    </div>

    <!-- 猜你想问内容 -->
    <div
      v-if="currentTab === 'guess'"
      class="flex"
    >
      <div
        v-for="item in list"
        :key="item.value"
        class="h-[80px] bg-[#F5F5F5] rounded-[4px] border-[1px] border-solid border-[#EEEEEE] text-[12px] text-[#333333] flex items-start justify-center p-[10px_10px_16px_10px] box-border flex-1 ml-[11px] first:ml-0 position-relative overflow-hidden"
        @click="() => clickGuessQuestion(item.remark)"
      >
        <div class="w-[52px] h-[22px] bg-[#EBF3FE] rounded-tl-[4px] rounded-br-[4px] rounded-tr-none rounded-bl-none absolute bottom-0 right-0 flex items-center justify-center box-border">
          <div class="text-[11px] text-[#3887F5]">
            一键填入
          </div>
        </div>
        <div>
          {{ item.remark }}
        </div>
      </div>
    </div>

    <!-- 历史咨询内容 -->
    <div
      v-if="currentTab === 'history' && currentHistoryList.length > 0"
      class="flex"
    >
      <div
        v-for="(item, index) in currentHistoryList"
        :key="index"
        class="h-[80px] bg-[#F5F5F5] rounded-[4px] border-[1px] border-solid border-[#EEEEEE] text-[12px] text-[#333333] flex items-star justify-center p-[10px_10px_16px_10px] box-border flex-1 ml-[11px] first:ml-0 position-relative overflow-hidden"
        @click="handleHistoryClick(item)"
      >
        <div class="w-[52px] h-[22px] bg-[#EBF3FE] rounded-tl-[4px] rounded-br-[4px] rounded-tr-none rounded-bl-none absolute bottom-0 right-0 flex items-center justify-center box-border">
          <div class="text-[11px] text-[#3887F5]">
            一键填入
          </div>
        </div>
        <div class="font-normal">
          {{ item }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import { isArrNull } from "@/libs/basics-tools";
import { aliGuessYouWant, gdtKeywordFn } from "@/api/search";
import { dataDictionary } from "@/api";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath";
import { getLatestArticleTitle } from "@/pages/lawyer-home";
import { getParalegalData } from "@/libs/paralegalData.js";

export default {
  name: "GuessYouWantAsk",
  props: {
    uvCode: {
      type: String,
      default: "LAW_APPLET_ASK_LAWYER_GUESS_CONFIG_ERROR_TOAST_PAGE",
    },
  },
  data() {
    return {
      /** 猜你想问的问题列表 */
      guessList: [],
      list: [],
      /** 当前选中的tab */
      currentTab: "guess",
      /** 历史咨询数据 */
      historyList: [],
      /** 当前显示的历史咨询列表 */
      currentHistoryList: [],
    };
  },
  computed: {
    /**
     * 是否有换一批按钮
     */
    hasChangeBatch() {
      return (this.currentTab === "guess" && this.guessList.length > 2) || (this.currentTab === "history" && this.historyTextList.length > 2);
    },
    /**
     * 历史咨询是否有换一批按钮
     */
    hasHistoryChangeBatch() {
      return this.historyTextList.length > 2;
    },
    /**
     * 历史咨询文本列表
     */
    historyTextList() {
      return this.historyList.map((item) => item.info);
    },
    /**
     * 是否登录
     */
    isLogin() {
      return this.$store.getters["user/getToken"];
    },
  },
  mounted() {
    buryPointChannelBasics({
      code: this.uvCode,
      type: 1,
      behavior: BURY_POINT_CHANNEL_TYPE.CK,
    });

    this.getGuessList();
    this.getAiGuessList();
    this.getHistoryData();
  },
  methods: {
    isArrNull,
    /** 切换tab */
    switchTab(tab) {
      this.currentTab = tab;
      this.$emit("tabChange", tab);
    },
    /** 点击换一批 */
    changeBatch() {
      if (this.currentTab === "guess") {
        this.$emit("changeBatch");
        this.getList();
      } else {
        this.$emit("changeHistoryBatch");
        this.getHistoryList();
      }
    },
    /** 点击历史咨询换一批 */
    changeHistoryBatch() {
      this.getHistoryList();
    },
    /** 从列表中随机取2个 */
    getList() {
      const list = this.guessList.slice(0);
      const randomList = [];

      // 如果原数组长度小于等于2，直接全部使用
      if (list.length <= 2) {
        this.list = list;
        return;
      }

      // 从列表中随机取2个
      for (let i = 0; i < 2; i++) {
        const randomIndex = Math.floor(Math.random() * list.length);
        randomList.push(list[randomIndex]);
        list.splice(randomIndex, 1);
      }
      this.list = randomList;
    },
    /** 从历史咨询列表中随机取2个 */
    getHistoryList() {
      const list = this.historyTextList.slice(0);
      const randomList = [];

      // 如果原数组长度小于等于2，直接全部使用
      if (list.length <= 2) {
        this.currentHistoryList = list;
        return;
      }

      // 从列表中随机取2个
      for (let i = 0; i < 2; i++) {
        const randomIndex = Math.floor(Math.random() * list.length);
        randomList.push(list[randomIndex]);
        list.splice(randomIndex, 1);
      }
      this.currentHistoryList = randomList;
    },
    /** 获取猜你想问的问题列表 */
    getGuessList() {
      dataDictionary({
        groupCode: "GUESS_YOU_WANT_ASK",
      }).then((res) => {
        this.guessList = res.data || [];
        this.getList();
      });
    },
    /**
     * 获取AI的猜你想问列表
     * 如果有 clickId 说明是从腾讯广告点击进来
     * https://lanhuapp.com/web/#/item/project/product?pid=aff2057b-63e8-4432-bbe5-92da4cfecbe1&image_id=35a5d641-6c07-4f72-849e-d4801f03a41b&tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=6cdef7ce-4813-4f93-bafb-f021dc58212b&docId=35a5d641-6c07-4f72-849e-d4801f03a41b&docType=axure&pageId=2bba84ee87df4f73bc0e11fa8034e3bc&parentId=2a47e437-b9ef-45a1-99c0-85da5c4ba4c3
     */
    getAiGuessList() {
      const latestArticleTitle = getLatestArticleTitle() || "";

      // 这个的优先级更高
      if (latestArticleTitle) {
        aliGuessYouWant({
          info: latestArticleTitle,
        }).then((res) => {
          this.guessList = (res.data.content || []).map((item, index) => {
            return {
              remark: item,
              value: index,
            };
          });
          this.getList();
        });

        return;
      }

      gdtKeywordFn().then((res) => {
        const keywordText = res?.data?.keywordText;

        // 没有关键词则不进行请求
        if (!keywordText) return;

        aliGuessYouWant({
          info: keywordText,
        }).then((res) => {
          this.guessList = (res.data.content || []).map((item, index) => {
            return {
              remark: item,
              value: index,
            };
          });
          this.getList();
        });
      });
    },
    /** 点击猜你想问的问题 */
    clickGuessQuestion(item) {
      buryPointTransformationPath.add(5055);

      buryPointChannelBasics({
        code: "LAW_APPLET_ASK_LAWYER_FAKE_IM_PAGE_GUESS_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
      });

      this.$emit("click", item);
    },
    /** 请求历史数据 */
    getHistoryData() {
      if (this.isLogin) {
        getParalegalData().then((data) => {
          this.historyList = data?.data || [];
          // 初始化当前显示的历史咨询列表
          this.getHistoryList();
        });
      }
    },
    /** 处理历史咨询点击 */
    handleHistoryClick(value) {
      this.$emit("click", value);
    },
  },
};
</script>
