<template>
  <login-layout>
    <search-input
      v-model="searchInput"
      @search="goResult"
    />
    <div class="w-[375px] h-[44px] bg-[#FFFFFF] grid grid-cols-2 box-border">
      <div
        v-for="item in tabList"
        :key="item.value"
        :class="[{
          'font-bold text-[#222222]': currentTab === item.value,
          'text-[#999999]': currentTab !== item.value
        }]"
        class="w-full relative flex items-center justify-center"
        @click="changeTab(item.value)"
      >
        <div class="text-[14px]">
          {{ item.label }}
        </div>
        <div
          v-if="currentTab === item.value"
          class="w-[14px] h-[3px] bg-[#3887F5] rounded-[70px] absolute bottom-0 absolute-center"
        />
      </div>
    </div>
    <div class="px-[12px] mt-[12px]">
      <template v-if="currentTab===tabEnum.article">
        <div
          v-if="articleTotal > 0"
          class="space-y-[12px]"
        >
          <div
            v-for="item in articleV2LawyerPageSearchData"
            :key="item.id"
            @click="clickArticleBurialPoint"
          >
            <essay-item :data="item" />
          </div>
        </div>
        <div v-else>
          <search-empty
            @toAskLawyer="toAskLawyer"
          />
          <img
            v-if="bannerUrl.imageUrl"
            :src="bannerUrl.imageUrl"
            alt=""
            class="w-[351px] mt-[12px]"
            mode="widthFix"
            @click="jumpToPage(bannerUrl.addressUrl)"
          >
        </div>
      </template>
      <template v-if="currentTab===tabEnum.qaMessage">
        <div
          v-if="qaMessageTotal > 0"
          class="space-y-[12px]"
        >
          <div
            v-for="item in qaMessage2cPageSearchData"
            :key="item.id"
            @click="clickQaMessageBurialPoint"
          >
            <lawyer-card :data="item" />
          </div>
        </div>
        <div v-else>
          <search-empty
            @toAskLawyer="toAskLawyer"
          />
          <img
            v-if="bannerUrl.imageUrl"
            :src="bannerUrl.imageUrl"
            alt=""
            class="w-[351px] mt-[12px]"
            mode="widthFix"
            @click="jumpToPage(bannerUrl.addressUrl)"
          >
        </div>
      </template>
      <u-safe-bottom />
    </div>
    <telephone-consultation-popup />
  </login-layout>
</template>

<script>
import LawyerCard from "@/pages/index/component/lawyerCard.vue";
import EssayItem from "@/components/essay-item/index.vue";
import SearchEmpty from "@/pages/ask-details/search/components/SearchEmpty.vue";
import { articleV2LawyerPageSearch, qaMessage2cPageSearch } from "@/api/common";
import searchData from "@/pages/ask-details/search/searchData";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import SearchInput from "@/components/SearchInput.vue";
import { advertListPosition } from "@/api/lawyer";

import LoginLayout from "@/components/login/login-layout.vue";
import TelephoneConsultationPopup from "@/components/telephone-consultation-popup/index.vue";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import { sceneType } from "@/libs/config";
import { toConfirmOrderPlus } from "@/libs/tools";

const tabEnum = {
  article: "a",
  qaMessage: "q"
};

export default {
  name: "SearchSpecialResult",
  components: {
    TelephoneConsultationPopup,
    LoginLayout,
    SearchInput,
    USafeBottom,
    SearchEmpty,
    EssayItem,
    LawyerCard
  },
  data() {
    return {
      /** 问答数据 */
      qaMessage2cPageSearchData: [],
      /** 文章数据 */
      articleV2LawyerPageSearchData: [],
      typeValue: "",
      searchInput: searchData.searchText || "",
      /** 广告位数据 */
      bannerUrl: {},
      tabEnum,
      query: {
        currentPage: 1,
        pageSize: 10
      },
      isLastPage: false,
      tabList: [
        {
          label: "优质问答",
          value: tabEnum.qaMessage
        },
        {
          label: "法律文章",
          value: tabEnum.article
        }
      ],
      currentTab: "q"
    };
  },
  computed: {
    /** 法律文章数量 */
    articleTotal() {
      return this.articleV2LawyerPageSearchData?.length || 0;
    },
    /** 一问多答数量 */
    qaMessageTotal() {
      return this.qaMessage2cPageSearchData?.length || 0;
    }
  },
  watch: {
    currentTab: {
      handler() {
        this.resetData();
        this.getData();
      },
      immediate: true
    }
  },
  mounted() {
    this.getBannerData();
  },
  onReachBottom() {
    this.query.currentPage++;
    this.getData();
  },
  methods: {
    /** 重置数据 */
    resetData() {
      this.qaMessage2cPageSearchData = [];
      this.articleV2LawyerPageSearchData = [];
      this.query.currentPage = 1;
      this.isLastPage = false;
    },
    /** 点击搜索按钮 */
    goResult(value) {
      searchData.searchText = value;

      this.resetData();
      this.getData();
    },
    /** 请求数据 */
    getData() {
      const keyword = searchData.searchText;

      if (this.isLastPage) {
        return;
      }

      if (this.currentTab === tabEnum.article) {
        articleV2LawyerPageSearch({
          keyword,
          ...this.query
        }).then(res => {
          this.articleV2LawyerPageSearchData = [...this.articleV2LawyerPageSearchData, ...res.data.records];
          const total = res.data.total;

          if (this.articleV2LawyerPageSearchData.length >= total) {
            this.isLastPage = true;
          }
        });
      } else {
        qaMessage2cPageSearch({
          keyword,
          ...this.query
        }).then(res => {
          this.qaMessage2cPageSearchData = [...this.qaMessage2cPageSearchData, ...res.data.records];

          if (this.qaMessage2cPageSearchData.length >= res.data.total) {
            this.isLastPage = true;
          }
        });
      }
    },
    /** 获取广告位数据 */
    getBannerData() {
      advertListPosition({ positionId: 185 }).then(({ data }) => {
        this.bannerUrl = data?.[0]?.advertContents?.[0] || {};
      });
    },
    /** 点击跳转页面 */
    jumpToPage(addressUrl) {
      buryPointChannelBasics({
        code: "LAW_APPLET_CONSULT_TOPIC_SECOND_PAGE_RESULT_PAGE_LAWYER_BANNER_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });

      uni.navigateTo({
        url: addressUrl
      });
    },
    toAskLawyer() {
      buryPointChannelBasics({
        code: "LAW_APPLET_CONSULT_TOPIC_SECOND_PAGE_RESULT_PAGE_ASK_LAWYER_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });

      toConfirmOrderPlus(sceneType.sydb);
    },
    /** 点击文章埋点 */
    clickArticleBurialPoint() {
      buryPointChannelBasics({
        code: "LAW_APPLET_SEARCH_RESULT_PAGE_ARTICLE_CARD_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });
    },
    /** 点击问答埋点 */
    clickQaMessageBurialPoint() {
      buryPointChannelBasics({
        code: "LAW_APPLET_SEARCH_RESULT_PAGE_QA_MESSAGE_CARD_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });
    },
    changeTab(value) {
      this.currentTab = value;
    }
  }
};
</script>
