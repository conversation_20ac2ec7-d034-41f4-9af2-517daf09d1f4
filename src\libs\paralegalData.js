import {
  caseSourceV2HistoryAddInfo,
  caseSourceV2HistoryGetInfo,
  caseSourceV2HistoryUpdateInfo,
} from "@/api/im";

const $localStorage = {
  setItem: uni.setStorageSync,
  getItem: uni.getStorageSync,
  removeItem: uni.removeStorageSync,
};
const SYN_HISTORY_ID = "synHistoryId";

/**
 * 获取上一次咨询历史的id
 * @returns {string|string}
 */
export function getParalegalDataId() {
  return uni.getStorageSync(SYN_HISTORY_ID) || "";
}

/** 跟新咨询历史 */
export function updateParalegalData(data) {
  caseSourceV2HistoryUpdateInfo(data);
}

/** 储存咨询历史 */
export async function saveParalegalData(data) {
  try {
    const res = (await caseSourceV2HistoryAddInfo(data)).data;

    // 将后端返回的id存入本地
    uni.setStorageSync(SYN_HISTORY_ID, res.id);

    return res;
  } catch (e) {
    return {};
  }
}

/** 读取咨询历史 */
export function getParalegalData() {
  try {
    return caseSourceV2HistoryGetInfo();
  } catch (e) {
    return [];
  }
}
