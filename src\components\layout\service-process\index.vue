<template>
  <div class="business-step">
    <div class="title">
      服务流程
    </div>
    <div class="business-step-wrap flex flex-space-between">
      <div
        v-for="(item,index) in processData"
        :key="index+'processData'"
        class="item"
      >
        <img
          :src="require(`@/assets/case-about/doc-step${index+1}.png`)"
          alt=""
          class="img"
        >
        <div>{{ item }}</div>
      </div>
      <!-- <div class="item">
        <img class="img" src="@/assets/case-about/doc-step2.png" alt="">
        <div>匹配律师</div>
      </div>
      <div class="item">
        <img class="img" src="@/assets/case-about/doc-step3.png" alt="">
        <div>沟通服务</div>
      </div>
      <div class="item">
        <img class="img" src="@/assets/case-about/doc-step4.png" alt="">
        <div>确认完成</div>
      </div> -->
    </div>
  </div>
</template>

<script>
export default {
  name: "Index",
  props: {
    processData: {
      type: Array,
      default: () => ["提交需求", "匹配律师", "沟通服务", "确认完成"]
    }
  }
};
</script>

<style lang="scss" scoped>
.business-step {
  &-wrap {
    width: 343px;
    height: 64px;
    background: #F9F9F9;
    border-radius: 8px;
    padding: 15px 17px;
    box-sizing: border-box;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333333;
    line-height: 17px;
  }

  .title {
    font-size: 14px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: bold;
    color: #333333;
    line-height: 20px;
    margin: 16px 0 7px;
  }

  .item {
    .img {
      width: 37px;
      height: 13px;
      margin-bottom: 4px;
    }
  }
}
</style>
