import webImTools from "@/libs/web-im-tools";
import { caseSourceV2HistorySynWd } from "@/api/im";
import { getParalegalDataId } from "@/libs/paralegalData";
import store from "@/store/index.js";
let CALLBACK_INDEX = 0;

export const paralegalData = "im_history";
export const SUBMIT_QUESTION_DATA = "submit_question_data";

export const asyncRequest = {};
export const callbacklink = {};

export function setAsyncRequest({ asyncName, callback }) {
  if (typeof callback !== "function") {
    console.error("setAsyncRequest params callback is not a function");
    return;
  }

  if (!asyncRequest[asyncName]) asyncRequest[asyncName] = [];

  console.log(asyncRequest[asyncName], "asyncRequest[name]");

  asyncRequest[asyncName].push(callback);
}

export function triggerAsyncRequest({ asyncName, callback }) {
  // 确保 asyncRequest[asyncName] 是一个数组
  if (!Array.isArray(asyncRequest[asyncName])) {
    asyncRequest[asyncName] = [];
  }

  // 日志输出
  console.log(asyncRequest[asyncName], "triggerAsyncRequest");

  // 因为vue组件渲染的问题，所以可能导致 triggerAsyncRequest 调用的时候，相关的回调函数还没有进行注册
  // 所以这里使用递归进行调用，保证在执行函数的时候，相关回调一定会被触发一次
  if (asyncRequest[asyncName].length > 0) {
    asyncRequest[asyncName]?.forEach((item) => {
      item(callback);
    });

    // 触发一次后就进行清空操作
    asyncRequest[asyncName] = [];
  } else {
    setTimeout(() => {
      triggerAsyncRequest({ asyncName, callback });
    }, 1000);
  }
}

export const ASYNC_REQUEST_NAME = {
  aiTips: "aiTips",
};

/**
 * 操作聊天助手历史记录的类
 * 单独封装，更好调用
 */
class ParalegalHistory {
  /** 存储历史记录 */
  saveHistory(data) {
    try {
      uni.setStorageSync(
        paralegalData + store.getters["user/getToken"],
        JSON.stringify(data)
      );
    } catch (e) {
      uni.setStorageSync(paralegalData + store.getters["user/getToken"], "[]");
    }
  }

  /** 获取历史记录 */
  getHistory() {
    try {
      return JSON.parse(
        uni.getStorageSync(paralegalData + store.getters["user/getToken"])
      );
    } catch (e) {
      return undefined;
    }
  }

  /** 储存列表信息 */
  saveHistoryData(data) {
    uni.setStorageSync(
      SUBMIT_QUESTION_DATA + store.getters["user/getToken"],
      data
    );
  }

  /** 获取列表信息 */
  getHistoryData() {
    return (
      uni.getStorageSync(
        SUBMIT_QUESTION_DATA + store.getters["user/getToken"]
      ) || []
    );
  }

  /**
   * 判断是否是假私聊问律师进入的
   * https://lanhuapp.com/web/#/item/project/detailDetach?pid=dee1c4fe-a0b2-4bc3-a229-69c83138fa62&image_id=4792ccea-8446-4fa3-b497-3fdb5f1f1df8&tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&project_id=dee1c4fe-a0b2-4bc3-a229-69c83138fa62&fromEditor=true&type=image
   */
  isFakePrivate() {
    const historyData = this.getHistory();

    return !!historyData?.fakeLawyerInfo;
  }

  /** 清空历史记录 */
  clearHistory() {
    uni.removeStorageSync(paralegalData + store.getters["user/getToken"]);
    uni.removeStorageSync(
      SUBMIT_QUESTION_DATA + store.getters["user/getToken"]
    );
  }

  /**
   * 获得咨询类型
   */
  getConsultType() {
    const history = this.getHistory();

    return {
      ...(history.selectData?.[0] || {}),
    };
  }

  /**
   * 获取咨询消息
   */
  getConsultMessage() {
    const history = this.getHistory();

    return history.info || "";
  }
}

class ParalegalTools extends ParalegalHistory {
  /**
   * im 添加数据的工具
   */
  imTools = {};

  /** 收到消息的人 */
  otherSideUserName = "";

  /** 发送消息的人 */
  currentUserName = "";

  /** 消息队列 */
  messageQueue = [];

  constructor(otherSideUserName, currentUserName, options) {
    super();
    this.imTools = new webImTools(otherSideUserName, currentUserName);
    this.otherSideUserName = otherSideUserName;
    this.currentUserName = currentUserName;
    this.messageQueue = [];
    const { catchList, notCatchComponentsNameList } = options || {};

    this.catchList = catchList;
    this.notCatchComponentsNameList = notCatchComponentsNameList;
  }

  /**
   * 延时
   * @param delayTime
   * @returns {Promise<unknown>}
   */
  delay(delayTime) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, delayTime);
    });
  }

  asyncDelay(asyncName) {
    return new Promise((resolve) => {
      setAsyncRequest({
        asyncName: asyncName,
        callback: (callback) => {
          callback?.(asyncName);
          resolve();
        },
      });
    });
  }

  /**
   * 延迟添加消息 在一个dataList会按照顺序进行添加
   * @param targetList 目标数组
   * @param dataList 数据数组
   * @returns {Promise<void>}
   */
  async delayAddData(targetList, dataList) {
    for (let i = 0; i < dataList.length; i++) {
      if (dataList[i].asyncName) {
        targetList.push(dataList[i]);
        await this.asyncDelay(dataList[i].asyncName);
      } else {
        await this.delay(dataList[i].delay);
        targetList.push(dataList[i]);
      }
    }
  }

  promise(targetList, target) {
    targetList.push(target);

    return new Promise((resolve, reject) => {
      if (target.callbackIndex) {
        // 判断是否是promise
        const callback = callbacklink[target.callbackIndex];

        // 将callback进行包装，使它具有promise的特性，以防止回调地狱
        callbacklink[target.callbackIndex] = (...args) => {
          if (callback instanceof Promise) {
            callback(...args)
              .then((data) => {
                resolve(data);
              })
              .catch((err) => {
                reject(err);
              });
          } else {
            resolve(callback(...args));
          }
        };
      }
    });
  }

  /**
   * 添加消息
   * @param {"custom"|"text"} type
   * @param data
   * @param username
   * @param otherData
   * @returns {*}
   * @private
   */
  _addData({ type, data, username, otherData, callback }) {
    let dataExt = otherData;

    if (callback) {
      callbacklink[CALLBACK_INDEX] = callback;

      dataExt = {
        ...otherData,
        // 回调索引
        callbackIndex: CALLBACK_INDEX,
      };

      CALLBACK_INDEX++;
    }

    const catchList = this.catchList;


    switch (type) {
      case "custom": {
        const chat = this.imTools.setPrivateCustom(data, username, dataExt);
        console.log(catchList, "catchList");
        if (catchList) {
          if (!this.notCatchComponentsNameList.find((item) => item === data.style)) {
            catchList.push(chat);
          }
        }

        return chat;
      }
      case "text": {
        const chat = this.imTools.setPrivateText(data, username, dataExt);

        if (catchList) {
          catchList.push(chat);
        }

        return chat;
      }
    }
  }

  /**
   *
   * 添加收到的消息
   * @param {"custom"|"text"} type
   * @param [data]
   * @param [username] 有默认值，一般情况下不推荐使用该值
   * @param [otherData]
   * @param [avatarUrl]
   * @param [spinning] 是否显示加载动画
   * @param [delay] 延迟添加消息
   * @param [asyncName] 异步请求名称
   * @param [callback]
   */
  addReceiveData({
    type,
    data,
    username = this.otherSideUserName,
    otherData,
    avatarUrl = true,
    spinning = false,
    delay = 0,
    asyncName = null,
    callback,
  }) {
    return this._addData({
      type,
      data,
      username: avatarUrl ? username : "",
      otherData: {
        spinning,
        delay,
        asyncName,
        ...otherData,
      },
      callback,
    });
  }

  /**
   * 添加发送的消息
   * @param {"custom"|"text"} type
   * @param [data]
   * @param [username] 有默认值，一般情况下不推荐使用该值
   * @param [otherData]
   * @param [spinning]
   * @param [delay]
   * @param [asyncName] 异步请求名称
   */
  addSendData({
    type,
    data,
    username = this.currentUserName,
    otherData,
    spinning = false,
    delay = 0,
    asyncName = null,
    callback,
  }) {
    return this._addData({
      type,
      data,
      username,
      otherData: {
        spinning,
        delay,
        asyncName,
        ...otherData,
      },
      callback,
    });
  }

  /** 从消息队列中获取消息 */
  getMessageFromQueue() {
    return this.messageQueue.shift();
  }

  /** 往消息队列中添加消息 */
  addMessageToQueue(message) {
    this.messageQueue.push(message);
  }

  /**
   * 初始化消息队列
   * @param data
   */
  initMessageQueue(data = []) {
    this.messageQueue = data;
  }
}

export function paralegalTools(otherSideUserName, currentUserName, options) {
  return new ParalegalTools(otherSideUserName, currentUserName, options);
}

export const paralegalHistory = new ParalegalHistory();

/** 判断是否生成问答 */
export async function isGenerateQuestion() {
  const res = await caseSourceV2HistorySynWd({
    id: getParalegalDataId(),
  });

  // * 如果 res.data 为 false 则直接退出
  if (!res.data) {
    throw new Error("不是全量用户");
  }
}

/** 从缓存中获取首页中选择的值 */
export function getCacheTypeValue() {
  const lastValue = uni.getStorageSync("cacheTypeValue") || {};

  if (!lastValue.value) {
    return {};
  }

  uni.removeStorageSync("cacheTypeValue");

  return lastValue;
}
