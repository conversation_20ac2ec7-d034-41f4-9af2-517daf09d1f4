<template>
  <div v-if="!isArrNull(quesInfoList)">
    <div class="title">
      <div>热门问答</div>
      <div
        class="title-more"
        @click="showMore"
      >
        <div>更多</div>
        <img
          alt=""
          class="title-more__icon"
          src="@/pages/index/imgs/arrow.png"
        >
      </div>
    </div>
    <div class="content position-relative">
      <img
        alt=""
        class="background-image"
        src="@/pages/ask-details/img/<EMAIL>"
      >
      <div class="card-box">
        <div
          v-for="item in quesInfoList"
          :key="item.id"
          class="card-item"
          @click="toQaDetails(item)"
        >
          <div class="card-item__title flex flex-align-center">
            <img
              alt=""
              class="card-item__title__icon"
              src="@/pages/ask-details/img/<EMAIL>"
            >
            <div class="mg-l-4 text-ellipsis">
              {{ item.detail }}
            </div>
          </div>
          <div class="card-item__content text-ellipsis-2">
            {{ item.replyContent }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LawyerCard from "@/pages/index/component/lawyerCard.vue";
import { qaMessagePageList } from "@/api";
import { isArrNull } from "@/libs/basics-tools.js";

export default {
  name: "DetailLawyer",
  components: { LawyerCard },
  props: {
    details: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      quesInfoList: [],
    };
  },
  mounted() {
    this.getQaMessagePageList();
  },
  methods: {
    isArrNull,
    /* 跳转问答详情*/
    toQaDetails(data){
      uni.navigateTo({
        url: "/pages/ask-details/index?id=" + data.id,
      });
    },
    /** 点击查看更多 */
    showMore() {
      uni.navigateTo({
        url:
          "/pages/lawyer-home/all-answer/index?typeValue=" +
          this.details.typeValue,
      });
    },
    /**
     * 获取优秀律师解答
     */
    getQaMessagePageList() {
      qaMessagePageList({
        qaMessageRequestType: 2,
        pageSize: 3,
        typeValue: this.details.typeValue,
      }).then(({ code, data: { records = [] } }) => {
        if (code === 0) {
          this.quesInfoList = records.filter((el) => el.id !== this.details.id);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  box-sizing: border-box;
  width: 375px;
  height: 164px;
  padding: 54px 12px 0 12px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;

  &-more {
    display: flex;
    align-items: center;
    font-size: 13px;
    font-weight: 400;
    color: #999999;

    &__icon {
      width: 16px;
      height: 16px;
      display: block;
    }
  }
}

.card-box {
  display: flex;
  overflow-x: auto;

  .card-item {
    display: block;
    width: 248px;
    height: 94px;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 6px;
    opacity: 1;
    border: 1px solid #ecdfcb;
    padding: 12px;
    margin-left: 12px;

    &:not(:last-child) {
      margin-left: 10px;
    }

    &__title {
      font-size: 15px;
      font-weight: bold;
      color: #333333;

      &__icon {
        flex-shrink: 0;
        display: block;
        width: 16px;
        height: 16px;
      }
    }

    &__content {
      margin-top: 10px;
      font-size: 13px;
      font-weight: 400;
      color: #999999;
    }
  }
}
</style>
