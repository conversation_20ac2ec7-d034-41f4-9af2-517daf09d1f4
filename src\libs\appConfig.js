import { IS_LOCAL_DEV } from "./config";

/**
 * 平台类型常量 - 固定的常量名，不会因为 APP_CURRENT_PLATFORM_MAP 键名变化而改变
 * 这样可以保证代码中使用的常量名始终稳定，只需要修改下面的映射关系
 */
export const PLATFORM_TYPES = {
  ORIGIN: "origin",
  // 可以在这里添加更多平台类型
  // 浅层马甲包
  SHALLOW_MAJIA: "shallowMaji",
};

export const APP_CURRENT_PLATFORM_MAP = {
  [PLATFORM_TYPES.ORIGIN]: "wxf2af270eb52577d7",
  [PLATFORM_TYPES.SHALLOW_MAJIA]: "wx6719a40e4eface31",
};

/**
 * 根据平台类型常量获取对应的平台值
 * @param {string} platformType - 平台类型常量名（如 "ORIGIN"）或平台值（如 "origin"）
 * @returns {string} 平台值（如 "origin"）
 */
function getPlatformValue(platformType) {
  // 如果是平台类型常量名，则从 PLATFORM_TYPES 中获取对应的值
  if (PLATFORM_TYPES.hasOwnProperty(platformType)) {
    return PLATFORM_TYPES[platformType];
  }
  // 如果本身就是平台值，直接返回
  if (Object.values(PLATFORM_TYPES).includes(platformType)) {
    return platformType;
  }
  // 兜底：返回原值
  return platformType;
}

/**
 * 微信小程序 AppID 配置映射表
 * 每个 AppID 对应不同的配置信息，包括请求头等
 */
export const APP_CONFIG_MAP = {
  // 主要小程序配置
  [APP_CURRENT_PLATFORM_MAP[PLATFORM_TYPES.ORIGIN]]: {
    apiVersion: "3.6.8",
    osVersion: "13",
  },
  [APP_CURRENT_PLATFORM_MAP[PLATFORM_TYPES.SHALLOW_MAJIA]]: {
    apiVersion: "3.6.8",
    osVersion: "1302",
  },
  // 默认配置 - 当 AppID 匹配不上时使用
  default: {
    apiVersion: "3.6.8",
    osVersion: "13",
  },
};

export function getAppCurrentPlatform() {
  // #ifdef MP-WEIXIN
  if (IS_LOCAL_DEV) {
    return PLATFORM_TYPES.SHALLOW_MAJIA;
  }
  // #endif

  try {
    const accountInfo = uni.getAccountInfoSync();
    const appId = accountInfo.miniProgram.appId;

    // 查找匹配的平台
    for (const [platform, platformAppId] of Object.entries(
      APP_CURRENT_PLATFORM_MAP
    )) {
      if (platformAppId === appId) {
        console.log("当前平台", platform);
        return platform;
      }
    }

    // 如果没有找到匹配的平台，返回默认值
    return PLATFORM_TYPES.ORIGIN;
  } catch (error) {
    console.error("获取当前平台失败:", error);
    return PLATFORM_TYPES.ORIGIN;
  }
}

/**
 * 根据 AppID 获取对应的配置
 * @param {string} appId - 微信小程序的 AppID
 * @returns {Object} 对应的配置对象
 */
export function getAppConfig(appId) {
  return APP_CONFIG_MAP[appId] || APP_CONFIG_MAP.default;
}

/**
 * 获取所有可用的 AppID 列表
 * @returns {Array<string>} AppID 列表
 */
export function getAvailableAppIds() {
  return Object.keys(APP_CONFIG_MAP).filter((key) => key !== "default");
}

/**
 * 验证 AppID 是否在配置中
 * @param {string} appId - 微信小程序的 AppID
 * @returns {boolean} 是否存在配置
 */
export function isValidAppId(appId) {
  return APP_CONFIG_MAP.hasOwnProperty(appId) && appId !== "default";
}

/**
 * 获取当前小程序对应的配置
 * @returns {Object} 当前小程序的配置对象
 */
export function getCurrentAppConfig() {
  try {
    const accountInfo = uni.getAccountInfoSync();
    const appId = accountInfo.miniProgram.appId;
    const config = getAppConfig(appId);
    console.log("全局根据appId获取配置", appId, config);
    return config;
  } catch (error) {
    console.error("获取当前小程序配置失败:", error);
    return APP_CONFIG_MAP.default;
  }
}

/**
 * 判断当前平台是否匹配指定平台
 * @param {string} targetPlatform - 目标平台标识（可以是平台类型常量名或平台值）
 * @returns {boolean} 是否匹配
 *
 * 用法：
 * if (isPlatform(PLATFORM_TYPES.ORIGIN)) {
 *   // 当前是 origin 平台时执行
 * }
 * if (isPlatform("ORIGIN")) {
 *   // 当前是 origin 平台时执行
 * }
 */
export function isPlatform(targetPlatform) {
  const currentPlatform = getAppCurrentPlatform();
  const targetPlatformValue = getPlatformValue(targetPlatform);
  return currentPlatform === targetPlatformValue;
}

/**
 * 判断当前平台是否不匹配指定平台
 * @param {string} targetPlatform - 目标平台标识（可以是平台类型常量名或平台值）
 * @returns {boolean} 是否不匹配
 *
 * 用法：
 * if (isNotPlatform(PLATFORM_TYPES.ORIGIN)) {
 *   // 当前不是 origin 平台时执行
 * }
 */
export function isNotPlatform(targetPlatform) {
  const currentPlatform = getAppCurrentPlatform();
  const targetPlatformValue = getPlatformValue(targetPlatform);
  return currentPlatform !== targetPlatformValue;
}

/**
 * 判断当前平台是否在指定平台列表中
 * @param {string|Array<string>} targetPlatforms - 目标平台标识或平台数组（可以是平台类型常量名或平台值）
 * @returns {boolean} 是否匹配任一平台
 *
 * 用法：
 * if (isPlatformIn([PLATFORM_TYPES.ORIGIN, PLATFORM_TYPES.OTHER])) {
 *   // 当前是 origin 或 other 平台时执行
 * }
 * if (isPlatformIn(PLATFORM_TYPES.ORIGIN)) {
 *   // 当前是 origin 平台时执行
 * }
 */
export function isPlatformIn(targetPlatforms) {
  const currentPlatform = getAppCurrentPlatform();

  if (Array.isArray(targetPlatforms)) {
    const actualPlatforms = targetPlatforms.map(getPlatformValue);
    return actualPlatforms.includes(currentPlatform);
  } else {
    const targetPlatformValue = getPlatformValue(targetPlatforms);
    return currentPlatform === targetPlatformValue;
  }
}

/**
 * 判断当前平台是否不在指定平台列表中
 * @param {string|Array<string>} targetPlatforms - 目标平台标识或平台数组（可以是平台类型常量名或平台值）
 * @returns {boolean} 是否不匹配任一平台
 *
 * 用法：
 * if (isNotPlatformIn([PLATFORM_TYPES.ORIGIN, PLATFORM_TYPES.OTHER])) {
 *   // 当前不是 origin 和 other 平台时执行
 * }
 */
export function isNotPlatformIn(targetPlatforms) {
  const currentPlatform = getAppCurrentPlatform();

  if (Array.isArray(targetPlatforms)) {
    const actualPlatforms = targetPlatforms.map(getPlatformValue);
    return !actualPlatforms.includes(currentPlatform);
  } else {
    const targetPlatformValue = getPlatformValue(targetPlatforms);
    return currentPlatform !== targetPlatformValue;
  }
}
