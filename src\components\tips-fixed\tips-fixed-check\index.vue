<template>
  <app-login-show v-if="showCheck">
    <div class="check flex flex-space-between flex-align-center">
      <div class="flex flex-align-center">
        <div>
          <u-icon
            class="close-icon"
            color="#fff"
            name="close"
            size="14"
            @click="checkIconClick"
          />
        </div>
        <p class="check-text">
          {{ showCheckText }}
        </p>
      </div>
      <div
        class="check-button"
        @click="check"
      >
        去查看
      </div>
    </div>
  </app-login-show>
</template>

<script>
import { QUESTION_RESULT_FIRST, toConsultation } from "@/libs/turnPages.js";
import AppLoginShow from "@/components/app-components/app-login-show/index.vue";
import UIcon from "@/uview-ui/components/u-icon/u-icon.vue";
import { caseSourceV2GetCaseOrZx } from "@/api/special.js";
import { bindOnHook } from "@/libs/hooks.js";

export default {
  name: "TipsFixedCheck",
  components: { UIcon, AppLoginShow },
  data() {
    return {
      showCheck: false,
      isQuestion: false,
    };
  },
  mounted() {
    bindOnHook.call(this, "onShow", () => {
      this.getQuestionResultFirst();
    });
  },
  watch: {
    showCheck: {
      handler(val) {
        this.$emit("show", val);
      },
      immediate: true,
    },
  },
  computed: {
    /** show文案 */
    showCheckText() {
      return this.isQuestion
        ? "您的问题已发布，平台正分派律师，您可在【咨询】查看进度"
        : "您的案件已发布，平台正分派律师分析案件，您可在【咨询-起诉案件】查看进度";
    },
  },
  methods: {
    checkIconClick() {
      this.showCheck = false;
    },
    /**
     * 点击去查看
     * 跳转至咨询-免费咨询页面
     */
    check() {
      if (this.isQuestion)
      {
        toConsultation();
        return;
      }

      toConsultation({
        index: 2
      });
    },
    /** 判断是否是用户生成问答结果后的首次从问答结果页跳出 */
    getQuestionResultFirst() {
      const isQuestionResult = uni.getStorageSync(QUESTION_RESULT_FIRST);

      if (isQuestionResult) {
        caseSourceV2GetCaseOrZx().then((res) => {
          /** 0 无 1 案源 2 问答 */
          const type = Number(res.data?.type || 0);

          uni.removeStorageSync(QUESTION_RESULT_FIRST);

          this.isQuestion = type === 2;

          this.showCheck = true;

          // 15秒后自动隐藏
          setTimeout(() => {
            this.showCheck = false;
          }, 15000);
        });

        return;
      }

      this.showCheck = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.check {
  padding: 0 16px;
  width: 375px;
  height: 42px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  box-sizing: border-box;
  z-index: 99999;
}

.check-text {
  font-size: 13px;
  font-weight: 400;
  color: #ffffff;
  margin-left: 12px;
}

.check-button {
  flex-shrink: 0;
  background: linear-gradient(180deg, #74d68f 0%, #49bf7f 100%);
  width: 67px;
  height: 26px;
  line-height: 26px;
  text-align: center;
  border-radius: 18px;
  margin-left: auto;
  font-size: 13px;
  font-weight: 400;
  color: #ffffff;
}
</style>
