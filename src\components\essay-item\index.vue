<template>
  <div
    class="essay-item"
    @click="toArticleDetail(data)"
  >
    <p class="title van-ellipsis">
      {{ data.title }}
    </p>
    <div class="essay-item-container flex">
      <div class="content flex-1">
        <p class="detail text-ellipsis-2">
          {{ data.summary }}
        </p>
        <div class="info flex flex-space-between flex-align-center">
          <div class="flex flex-1">
            <p class="tag">
              #{{ data.typeLabel }}
            </p>
            <div class="number-of-read flex flex-align-center">
              <!--                         <img class="icon" src="@/components/essay-item/<EMAIL>" alt="">-->
              <p class="text">
                {{ data.pv }}浏览
              </p>
            </div>
          </div>
          <p class="date">
            {{ data.publishTimeDesc }}
          </p>
        </div>
      </div>
      <img
        v-if="data.thumbnail"
        :src="data.thumbnail"
        alt=""
        class="logo"
      >
      <img
        v-else
        alt=""
        class="logo"
        src="./<EMAIL>"
      >
    </div>
  </div>
</template>

<script>
import { toArticleDetail } from "@/libs/turnPages.js";

export default {
  name: "EssayItem",
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  methods: { toArticleDetail },
};
</script>

<style lang="scss" scoped>
.essay-item{
  background: white;
  padding: 16px 12px 0;
  border-radius: 8px 8px 8px 8px;
  .title{
    font-size: 16px;
    font-weight: 500;
    color: #333333;
  }
  .essay-item-container{
    padding: 16px 0 16px;
    //border-bottom: 0.5px solid #F5F5F5;
    .logo{
      width: 90px;
      height: 60px;
      border-radius: 8px 8px 8px 8px;
      overflow: hidden;
      margin-left: 12px;
    }
    .content{
      overflow: hidden;
      .detail{
        margin-bottom: 9px;
        font-size: 13px;
        font-weight: 400;
        color: #666666;
        overflow: hidden;
      }
      .info{
        .tag{
          font-size: 12px;
          font-weight: 400;
          color: #3887F5;
        }
        .number-of-read{
          padding-left: 8px;
          .icon{
            width:16px;
            height: 16px;
            margin-right: 2px;
          }
          .text{
            font-size: 12px;
            font-weight: 400;
            color: #999999;
          }
        }
        .date{
          font-size: 12px;
          line-height: 17px;
          font-weight: 400;
          color: #999999;
        }
      }
    }
  }
}
</style>
