import { requestCommon, requestCore, requestInfo } from "@/libs/axios";
import { storeRequest } from "@/libs/store-request";

/* 数据字典*/
export const dataDictionary = (data) =>
  requestCore.post("/dataDetail/list", data);

/** 分享数据字典 */
export const shareDataDictionary = () => {
  // #ifdef MP-HJLS
  const groupCode = "HJ_C_MINI_SHARE";
  // #endif
  // #ifndef MP-HJLS || MP_HLZX
  const groupCode = "C_MINI_SHARE";
  // #endif
  return requestCore
    .post("/dataDetail/list", { groupCode: groupCode })
    .then((res) => {
      return res.data.map((item) => {
        try {
          const remark = JSON.parse(item.remark);
          return {
            ...item,
            remark,
          };
        } catch (e) {
          console.log(e);
          return item;
        }
      });
    });
};

export const lawyerSpeciality = () => {
  return dataDictionary({ groupCode: "LAWYER_SPECIALITY" }).then(({ data }) => {
    return data?.map((item) => {
      try {
        return {
          ...item,
          ext: JSON.parse(item.supplementDesc) || {},
        };
      } catch (e) {
        return {
          ...item,
          ext: {},
        };
      }
    });
  });
};

/** 序列化字典中的数据 */
export function serializationDictionary(fun) {
  return fun().then(({ data }) => {
    return data?.map((item) => {
      try {
        return {
          ...item,
          supplementDesc: JSON.parse(item.supplementDesc) || {},
        };
      } catch (e) {
        return {
          ...item,
          supplementDesc: {},
        };
      }
    });
  });
}

/** 关键词分类 */
export function getLawyerSpeciality() {
  return serializationDictionary(() =>
    dataDictionary({ groupCode: "HEADFIGURE_KEYWORD" })
  );
}

/* 意见反馈*/
export const feedbackSave = (data) => requestCore.post("/feedback/save", data);

/* 根据栏目id获取banner*/
export const getBanner = (data) =>
  requestCore.post("/banner/list/column", data);

/* 根据位置获取广告*/
export const getAdvertListPosition = (data) =>
  requestCore.post("/op/list/position", data);

/* 2.1.3公共咨询引导入口*/
export const serviceManegeInfoCommon = (scene) =>
  requestCommon.post(`/info/serviceManege/infoCommon/${scene}`);

// 1.7.0
/** 获取当前用户有效的案源信息 */
export const caseSourceV2GetInfo = (data) =>
  requestInfo.post("/caseSourceV2/getInfo", data);
/* 2.1.3获取公共服务律师*/
export const getCommonLawyer = () =>
  requestCommon.post("/lawyer/getCommon/lawyer");

/* 案源保存*/
export const caseSourceV2Save = (data) =>
  requestInfo.post("/caseSourceV2/save", data, {
    loading: true,
  });

/** 保存案源补充问题描述 */
export const saveProblemDescription = (data) =>
  requestInfo.post("/caseSourceV2/saveProblemDescription", data, {
    loading: true,
  });

/** 落地页-根据用户输入的问题描述，获取【我要咨询】的内容以及案件类型 */
export const consultationAliContent = (data) =>
  requestInfo.post("/landing/page/consultation/aliContent", data);

/* 栏目列表*/

export const getCollumnList = (data) => requestInfo.post("/collumn/list", data);

/* 一级栏目*/
export const getCollumnLevelList = (data) =>
  requestInfo.post("/collumn/collumnLevel", data);

/* 子级栏目*/
export const getCollumnChildrenList = (data) =>
  requestInfo.post("/collumn/collumnChildren", data);

/* 获取父级栏目*/
export const getCollumnParentList = (data) =>
  requestInfo.post("/collumn/collumnParentList", data);

/* 获取栏目面包屑位置*/
export const getCollumnPosition = (data) =>
  requestInfo.post("/collumn/collumnPosition", data);

/* 文章通过标签分页查询*/
export const articleTagPage = (data) =>
  requestInfo.post("/article/tag/page", data);

/* 文章通过标签真分页查询 */
export const articleTagPageNew = (data) =>
  requestInfo.post("/article/tag/page/new", data);
/* 法临端/小程序 -问答列表 */
export const qaMessageList = (data) =>
  requestInfo.post("/qaMessage/pageList", data);
/* 查询当前用户的公共案源列表 */
export const pagePublicCaseSourceInfoByUserId = (data) =>
  requestInfo.post("/caseSourceV2/pagePublicCaseSourceInfoByUserId", data);
/* 我的咨询-案件委托 */
export const pagePublicCaseSourceInfoByUserIdV2 = (data) =>
  requestInfo.post("/caseSourceV2/v2/pagePublicCaseSourceInfoByUserId", data);
/* 获取指定案源的案件资料*/
export const postListCaseSourceV2Result = (caseSourceV2Id) =>
  requestInfo.post(`/caseSourceV2/listCaseSourceV2Result/${caseSourceV2Id}`);
/* 文章详情*/
export const articleCommonDetail = (data, options = {}) =>
  requestInfo.post("/article/common/detail", data, options);

/* 根据案源id获取案源的详情信息, 修改走案源保存接口*/
export const lawyerCaseSourceData = (caseSourceId) =>
  requestCommon.post(
    `/info/userCaseSource/lawyerCaseSourceData/${caseSourceId}`
  );
/* 添加收藏*/
export const userClickCollect = (data) =>
  requestInfo.post("/user/clickCollect", data);

/* 取消收藏*/
export const userRemoveCollect = (data) =>
  requestInfo.post("/user/removeCollect", data);

/* 用户收藏状态*/
export const userCollectUserStatus = (data) =>
  requestInfo.post("/user/collectUserStatus", data);

/* 用户添加点赞*/
export const userClickPraise = (data) =>
  requestInfo.post("/user/clickPraise", data);

/* 用户取消点赞*/
export const userRemovePraise = (data) =>
  requestInfo.post("/user/removePraise", data);

/* 用户点赞状态*/
export const userPraiseStatus = (data) =>
  requestInfo.post("/user/praiseStatus", data);

/* 评论列表*/
export const discussList = (data) => requestInfo.post("/discuss/list", data);

/* 添加评论*/
export const discussAdd = (data) => requestInfo.post("/discuss/add", data);

/* 评论点赞*/
export const discussLike = (data) => requestInfo.post("/discuss/like", data);

/* 热门作者*/
export const authorListHot = (data) =>
  requestInfo.post("/author/list/hot", data);

/* 添加用户举报*/
export const reportAdd = (data) => requestCore.post("/report/add", data);

/* 用户举报类型枚举列表*/
export const reportEnumList = (data) =>
  requestCore.post("/report/enum/list", data);

/* 用户回复列表 */
export const listDiscuss = (data) =>
  requestInfo.post("/author/listDiscuss", data);

/* 获取系统参数*/
export const getCommonConfigKey = (data) =>
  requestCore.post("/common/config/key", data);

/* 获取当前栏目信息*/
export const getCollumnInfo = (data) =>
  requestInfo.post("/collumn/collumnInfo", data);

/* 客户端配置获取*/
export const getClientConfig = (data) =>
  requestCore.post("/common/client/config", data);

/* 微信授权配置*/
export const getUserAuthConfig = (data) =>
  requestCore.post("/user/wechat/auth/config", data);

/* 营销内容查询接口*/
export const getMarketingContent = (data) =>
  requestCore.post(`/marketingContent/${data.id}`);

/* 获取分享图片*/
export const getShareImg = (data) => requestCore.get("/img/base", data);

/* 文章新增*/
export const articleAnswerInsert = (data) =>
  requestInfo.post("/article/answer/insert", data);

/* 获取用户正在咨询的案源信息*/
export const getCaseSource = (data) =>
  requestInfo.post("/caseSource/getCaseSource", data);

/* 案源新增*/
export const caseSourceAdd = (data) =>
  requestInfo.post("/caseSource/add", data);

/* 案源新增校验*/
export const caseSourceCheckInfo = (data) =>
  requestInfo.post("/caseSource/checkInfo", data);

/* 获取深度案源锁定律师信息*/
export const caseSourceGetLockLawyer = (data) =>
  requestInfo.post(`/caseSource/getLockLawyer/${data.caseSourceId}`, data);

/* 获取免费案源锁定律师信息*/
export const caseSourceFreeGetLockLawyer = (data) =>
  requestInfo.post(`/caseSourceFree/getLockLawyer/${data.caseSourceId}`, data);

/* 获取急速案源锁定律师信息*/
export const caseSourcePayGetLockLawyer = (data) =>
  requestInfo.post(`/caseSourcePay/getLockLawyer/${data.caseSourceId}`, data);

/* 未解决案源*/
export const caseSourceNoSolve = (data) =>
  requestInfo.post(`/caseSource/noSolve/${data.caseSourceId}`, data);

/* 已解决案源*/
export const caseSourceSolve = (data) =>
  requestInfo.post(`/caseSource/solve/${data.caseSourceId}`, data);

/* 获取案源状态*/
export const caseSourceGetStatus = (data) =>
  requestInfo.post(`/caseSource/getStatus/${data.caseSourceId}`);

/* 一对一咨询案源校验*/
export const caseSourceAppointCheckInfo = (data) =>
  requestInfo.post("/caseSourceAppoint/checkInfo", data);

/* 一对一咨询案源新增*/
export const caseSourceAppointAdd = (data) =>
  requestInfo.post("/caseSourceAppoint/add", data);

/* 一对一咨询获取用户当前进行中的案源*/
export const caseSourceAppointGetCaseSource = (data) =>
  requestInfo.post(`/caseSourceAppoint/getCaseSource/${data.lawyerId}`, data);

/* 一对一咨询获取当前锁定案例的律师信息*/
export const caseSourceAppointGetLockLawyer = (data) =>
  requestInfo.post(
    `/caseSourceAppoint/getLockLawyer/${data.caseSourceId}`,
    data
  );

/* 一对一咨询已解决*/
export const caseSourceAppointSolve = (data) =>
  requestInfo.post(`/caseSourceAppoint/solve/${data.caseSourceId}`, data);

/* 一对一咨询未解决*/
export const caseSourceAppointNoSolve = (data) =>
  requestInfo.post(`/caseSourceAppoint/noSolve/${data.caseSourceId}`, data);

/* 一对一咨询获取案源状态*/
export const caseSourceAppointGetStatus = (data) =>
  requestInfo.post(`/caseSourceAppoint/getStatus/${data.caseSourceId}`, data);

/* 通过imToken获取律师信息*/
export const lawyerGetByImtokenl = (data) =>
  requestCommon.post("/lawyer/lawyer/imOneLawyer", data);

/* 免费案源新增前校验*/
export const caseSourceFreecheckInfo = (data) =>
  requestInfo.post("/caseSourceFree/checkInfo", data);

/* 根据免费案源ID获取转换的急速订单支付状态*/
export const caseSourceFreeGetPayStatus = (data) =>
  requestInfo.post(`/caseSourceFree/getPayStatus/${data.caseSourceId}`, data);

/* 根据免费案源ID获取对应的待接单深度案源信息*/
export const getInfoByFreeId = (data) =>
  requestInfo.post(
    `/caseSource/getInfoByFreeId/${data.caseSourceFreeId}`,
    data
  );

/* 结束公共深度案源*/
export const finishCaseSource = (data) =>
  requestInfo.post(`/caseSource/finish/${data.caseSourceId}`, data);

/* 急速案源获取案源会话信息*/
export const caseSourcePaygetLockCaseSource = (data) =>
  requestInfo.post(
    `/caseSourcePay/getLockCaseSource/${data.caseSourceId}`,
    data
  );

/* 结束急速案源*/
export const finishCaseSourcePay = (data) =>
  requestInfo.post(`/caseSourcePay/finish/${data.caseSourceId}`, data);

/* 1-1案源获取案源会话信息*/
export const caseSourceAppointgetLockCaseSource = (data) =>
  requestInfo.post(
    `/caseSourceAppoint/getLockCaseSource/${data.caseSourceId}`,
    data
  );

/* 公共免费案源获取案源会话信息*/
export const caseSourceFreegetLockCaseSource = (data) =>
  requestInfo.post(
    `/caseSourceFree/getLockCaseSource/${data.caseSourceId}`,
    data
  );

/* 免费案源新增*/
export const caseSourceFreeadd = (data) =>
  requestInfo.post("/caseSourceFree/add", data);

/* 公共深度案源获取案源会话信息*/
export const caseSourcegetLockCaseSource = (data) =>
  requestInfo.post(`/caseSource/getLockCaseSource/${data.caseSourceId}`, data);

/* 结束1-1案源*/
export const finishCaseSourceAppoint = (data) =>
  requestInfo.post(`/caseSourceAppoint/finish/${data.caseSourceId}`, data);

/* 用户咨询列表 */
export const listConsulation = (data) =>
  requestInfo.post("/caseSource/page", data);

/* 获取用户公共咨询(免费案源)列表*/
export const getCommonSourceFree = (data) =>
  requestCommon.post("/info/caseSourceFree/page", data);

/* 收藏列表 */
export const listCollect = (data) =>
  requestInfo.post("/user/listCollectPage", data);

/* 文章分词分页查询*/
export const commonPage = (data) =>
  requestInfo.post("/article/common/page", data);

/* 获取用户深度案源列表*/
export const getCommonDeepSourceFree = (data) =>
  requestCommon.post("/info/caseSource/page", data);

/* 用户消息列表 */
export const messageList = (data) => requestCore.post("/message/page", data);

export const getCaseSourceV2 = (data) =>
  requestInfo.post("/caseSourceV2/getInfo", data);

// 字典查询
export const getType = (data) => {
  // return requestCore.post('/dataDetail/list', params)
};

// 1.7.0落地页案源保存接口
export const serviceSave = async (data) => {
  const deepData = { ...data };
  if (deepData.serverCode && deepData.serverCode in SERVERCODE) {
    try {
      const res = await infoCommon(deepData.serverCode);
      deepData.serverCode = res.data.serviceCode;
    } catch (e) {
      console.log(e);
    }
  }
  return requestInfo.post("/caseSourceV2/save", deepData, { loading: true });
};

export const getKeyCode = (data) =>
  requestCore.post("/common/config/key", data);

export const servicePayStatus = (data) =>
  requestCommon.post("/order/v2/orderUser/queryOrderStatus", data);

/* 获取用户和律师的服务信息和IM信息*/
export const getServerAndImInfo = (data) =>
  requestCommon.post("/info/caseSourceServerV2/getServerAndImInfo", data);

export const serviceSaveCode = (data) => {
  if (data.serverCode in SERVERCODE) {
    return infoCommon(data.serverCode);
  }
  return requestInfo.post("/serviceManege/info/" + data.serverCode);
};

export const getArea = (data) => requestCore.post("/area/getArea", data); // 省市区县列表查询接口
export const currencyGetAddress = (data) =>
  requestCommon.post("/core/currency/getAddress", data); // 省市区县

/* V2案源问题*/
export const problemsList = (data) =>
  requestCommon.post("/info/caseSourceServerV2/problems", data);

/* 问答列表*/
export const qaMessagePageList = (data) =>
  requestCommon.post("/info/qaMessage/pageList", data);

/* 获取主动咨询结果信息*/
export const getServerInfoV2 = (caseSourceServerV2Id) =>
  requestCommon.post(
    `/info/caseSourceServerV2/getServerInfo/${caseSourceServerV2Id}`
  );

/* 是否可以退款*/
export const orderUserV2Refund = (data) =>
  requestCommon.post("/order/orderUserV2Refund/refundable", data);

/* 查询案源是否有退款*/
export const orderCaseSourceRefund = (data) =>
  requestCommon.post("/order/orderCaseSourceRefund/refundStatus", data);

/* 2.2.4 追问的时候发送，点一次追问，发送一次*/
export const clickPushMessages = (data) =>
  requestCommon.post("/info/caseSourceV2/message/deal", data);

/* 结束*/
export const userCaseSourceComplete = (serverId) =>
  requestCommon.post(`/info/userCaseSource/complete/${serverId}`);

/**
 * 根据服务code 获取服务价格相关信息
 * @param serverCode 服务项code
 * @returns {*}
 */
export const serviceManegeInfo = (serverCode, lawyerId) =>
  requestCommon.post(`/info/serviceManege/info/${serverCode}`, { lawyerId });

/* 案源创建*/
export const createOrder = (data) =>
  requestCommon.post("/order/v2/orderUser/createOrder", data, {
    loading: true,
  });

/* 支付v2*/
export const orderPay = (data) =>
  requestCommon.post("/order/v2/orderUser/pay", data, {
    loading: true,
  });

/* 2.2.0根据类型获取服务商品*/
export const serviceManegeGetByType = (data) =>
  requestCommon.post("/info/serviceManege/getByType", data);
// 2.0.4.1
/* 用户服务评价v2*/
export const evaluateCommon = (data) =>
  requestCommon.post("/info/userEvaluate/evaluate", data);

/* 用户锁定律师*/
export const caseSourceServerV2userLock = (data) =>
  requestCommon.post("/info/caseSourceServerV2/userLock", data);

/* 2.2.2 问答新增底部最新问答列表*/
export const newestEvaluateLists = (data) =>
  requestCommon.post("/info/article/answer/insert/newestEvaluate", data);

/* 2.2.2 问答新增底部最新订单列表*/
export const newestOrderLists = (data) =>
  requestCommon.post("/info/article/answer/insert/newestOrder", data);
/* 上传图片*/
export const uploadImgs = (data) =>
  requestCommon.post("/core/upload/image", data);

/* 获取公众号信息*/
export const getGzhInfo = () => requestCommon.post("/info/user/getGzhInfo");
/* 催一催*/
export const userDingNoticeLawyer = (data) =>
  requestCommon.post("/info/caseSourceServerV2/userDingNoticeLawyer", data);

/**
 * 获取用户最新服务被的接单信息
 * @return {Promise | Promise<unknown>}
 */
export const userLastServerInfo = () =>
  requestInfo.post("/caseSourceServerV2/userLastServerInfo");

/* 法律指南 相关服务分页查询*/
export const lawGuideServerPage = (params) =>
  requestInfo.post("/lawGuide/server/page", params);

/* 法律指南详情（法律意见书）*/
export const lawGuideOpinionDetail = (params) =>
  requestInfo.post("/lawGuide/opinion/detail", params);

/* 获取48内的待评价服务*/
export const caseSourceV2NoEvaluated = (params) =>
  requestInfo.post("/caseSourceV2/noEvaluated", params);

/** 豆包ai问题回答(非流式调用) */
export const aiEngineAsk = (params) =>
  requestCommon.post("/info/aiEngineAsk/db/unStream/ask", params);

/** 开启一条最近关闭的案源 */
export const caseSourceOpen = (data) =>
  requestCommon.post("/info/caseSource/open", data);

/** 获取落地页信息 */
export const getChannelPageInfo = (data) =>
  requestCommon.post("/core/channel/getChannelPageInfo", data);

/** 落地页-根据用户输入的问题描述匹配配置的词 */
export const consultationKey = (data) =>
  requestInfo.post("/landing/page/consultation/key", data);

/** B端H5投放：律师入驻2 */
export const lawyerEnterByPhoneTwo = (data) => requestCommon.post("/paralegal/promotion/v2/lawyerEnterByPhone", data);


// 查询律师费计算器配置表

export const lawyerFeeCalculatorListData = (data) => requestCommon.post("/info/lawyerFeeCalculator/listData", data);
/** 查询合同审查的分类和AB方名称 */
export const contractReviewTypeList = (data) => requestCommon.post("/info/contractReview/typeList", data);

/** 新增合同审查记录 */
export const contractReviewAddContractReview = (data) => requestCommon.post("/info/contractReview/addContractReview", data);

/** 查询合同审查的记录 */
export const contractReviewPageContractReview = (data) => requestCommon.post("/info/contractReview/pageContractReview", data);

/** 查询用户今日合同审查的次数 */
export const contractReviewCountContractReviewTimesToday = (data) => requestCommon.post("/info/contractReview/countContractReviewTimesToday", data);

//上传word
export const uploadWordUrl = process.env.VUE_APP_ENV_BASE_URL + `/core/upload/words`;

// 上传图片
export const uploadImgUrl = process.env.VUE_APP_ENV_BASE_URL + `/core/upload/image`;

// 上传视频
export const uploadVideoUrl = process.env.VUE_APP_ENV_BASE_URL + `/core/upload/video`;

/** 通过id查询合同审查记录 */
export const contractReviewSelectById = (data) => requestCommon.post("/info/contractReview/selectById", data);

//官司案件信息发布
export const lawsuitCasePublish = (data) => requestCommon.post("/info/lawsuitCase/publish", data);

/**
 * 获取最新的案源信息
 * https://showdoc.imlaw.cn/web/#/5/3854
 */
export const lawsuitCaseGetLatestSuitCase = (data) =>
  requestCommon.post("/info/lawsuitCase/getLatestSuitCase", data);

/**
 * 官司案件信息编辑
 * https://showdoc.imlaw.cn/web/#/5/3855
 */
export const lawsuitCaseUpdate = (data) =>
  requestCommon.post("/info/lawsuitCase/update", data);


/**
 * 确认委托律师
 * https://showdoc.imlaw.cn/web/#/5/3856
 */
export const lawsuitCaseConfirmDelegate = (data) =>
  requestCommon.post("/info/lawsuitCase/confirmDelegate", data);

/**
 * 我发布的官司分页
 * https://showdoc.imlaw.cn/web/#/5/3866
 */
export const lawsuitCaseMySuitCase = (data) =>
  requestCommon.post("/info/lawsuitCase/mySuitCase", data);

/**
 * 我发布的官司详情
 * https://showdoc.imlaw.cn/web/#/5/3867
 */
export const lawsuitCaseDetailById = (data) =>
  requestCommon.post("/info/lawsuitCase/detailById", data);

/**
 * 通过服务id查询官司案件信息服务表详情
 * https://showdoc.imlaw.cn/web/#/5/3868
 */
export function lawsuitCaseDetailByServerId(data) {
  return storeRequest({
    api: () => requestCommon.post("/info/lawsuitCase/detailByServerId", data),
    data,
    cacheName: "lawsuitCaseDetailByServerId",
    storeMaxTime: 3000,
  });
}

/**
 * 获取最后一个案子的尾款支付弹窗
 * https://showdoc.imlaw.cn/web/#/5/3870
 */
export const lawsuitCaseGetFinalSuitCasePaymentStatus = (data) =>
  requestCommon.post("/info/lawsuitCase/getFinalSuitCasePaymentStatus", data);


/**
 * 最后一个案子的尾款支付弹窗-已签订
 * https://showdoc.imlaw.cn/web/#/5/3871
 */
export const lawsuitCaseSuitCaseSigned = (data) =>
  requestCommon.post("/info/lawsuitCase/suitCaseSigned", data);

/**
 * 通过系统参数pd_day_time判断是否是夜间咨询1白天0夜间
 * https://showdoc.imlaw.cn/web/#/5/3877
 */
export const commonDayOrNight = () =>
  requestCommon.post("/core/common/dayOrNight");


/**
 * 小程序问律师关键词匹配
 * https://showdoc.imlaw.cn/web/#/5/3876
 */
export const consultationKeyAppletAskLawyer = (data) =>
  requestCommon.post("/info/landing/page/consultation/key/appletAskLawyer", data);
