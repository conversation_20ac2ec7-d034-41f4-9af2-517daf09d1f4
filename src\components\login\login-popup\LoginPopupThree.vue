<template>
  <div>
    <app-popup
      :closeOnClickOverlay="false"
      :duration="0"
      :round="0"
      :safeAreaInsetBottom="false"
      :show="show"
      :zIndex="zIndex"
      bgColor="transparent"
      mode="bottom"
      @open="open"
    >
      <div class="flex items-center pr-[16px] justify-end mb-[16px]">
        <img
          alt=""
          class="w-[24px] h-[24px] block"
          src="../../../pages/submit-question/imgs/apK8Dj.png"
          @click="loginPopupClose"
        >
      </div>
      <div class="w-[375px] position-relative box-border">
        <img
          :src="headerUrl"
          alt=""
          class="w-[375px] block"
          mode="widthFix"
        >
        <div class="absolute bottom-[24px] px-[32px] left-0">
          <div
            class="text-[13px] text-[#999999] flex justify-center items-center mt-[40px]"
          >
            <if t="hjls">
              <img
                v-if="check"
                alt=""
                class="w-[16px] h-[16px] block"
                src="../imgs/<EMAIL>"
                @click="check = false"
              >
              <img
                v-else
                alt=""
                class="w-[16px] h-[16px] block"
                src="../imgs/check.png"
                @click="check = true"
              >
            </if>
            <if f="hjls">
              <img
                v-if="check"
                alt=""
                class="w-[16px] h-[16px] block"
                src="../imgs/check-active.png"
                @click="check = false"
              >
              <img
                v-else
                alt=""
                class="w-[16px] h-[16px] block"
                src="../imgs/check.png"
                @click="check = true"
              >
            </if>
            <span class="ml-[4px]">
              阅读并同意
            </span>
            <span
              class="text-[#F78C3E]"
              @click="protocolClick(1)"
            >
              《隐私政策》
            </span>
            <span
              class="text-[#F78C3E]"
              @click="protocolClick(2)"
            >
              《用户服务协议》
            </span>
          </div>
          <button
            class="w-[311px] h-[44px] mt-[16px] bg-[#082846] rounded-[8px] text-[16px] font-bold text-[#FFFFFF] flex items-center justify-center box-border mx-auto"
            open-type="getPhoneNumber"
            @click="btnHandleClick(false, true)"
            @getphonenumber="getPhoneNumberHandler"
          >
            一键登录
          </button>
          <u-safe-bottom />
        </div>
      </div>
    </app-popup>
  </div>
</template>
<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import LoginPopupMixin from "@/components/login/mixins/LoginPopupMixin";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "LoginPopupThree",
  components: { USafeBottom, AppPopup },
  mixins: [LoginPopupMixin],
  computed: {
    headerUrl() {
      const pic = {
        1: require("@/components/login/imgs/header/1.png"),
        3: require("@/components/login/imgs/header/3.png"),
        8: require("@/components/login/imgs/header/8.png")
      };

      return pic[this.index];
    }
  }
};
</script>
