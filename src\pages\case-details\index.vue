<template>
  <div class="case-details">
    <my-litigate-container
      :subtitle="subtitle"
      class="pd-bm-16"
      position="bottom"
      title="案件资料"
    >
      <app-descriptions
        :amountGradeRes="amountGradeRes"
        :appealTypeRes="appealTypeRes"
        :columns="columns"
        :dataSource="caseSourceDetail"
      />
      <!-- 2.1.6 新增功能，案件详情列表 -->
      <div class="case-details-prefect-done">
        <case-details-prefect-done
          v-if="isPerfectCaseInformation"
          :data="caseSourceData"
        />
      </div>
      <div
        v-if="!isPerfectCaseInformation"
        class="btn"
        @click="showCaseData"
      >
        完善案件资料
      </div>
      <div class="h-[44px]" />
      <u-safe-bottom />
    </my-litigate-container>
  </div>
</template>

<script>
import MyLitigateContainer from "@/components/my-litigate/my-litigate-container";
import AppDescriptions from "@/pages/case-details/components/app-descriptions";
import { userCaseSourceDetail } from "@/api/my-consultation-new";
import { dataDictionary, postListCaseSourceV2Result } from "@/api";
import CaseDetailsPrefectDone from "@/pages/case-details/components/case-details/case-details-prefect-done";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
// import { caseDetailsPageBuriedPoint,caseDetailsPerfectCaseBuriedPoint } from './js/index'

/**
 * 案件详情界面
 * @FilePath pages\case-details\index.vue
 */
export default {
  name: "CaseDetails",
  components: {
    CaseDetailsPrefectDone,
    AppDescriptions,
    MyLitigateContainer,
    USafeBottom
  },
  data() {
    return {
      routeObj: {}, // 路由参数
      /** 案源详情数据 */
      caseSourceDetail: {},
      // detailList: [],
      /** 案件诉求 */
      appealTypeRes: [],
      /** 涉及金额 */
      amountGradeRes: [],
      // * 2.1.6 新增
      /** 案件详情列表 */
      caseSourceData: []
    };
  },
  onLoad(options) {
    this.routeObj = options;
  },
  onShow() {
    /** 获取案源详情 */
    userCaseSourceDetail(this.routeObj.id).then(res => {
      this.caseSourceDetail = res.data;
      console.log("caseSourceDetail:", this.caseSourceDetail);
      // this.detailList = res.data.records || []

      // const sourcePage2DataStatus = res.data?.perfectionQuestion ? 1 : 0

      // 如果已经完事了案件资料，则获取值
      if (res.data?.perfectionQuestion) {
        this.getCaseSourceData();
      }
      // // 2.10.0 埋点 页面访问PV、UV埋点
      // caseDetailsPageBuriedPoint({sourcePage2DataStatus})
    });

    /** 案件诉求 */
    dataDictionary({
      groupCode: "APPEAL_TYPE"
    }).then(res => {
      this.appealTypeRes = res.data;
    });

    /** 涉及金额 */
    dataDictionary({
      groupCode: "AMOUNT_GRADE"
    }).then(res => {
      this.amountGradeRes = res.data;
    });
  },
  computed: {
    /** 详情列表 */
    columns() {
      const baseColumns = [
        {
          title: "委托人",
          dataIndex: "userName"
        },
        {
          title: "联系电话",
          dataIndex: "phone"
        },
        {
          title: "案件类型",
          dataIndex: "typeLabel"
        },
        {
          title: "案件金额",
          dataIndex: "amountGrade"
        },
        {
          title: "所在城市",
          dataIndex: "regionName"
        },
        {
          title: "案件诉求",
          dataIndex: "appealyType"
        },
        {
          title: "案件描述",
          dataIndex: "info"
        },
        {
          title: "补充描述",
          dataIndex: "problemDescription"
        }
        // {
        //   title: '是否加急',
        //   dataIndex: 'urgent',
        // },
      ];

      if (this.isNeedPerfectInformation)
        return [
          ...baseColumns,
          {
            title: "相关资料",
            dataIndex: "perfectionQuestion"
          }
        ];

      return baseColumns;
    },
    /** 咨询详情信息列表 */
    caseSourceDetailRecords() {
      return this.caseSourceDetail?.records || [];
    },
    /** 是否完善案件资料 */
    isPerfectCaseInformation() {
      return this.caseSourceDetail?.perfectionQuestion;
    },
    /** 展示的副标题 */
    subtitle() {
      return this.caseSourceDetail?.perfectionQuestion
        ? "（法律顾问将尽快与您联系）"
        : "（法律顾问将尽快与您联系，请完善案件资料）";
    },
    /** 是否需要完善资料 */
    isNeedPerfectInformation() {
      return this.caseSourceDetail?.needPerfectionQuestion;
    }
  },
  methods: {
    getCaseSourceData() {
      postListCaseSourceV2Result(this.routeObj.id).then(res => {
        this.caseSourceData = res.data || [];
        console.log("caseSourceData:", this.caseSourceData);
      });
    },
    /** 查看案件资料 */
    showCaseData() {
      // // 点击完善案件资料埋点
      // caseDetailsPerfectCaseBuriedPoint()
      uni.navigateTo({
        url: `/pages/case-details/case-details-prefect/index?id=${this.routeObj.id}`
      });
    }
  }
};
</script>

<style lang="less" scoped>
.case-details {
  height: 100vh;
  background: #fff;
  padding-bottom: 40px;
  .case-details-prefect-done {
    margin-top: 25px;
  }
  .btn {
    width: 343px;
    height: 44px;
    border-radius: 90px 90px 90px 90px;
    border: 1px solid #3887f5;
    text-align: center;
    line-height: 44px;
    color: #3887f5;
    font-size: 16px;
    position: fixed;
    bottom: 40px;
    left: 16px;
  }
}

.case-choose:not(:last-child) {
  margin-bottom: 16px;
}
</style>
