<template>
  <div>
    <slot />
    <app-popup
      :animation="false"
      :popupStyle="transitionStyleProps"
      :safeAreaInsetBottom="false"
      :show="show"
      bgColor="transparent"
      mode="top"
      @close="close"
    >
      <slot />
      <view class="geo-wrapper">
        <div
          class="geo-wrapper-title flex flex-align-center flex-space-between"
        >
          <div class="geo-current">
            当前：{{ currentSelect || "暂无定位" }}
          </div>
          <div
            class="geo-reset flex flex-align-center"
            @click="handleGetLocation"
          >
            <if f="hjls">
              <img
                alt=""
                class="location-icon"
                src="@/pages/index/imgs/location-blue.png"
              >
            </if>
            <if t="hjls">
              <img
                alt=""
                class="location-icon"
                src="@/pages/index/imgs/222.png"
              >
            </if>
            {{ locationText }}
          </div>
        </div>
        <div class="geo-wrapper-con flex">
          <div class="left-province">
            <scroll-view
              class="scroll-view"
              scrollY="true"
            >
              <div
                v-for="item in province"
                :key="item.code"
                class="geo-line fontStyle flex flex-align-center"
                @click="provinceClick(item)"
              >
                {{ item.name }}
                <div
                  v-if="item.code === provinceActive"
                  class="active"
                />
              </div>
            </scroll-view>
          </div>
          <div class="right-city">
            <scroll-view
              class="scroll-view"
              scrollY="true"
            >
              <div
                v-for="item in city"
                :key="item.code"
                class="geo-line fontStyle flex flex-align-center flex-space-between"
                @click="cityClick(item)"
              >
                {{ item.name }}
                <if f="hjls">
                  <img
                    v-if="item.code === cityActive"
                    alt=""
                    class="active"
                    src="@/pages/index/imgs/city-check.png"
                  >
                </if>
                <if t="hjls">
                  <img
                    v-if="item.code === cityActive"
                    alt=""
                    class="active"
                    src="@/pages/index/imgs/check-green.png"
                  >
                </if>
              </div>
            </scroll-view>
          </div>
        </div>
      </view>
    </app-popup>
  </div>
</template>

<script>
import { getLocation } from "@/libs/getLocation.js";
import { getArea } from "@/api/index.js";
import AppPopup from "@/components/app-components/app-popup/index.vue";
import { bindOnHook } from "@/libs/hooks";

export default {
  name: "PopupLocation",
  components: { AppPopup },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    transitionStyleProps: {
      type: Object,
      default: () => ({})
    },
    nationwide: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      province: [],
      city: [],
      provinceActive: null,
      cityActive: null,
      currentSelect: "暂无定位",
      cityCode: null,
      locationText: "重新定位"
    };
  },
  mounted() {
    this.getAreaRequest();
    this.reqGetLocation();

    bindOnHook.call(this, "onShow", () => {
      this.reqGetLocation();
    });
  },
  methods: {
    /* 定位*/
    handleGetLocation() {
      this.$emit("handleGetLocation");
      this.reqGetLocation();
    },
    /* 请求定位*/
    reqGetLocation() {
      // 获取定位
      getLocation()
        .then(({ cityCode, cityName, provinceCode, provinceName }) => {
          this.currentSelect = cityName;
          this.locationText = "重新定位";
          this.provinceActive = null;
          this.cityActive = null;
          // this.$emit('setCity',{name: city})
          this.$emit("getLocationCallback", { name: cityName });
          this.$emit("update:show", false);
          this.$emit("setCity", {
            name: cityName,
            code: cityCode,
            provinceName,
            provinceCode
          });
        })
        .catch(() => {
          this.locationText = "无法获取定位";
          // 默认全国
          this.$emit("setCity", { name: "全国", code: "" });
        });
    },
    /* 获取地图*/
    getAreaRequest(type = 1, code = null, name) {
      if (code === "全国") {
        this.city = [{ code: "", type: 2, name: "全国" }];
      } else {
        getArea({ type, code }).then(({ data }) => {
          if (type === 1) {
            this.province = data;
            if (this.nationwide)
              this.province.unshift({ code: "全国", type: 1, name: "全国" });
          }
          if (type === 2) {
            /* 有些城市是自治区 要特殊判断*/
            this.city =
              this.nationwide && data.length > 1
                ? [
                  { code: code, type: 2, name: "不限", provinceName: name },
                  ...data
                ]
                : data;
          }
        });
      }
    },
    /* 省级选择*/
    provinceClick(item) {
      this.provinceActive = item.code;
      this.getAreaRequest(2, item.code, item.name);
    },
    /* 城市选择*/
    cityClick({ name, code, provinceName }) {
      this.cityActive = code;
      this.currentSelect = name;
      this.$emit("update:show", false);
      this.$emit("setCity", { name, code, provinceName });
    },
    close() {
      this.$emit("update:show", false);
    }
  }
};
</script>

<style lang="scss" scoped>
.geo-wrapper {
  background: #f5f5f7;
  border-radius: 0 0 16px 16px;

  &-title {
    height: 44px;
    padding: 0 16px;
    border-bottom: 1px solid #eee;

    .geo-current {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
    }

    .geo-reset {
      font-size: 14px;
      font-weight: 400;
      color: #3887f5;

      .location-icon {
        width: 16px;
        height: 16px;
        margin-right: 2px;
      }
    }
  }

  &-con {
    .scroll-view {
      height: 352px;
    }

    .fontStyle {
      font-size: 14px;
      font-weight: 400;
      color: #333333;
    }

    .left-province {
      width: 130px;

      .geo-line {
        height: 44px;
        position: relative;
        padding-left: 16px;

        .active {
          position: absolute;
          width: 4px;
          height: 20px;
          background: #3887f5;
          border-radius: 20px 20px 20px 20px;
          left: 0;
        }
      }
    }

    .right-city {
      width: 245px;
      border-left: 1px solid #eee;

      .geo-line {
        padding: 12px 0 16px 12px;
        box-sizing: border-box;
        height: 44px;

        .active {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}
</style>
