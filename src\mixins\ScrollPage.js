import { getClientRect } from "@/libs/tools";

export default {
  data() {
    return {
      /** 是否计算滚动 */
      isScroll: true,
      lockTimer: null,
      /** 滚动条的距离 */
      scrollTop: 0
    };
  },
  methods: {
    /**
     * 引入一个锁的概念，锁住 isScroll，防止滚动过程中触发滚动事件
     */
    lockScroll() {
      this.isScroll = false;

      // 每次触发时清除上一次的定时器
      clearTimeout(this.lockTimer);

      this.lockTimer = setTimeout(() => {
        this.isScroll = true;
      }, 400);
    },
    /** 计算元素距顶部的距离 */
    getRect(selector) {
      return new Promise(async resolve => {
        try {
          const res = await getClientRect(selector);

          const header = await getClientRect.call(this, ".header");

          // 这里使用进度条距顶部的距离 + 元素对视窗的距离 - 头部的高度
          return resolve(res.top + this.scrollTop - header?.height || 0);
        } catch (e) {
          return resolve(0);
        }
      });
    }
  }
};
