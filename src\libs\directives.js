/**
 * Vue自定义指令集合
 */

import { getAppCurrentPlatform } from "./appConfig";

/**
 * v-platform 指令
 */
const platformDirective = {
  inserted(el, binding) {
    const currentPlatform = getAppCurrentPlatform();
    const targetPlatforms = binding.value;

    console.log("currentPlatform", currentPlatform);

    let shouldShow = false;

    if (Array.isArray(targetPlatforms)) {
      // 数组形式，匹配任一平台即显示
      shouldShow = targetPlatforms.some(
        (platform) => platform === currentPlatform
      );
    } else {
      // 单个平台字符串
      shouldShow = targetPlatforms === currentPlatform;
    }

    if (!shouldShow) {
      // 如果不匹配当前平台，隐藏元素
      el.style.display = "none";
      // 添加一个标记，表示这个元素被平台指令控制
      el.setAttribute("data-platform-hidden", "true");
    }
  },

  update(el, binding) {
    console.log("currentPlatform", currentPlatform);
    // 当指令值更新时重新检查
    const currentPlatform = getAppCurrentPlatform();
    const targetPlatforms = binding.value;

    let shouldShow = false;

    if (Array.isArray(targetPlatforms)) {
      shouldShow = targetPlatforms.some(
        (platform) => platform === currentPlatform
      );
    } else {
      shouldShow = targetPlatforms === currentPlatform;
    }

    if (shouldShow) {
      // 恢复显示
      if (el.getAttribute("data-platform-hidden") === "true") {
        el.style.display = "";
        el.removeAttribute("data-platform-hidden");
      }
    } else {
      // 隐藏元素
      el.style.display = "none";
      el.setAttribute("data-platform-hidden", "true");
    }
  },
};

/**
 * v-platform-not 指令（反向控制）
 */
const platformNotDirective = {
  inserted(el, binding) {
    const currentPlatform = getAppCurrentPlatform();
    const excludePlatforms = binding.value;

    let shouldHide = false;

    if (Array.isArray(excludePlatforms)) {
      shouldHide = excludePlatforms.some(
        (platform) => platform === currentPlatform
      );
    } else {
      shouldHide = excludePlatforms === currentPlatform;
    }

    if (shouldHide) {
      el.style.display = "none";
      el.setAttribute("data-platform-not-hidden", "true");
    }
  },

  update(el, binding) {
    const currentPlatform = getAppCurrentPlatform();
    const excludePlatforms = binding.value;

    let shouldHide = false;

    if (Array.isArray(excludePlatforms)) {
      shouldHide = excludePlatforms.some(
        (platform) => platform === currentPlatform
      );
    } else {
      shouldHide = excludePlatforms === currentPlatform;
    }

    if (shouldHide) {
      el.style.display = "none";
      el.setAttribute("data-platform-not-hidden", "true");
    } else {
      if (el.getAttribute("data-platform-not-hidden") === "true") {
        el.style.display = "";
        el.removeAttribute("data-platform-not-hidden");
      }
    }
  },
};

export { platformDirective, platformNotDirective };
