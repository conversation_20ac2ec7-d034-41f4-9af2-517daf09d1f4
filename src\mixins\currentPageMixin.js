import { getCurrentPageRoute } from "@/libs/turnPages";

export default {
  data() {
    return {
      /** 记录当前路由信息，因为由Vuex触发时，会触发其它未销毁页面 */
      fullPath: getCurrentPageRoute().fullPath
    };
  },
  mounted() {
    this.fullPath = getCurrentPageRoute().fullPath;

    // ! 这里是为了兼容百度，因为百度用上面的方法获取到当前的路由为空
    // #ifdef MP-BAIDU
    this.$nextTick(() => {
      this.fullPath = getCurrentPageRoute().fullPath;
    });
    // #endif
  },
  methods: {
    /** 判断是否是当前组件所在的页面 */
    isNotCurrentPage() {
      return this.fullPath !== getCurrentPageRoute().fullPath;
    }
  }
};
