<template>
  <div>
    <login-have-order-not-pay
      v-model="showOrderNotPay"
      :serviceInfo="serviceInfo"
    />
    <login-not-have-order
      v-model="showNotHaveOrder"
      :serviceInfo="serviceInfo"
    />
    <consult-popup v-model="consultPopupState" />
    <paid-popup
      v-model="paidPopupState" 
      :serviceInfo="serviceInfo"
    />
  </div>
</template>

<script>
import { serviceManegeInfoCommon } from "@/api";
import { buryPointChannelBasics } from "@/api/burypoint";
import { caseSourceV2GetCaseOrZx } from "@/api/special";
import { newPopupV2 } from "@/api/user";
import ConsultPopup from "@/components/login/components/ConsultPopup.vue";
import LoginHaveOrderNotPay from "@/components/login/components/HaveOrderNotPay.vue";
import LoginNotHaveOrder from "@/components/login/components/NotHaveOrder.vue";
import { BURY_POINT_CHANNEL_TYPE, POINT_CODE } from "@/enum/burypoint";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath";
import { sceneType } from "@/libs/config";
import PaidPopup from "@/components/login/components/paidPopup.vue";

export default {
  name: "ServerDialog",
  components: { PaidPopup, ConsultPopup, LoginNotHaveOrder, LoginHaveOrderNotPay },
  data() {
    return {
      /** 控制弹窗 */
      showOrderNotPay: false,
      showNotHaveOrder: false,
      serviceInfo: {},
      loginServerData: {},
      /* 注册未留资状态新增弹窗引导*/
      consultPopupState: false,
      //   紧急需求 3.6.5 已支付弹窗
      paidPopupState: false
    };
  },
  computed: {
    popupType() {
      return Number(this.loginServerData.popupType);
    },
    isLogin() {
      return !!this.$store.state.user.token;
    },
    /** 当前组件是否展示 */
    componentsShow() {
      return (
        this.showOrderNotPay || this.showNotHaveOrder || this.consultPopupState || this.paidPopupState
      );
    },
  },
  watch: {
    componentsShow: {
      handler(val) {
        if (!val) {
          console.log(val, "componentsShow");
          this.$store.commit("user/SET_LOGIN_POPUP_SHOW", false);
        }
      },
    },
  },
  mounted() {
    // 如果没有登录，则不显示弹窗
    if (!this.isLogin) {
      this.$store.commit("user/SET_LOGIN_POPUP_SHOW", false);
      return;
    }

    this.getLoginPopInfo();
  },
  methods: {
    // 获取登录后的提示弹窗
    getLoginPopInfo() {
      newPopupV2().then(({ data = {} }) => {
        this.loginServerData = data;
        this.judgePopup();
      });
    },
    /* 获取注册未留资状态新增弹窗引导状态 新用户并且没有留资*/
    getConsultPopupState() {
      const isNewUser = this.$store.getters["user/getUserInfo"].customNewUser;
      caseSourceV2GetCaseOrZx().then((res) => {
        /** 0 无 1 案源 2 问答 */
        const caseType = Number(res.data?.type || 0);
        const newUser = this.$store.getters["user/newUser"];
        // 咨询过并且没有付费
        this.consultPopupState = isNewUser && caseType === 0 && !newUser;
      });
    },
    close() {
      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_GUIDE_CLOSE_CLICK,
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
        type: 1,
      });
    },
    /** 判断弹窗 */
    async judgePopup() {
      try {
        switch (this.popupType) {
        // 有订单未支付
        case 11:
          this.serviceInfo = this.loginServerData.ext || {};
          // https://lanhuapp.com/web/#/item/project/product?tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&pid=ba6dc38e-6057-4fb5-b4bb-da2b8ffa0524&versionId=5a14f693-e27e-4ace-baae-ad6ae78c51d7&docId=0f8d005a-96fa-4f22-a36d-a0069a08f9e7&docType=axure&pageId=b9d22b0047704e84a39a61e6e2ab4343&image_id=0f8d005a-96fa-4f22-a36d-a0069a08f9e7&parentId=304ee83b-9ef2-4ff3-b313-da3a1b31d37b
          // 现在进入未支付页面
          // this.$store.commit("user/SET_LOGIN_POPUP_SHOW", false);
          // turnToBePay(this.serviceInfo);
          this.showOrderNotPay = true;
          break;
          // 留资无订单未支付
        case 12:
          this.serviceInfo = await serviceManegeInfoCommon(
            sceneType.login_jj
          ).then(({ data }) => {
            return data;
          });

          buryPointTransformationPath.add(5046);

          buryPointChannelBasics({
            code:
                "LAW_APPLET_INDEX_PAGE_V2_POP_UP_LEFT_DATA_NO_PAY_TO_PAY_POP_UP_PAGE",
            type: 1,
            behavior: BURY_POINT_CHANNEL_TYPE.CK,
          });

          this.showNotHaveOrder = true;
          break;
        case 13:
          // this.serviceInfo = this.loginServerData.ext || {};
          // this.paidPopupState = true;
          break;
        default:
          this.getConsultPopupState();
          break;
        }
      } catch (e) {
        console.error(e);
      }
    },
  },
};
</script>
