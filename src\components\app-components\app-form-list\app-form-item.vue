<template>
  <div
    :class="[leftClass]"
    :style="[formLineAddStyle]"
    class="form-line"
    @click="$emit('click', $event)"
  >
    <p
      :style="[$u.addStyle(titleStyle)]"
      class="title flex"
    >
      <span>{{ label }}</span><i
        v-if="required"
        class="form-line-required"
      >*</i>
    </p>
    <div class="flex flex-align-center flex-space-end w-full">
      <div class="w-full">
        <slot />
      </div>
      <u-icon
        v-if="showIcon"
        :color="iconColor"
        class="pd-lt-4"
        name="arrow-right"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "AppFormItem",
  mixins: [uni.$u.mixin],
  props: {
    label: {
      type: String,
      default: "",
    },
    /** 是否显示左边的必填星号，只作显示用，具体校验必填的逻辑，请在rules中配置 */
    required: {
      type: Boolean,
      default: false,
    },
    labelPosition: {
      type: String,
      default: "left",
    },
    /** 绑定的值 */
    prop: {
      type: String,
      default: "",
    },
    /** 是否显示右边的图标 */
    showIcon: {
      type: Boolean,
      default: true,
    },
    /** 图标颜色 */
    iconColor: {
      type: String,
      default: "#333333",
    },
    /** 是否显示下边框 */
    borderBottom: {
      type: Boolean,
      default: true,
    },
    formLineStyle: {
      type: [Object, String],
      default: () => ({}),
    },
    titleStyle: {
      type: [Object, String],
      default: () => ({}),
    },
  },
  computed: {
    leftClass() {
      if (this.labelPosition === "left")
        return "flex flex-space-between flex-align-center form-line-left left-class";

      return "";
    },
    formLineAddStyle() {
      const style = uni.$u.addStyle(this.formLineStyle);

      if (!this.borderBottom) {
        return {
          ...style,
          borderBottom: "none",
        };
      }

      return style;
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      // 父组件的实例
      this.updateParentData();
    },
    // 获取父组件的参数
    updateParentData() {
      // 此方法写在mixin中
      this.getParentData("AppForm");
    },
    getParentData(parentName = "AppForm") {
      /**
       * @description 获取父组件的参数，因为支付宝小程序不支持provide/inject的写法
       this.$parent在非H5中，可以准确获取到父组件，但是在H5中，需要多次this.$parent.$parent.xxx
       这里默认值等于undefined有它的含义，因为最顶层元素(组件)的$parent就是undefined，意味着不传name
       值(默认为undefined)，就是查找最顶层的$parent
       *  @param {string|undefined} name 父组件的参数名
       */
      function $parent(name = undefined) {
        let parent = this.$parent;
        console.log(parent?.$options.name, "this.$parent");
        // 通过while历遍，这里主要是为了H5需要多层解析的问题
        while (parent) {
          // 父组件
          if (parent.$options && parent.$options.name !== name) {
            // 如果组件的name不相等，继续上一级寻找
            parent = parent.$parent;
          } else {
            return parent;
          }
        }
        return false;
      }

      // 避免在created中去定义parent变量
      if (!this.parent) this.parent = {};
      // 这里的本质原理是，通过获取父组件实例(也即类似u-radio的父组件u-radio-group的this)
      // 将父组件this中对应的参数，赋值给本组件(u-radio的this)的parentData对象中对应的属性
      // 之所以需要这么做，是因为所有端中，头条小程序不支持通过this.parent.xxx去监听父组件参数的变化
      // 此处并不会自动更新子组件的数据，而是依赖父组件u-radio-group去监听data的变化，手动调用更新子组件的方法去重新获取
      this.parent = $parent.call(this, parentName);
      if (this.parent.children) {
        // 如果父组件的children不存在本组件的实例，才将本实例添加到父组件的children中
        this.parent.children.indexOf(this) === -1 &&
          this.parent.children.push(this);
      }
      if (this.parent && this.parentData) {
        // 历遍parentData中的属性，将parent中的同名属性赋值给parentData
        Object.keys(this.parentData).map((key) => {
          this.parentData[key] = this.parent[key];
        });
      }
      console.log(this.parent, "this.parent=================");
    },
  },
};
</script>

<style lang="scss" scoped>
.form-line {
  padding: 13px 0;
  border-bottom: 0.5px solid #f5f5f5;
  color: #333333;
  font-size: 14px;

  &-required {
    font-size: 14px;
    color: #f00;
    margin-left: 4px;
  }

  .title {
    flex-shrink: 0;
    padding-bottom: 12px;
    color: #333333;
    font-size: 16px;
    font-weight: bold;
  }
}

.form-line-left {
  .title {
    padding-bottom: 0;
    font-size: 16px;
    font-weight: bold;
  }
}

.left-class {
  .form-radio-more-item {
    background: #f5f5f7 !important;
    border: 0 !important;

    &:nth-child(n) {
      margin-right: 15px;
    }

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
