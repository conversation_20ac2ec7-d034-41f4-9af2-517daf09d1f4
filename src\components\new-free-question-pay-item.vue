<template>
  <div
    class="bg-[#F1F8FF] !bg-[#F0FAF6_ifhjls] rounded-[8px] p-[10px] box-border"
  >
    <div>
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div class="font-bold text-[16px] text-[#333333]">
            {{ serviceData.serviceName || "" }}
          </div>
          <div class="text-[12px] text-[#999999] ml-[6px]">
            /{{ serviceData.serviceNum }}{{ serviceData.unitLabel }}
          </div>
        </div>
        <div class="flex items-center">
          <div
            class="h-[17px] px-[4px] flex items-center justify-center rounded-[2px] border-[1px] border-solid border-[#EB4738] box-border text-[11px] text-[#EB4738] mr-[2px]"
          >
            已优惠¥{{
              (serviceData.originalPrice - serviceData.servicePrice)
                | amountFilter
            }}
          </div>
          <div class="font-bold text-[16px] text-[#F34747] ml-[2px]">
            ¥{{ serviceData.servicePrice | amountFilter }}
          </div>
        </div>
      </div>
      <div class="flex items-center justify-between mt-[6px]">
        <div class="flex items-center space-x-[6px]">
          <div
            v-for="(item,index) in tags"
            :key="index"
            class="h-[19px] px-[4px] flex items-center justify-center rounded-[2px] border-[1px] border-solid border-[#3887F5] box-border text-[12px] text-[#3887F5]"
          >
            {{ item }}
          </div>
        </div>
        <div class="text-[11px] text-[#999999] [text-decoration-line:line-through]">
          原价￥{{ serviceData.originalPrice | amountFilter }}
        </div>
      </div>
      <div class="flex items-center justify-between mt-[8px]">
        <div class="text-[12px] text-[#555555]">
          <div
            v-for="(item, index) in infos"
            :key="index"
            class="text-[12px] text-[#999999] flex items-center mt-[2px]"
          >
            <img
              alt=""
              class="mr-[2px] block w-[10px] h-[10px] shrink-0"
              src="@/pages/submit-question/to-be-paid/img/123.png"
            >
            <div>
              {{ item }}
            </div>
          </div>
        </div>
        <div
          class="w-[90px] h-[28px] bg-[linear-gradient(_90deg,_#FA700D_0%,_#F34747_100%)] rounded-[48px] font-bold text-[14px] text-[#FFFFFF] flex items-center justify-center"
          @click="handleClick"
        >
          去咨询
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: "NewFreeQuestionPayItem",
  props: {
    serviceData: {
      type: Object,
      default: () => ({}),
    },
    /** 按钮文案 */
    buttonText: {
      type: String,
      default: "去咨询",
    },
    infoDefault: {
      type: Array,
      default: () => ["平均60s内电话回拨", "服务时间内不限沟通次数"],
    },
    tagsDefault: {
      type: Array,
      default: () => ["未服务可退款", "极速响应"],
    }
  },
  computed: {
    serviceDesc() {
      try {
        return JSON.parse(this.serviceData.serviceDesc);
      } catch (e) {
        console.log(e);
        return {};
      }
    },
    tags(){
      console.log(this.serviceDesc.tags || this.tagsDefault, "this.serviceDesc.tags || this.tagsDefault");
      return this.serviceDesc.tags || this.tagsDefault;
    },
    infos(){
      console.log(this.serviceDesc.info || this.infoDefault, "this.serviceDesc.info || this.infoDefault");
      return this.serviceDesc.info || this.infoDefault;
    }
  },
  methods: {
    handleClick() {
      this.$emit("click", this.serviceInfo);
    }
  },
};
</script>
