---
trigger: always_on
---

Don`t help me git commit
Verify Information: Always verify information from the context before presenting it. Do not make assumptions or speculate without clear evidence.
File-by-File Changes: Make changes file by file and give the user a chance to spot mistakes.
No Apologies: Never use apologies.
No Understanding Feedback: Avoid giving feedback about understanding in comments or documentation.
No Whitespace Suggestions: Don't suggest whitespace changes.
No Summaries: Do not provide unnecessary summaries of changes made. Only summarize if the user explicitly asks for a brief overview after changes.
No Inventions: Don't invent changes other than what's explicitly requested.
No Unnecessary Confirmations: Don't ask for confirmation of information already provided in the context.
Preserve Existing Code: Don't remove unrelated code or functionalities. Pay attention to preserving existing structures.
Single Chunk Edits: Provide all edits in a single chunk instead of multiple-step instructions or explanations for the same file.
No Implementation Checks: Don't ask the user to verify implementations that are visible in the provided context. However, if a change affects functionality, provide an automated check or test instead of asking for manual verification.
No Unnecessary Updates: Don't suggest updates or changes to files when there are no actual modifications needed.
No Current Implementation: Don't discuss the current implementation unless the user asks for it or it is necessary to explain the impact of a requested change.
Check Context Generated File Content: Remember to check the context-generated file for the current file contents and implementations.
Use Explicit Variable Names: Prefer descriptive, explicit variable names over short, ambiguous ones to enhance code readability.
Follow Consistent Coding Style: Adhere to the existing coding style in the project for consistency.
Prioritize Performance: When suggesting changes, consider and prioritize code performance where applicable.
Security-First Approach: Always consider security implications when modifying or suggesting code changes.
Error Handling: Implement robust error handling and logging where necessary.
Modular Design: Encourage modular design principles to improve code maintainability and reusability.
Version Compatibility: Ensure suggested changes are compatible with the project's specified language or framework versions. If a version conflict arises, suggest an alternative or provide a backward-compatible solution.
Avoid Magic Numbers: Replace hardcoded values with named constants to improve code clarity and maintainability.
Consider Edge Cases: When implementing logic, always consider and handle potential edge cases.
Use Assertions: Include assertions wherever possible to validate assumptions and catch potential errors early.