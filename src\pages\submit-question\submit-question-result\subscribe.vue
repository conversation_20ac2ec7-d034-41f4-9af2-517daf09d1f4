<template>
  <div>
    <!-- #ifdef MP-WEIXIN -->
    <div class="subscribe flex flex-space-center">
      <div class="wrapper">
        <img
          alt=""
          class="img"
          src="@/pages/index/imgs/subscribe.png"
        >
        <div class="title">
          问题发布成功
        </div>
        <div class="subTitle">
          智能匹配律师解答中...
        </div>
        <div class="info">
          您的问题，我们已通知平台律师，律师会在稍后几分钟为您解答，为了第一时间收到消息通知，建议您订阅小程序消息哦
        </div>
        <div
          class="btn"
          @click="subscribe"
        >
          点击订阅
        </div>
        <div class="btn-text">
          开启订阅，第一时间看到律师回复
        </div>
      </div>
    </div>
    <!-- #endif -->
  </div>
</template>

<script>
import { requestSubscribeMessage } from "@/libs/tools.js";

export default {
  name: "Subscribe",
  onShow() {
    // 清除订阅消息状态
    uni.removeStorageSync("subscribe");
  },
  methods: {
    subscribe() {
      requestSubscribeMessage().then((res) => {
        uni.navigateBack();
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.subscribe{
  padding-top: 73px;
  text-align: center;
  height: 100vh;
  box-sizing: border-box;
  background:#fff;
  .wrapper{
    .img{
      width:120px;
      height:120px;
    }
    .title{
      font-size: 18px;
      font-weight: bold;
      color: #333333;
    }
    .subTitle{
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      margin-top: 8px;
    }
    .info{
      margin-top: 8px;
      padding: 0 32px;
      font-size: 13px;
      font-weight: 400;
      color: #999999;
      text-align: left;
    }
    .btn{
      width: 200px;
      height: 36px;
      background: #3887F5;
      border-radius: 68px;
      opacity: 1;
      font-size: 14px;
      font-weight: bold;
      color: #FFFFFF;
      line-height:36px;
      margin:40px auto 8px;
    }
    .btn-text{
      font-size: 12px;
      font-weight: 400;
      color: #3887F5;
    }
  }
}
</style>
