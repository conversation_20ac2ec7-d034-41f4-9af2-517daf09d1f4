<template>
  <div
    class="w-[375px] bg-[#F0F8FF] pt-[60px]"
    @click="jump"
  >
    <div
      class="w-[343px] h-[182px] bg-[#FFFFFF] rounded-[8px] position-relative box-border mx-auto pt-[90px]"
    >
      <img
        :src="lawyerInfoData.imgUrl"
        alt=""
        class="w-[90px] h-[90px] rounded-[12px] block absolute -top-[35px] absolute-center-x"
      >
      <div
        class="w-[138px] h-[30px] block absolute absolute-center-x top-[31px] box-border"
      >
        <img
          src="@/assets/imgs/<EMAIL>"
          alt=""
          class="background-image"
        >
        <div
          class="font-bold text-[14px] text-[#AD532D] flex justify-center pt-[4px]"
        >
          {{ realName }}律师
        </div>
      </div>
      <div
        class="h-[21px] bg-[linear-gradient(_123deg,_#FFF4CE_0%,_#FFF4CE_100%)] flex items-center rounded-tl-none rounded-br-none rounded-tr-[8px] rounded-bl-[8px] absolute right-0 top-0 text-[11px] text-[#B37C1F] px-[5px] box-border"
      >
        累计{{ lawyerInfoData.fansNum }}位用户推荐
      </div>
      <div class="flex items-center justify-center space-x-[14px]">
        <div
          v-for="(item, index) in lawyerTags"
          :key="index"
          class="h-[23px] bg-[#F5F5F7] rounded-[4px] flex items-center justify-center box-border px-[8px] py-[3px]"
        >
          <img
            :src="item.icon"
            alt=""
            class="w-[14px] h-[14px] block mr-[2px]"
          >
          <div class="text-[12px] text-[#666666]">
            {{ item.field }}
          </div>
        </div>
      </div>
      <div class="flex items-center mt-[16px]">
        <div
          class="w-[114px] h-[48px] flex justify-center items-center flex-col"
        >
          <div class="font-bold text-[18px] text-[#333333]">
            98%
          </div>
          <div class="text-[12px] text-[#666666] mt-[2px]">
            好评率
          </div>
        </div>
        <i class="w-[1px] h-[32px] bg-[#EEEEEE]" />
        <div
          class="w-[114px] h-[48px] flex justify-center items-center flex-col"
        >
          <div class="font-bold text-[18px] text-[#333333]">
            {{ lawyerInfoData.serviceNum }}
          </div>
          <div class="text-[12px] text-[#666666] mt-[2px]">
            服务人数
          </div>
        </div>
        <i class="w-[1px] h-[32px] bg-[#EEEEEE]" />
        <div
          class="w-[114px] h-[48px] flex justify-center items-center flex-col"
        >
          <div class="font-bold text-[18px] text-[#333333]">
            {{ lawyerInfoData.workTime }}
          </div>
          <div class="text-[12px] text-[#666666] mt-[2px]">
            执业年限
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { toLawyerHome } from "@/libs/turnPages";
import { shortLawyerName } from "@/libs/filter";

export default {
  name: "ImLawyerInfo",
  props: {
    lawyerInfo: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    realName() {
      // 如果超过了4个字，就截取4个字，并加省略号
      return shortLawyerName(this.lawyerInfoData.realName);
    },
    workField() {
      return this.workFields.join(" ");
    },
    workFields() {
      return this.lawyerInfoData?.workField || [];
    },
    /** 律师相关从业信息 */
    lawyerInfoData() {
      return this.lawyerInfo?.info || {};
    },
    lawyerTags() {
      return (
        this.lawyerInfo?.info?.lawyerTags?.split(",").slice(0, 3) || []
      ).map((item, index) => {
        const icons = [
          require("@/assets/imgs/pjcdfb.png"),
          require("@/assets/imgs/qz75wn.png"),
          require("@/assets/imgs/4csmf4.png")
        ];

        return {
          icon: icons[index],
          field: item
        };
      });
    }
  },
  methods: {
    jump() {
      toLawyerHome({
        id: this.lawyerInfoData.id
      });
    }
  }
};
</script>
