<template>
  <div>
    <app-popup
      :round="16"
      :show="value"
      :zIndex="99999"
      position="bottom"
      @close="$emit('input', false)"
    >
      <div class="pay-confirm-popup">
        <div class="server-box flex flex-align-center">
          <img
            :src="selectedService.icon"
            alt=""
            class="img"
          >
          <div class="content flex flex-space-between">
            <div class="title flex">
              <span>{{ selectedService.serviceName }}</span>
              <span class="price"><span class="dw">¥</span>
                {{ selectedService.servicePrice | amountFilterOne }}</span>
            </div>
            <div class="info flex">
              <span>规格：{{ selectedService.serviceNum
              }}{{ selectedService.unitLabel }}</span>
              <span class="old-p">原价¥{{
                selectedService.originalPrice | amountFilterOne
              }}</span>
            </div>
          </div>
        </div>
        <div class="server-info-box">
          <div class="title">
            服务说明
          </div>
          <div class="info">
            {{ selectedService.info }}
          </div>
          <img
            alt=""
            class="ser-img"
            mode="widthFix"
            src="@/pages/rapid-consultation-confirm-order/my-consultation/assets/<EMAIL>"
          >
        </div>
        <div
          class="pay-btn"
          @click.stop="btnClick"
        >
          去支付
          <img
            alt=""
            class="tip"
            src="@/components/fastPayPopup/tip2.png"
          >
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE, POINT_CODE } from "@/enum/burypoint";

export default {
  name: "FastPayPopup",
  components: { AppPopup },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    selectedService: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          buryPointChannelBasics({
            code: POINT_CODE.LAW_APPLET_CONSULT_GOODS_DETAIL_TWICE_CONFIRM_PAGE,
            behavior: BURY_POINT_CHANNEL_TYPE.CK,
            type: 1
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    btnClick() {
      buryPointChannelBasics({
        code:
          POINT_CODE.LAW_APPLET_CONSULT_GOODS_DETAIL_TWICE_CONFIRM_TO_PAY_CLICK,
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
        type: 1
      });

      this.$emit("input", false);
      this.$emit("click", this.selectedService);
    }
  }
};
</script>

<style lang="scss" scoped>
.pay-confirm-popup {
  background: #f5f5f7;
  padding: 16px 16px 24px 16px;
  border-radius: 16px 16px 0 0;
  position: relative;

  .server-box {
    padding: 12px;
    margin-bottom: 12px;
    background: #ffffff;
    border-radius: 8px;

    .img {
      width: 44px;
      height: 44px;
      border-radius: 4px;
      flex-shrink: 0;
    }

    .content {
      flex-direction: column;
      margin-left: 12px;
      width: 100%;

      .title {
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        margin-bottom: 6px;

        .price {
          font-size: 15px;
          font-weight: 500;
          margin-left: auto;
          color: #eb4738;

          .dw {
            font-size: 12px;
          }
        }
      }

      .info {
        font-size: 12px;
        font-weight: 400;
        color: #999999;

        .old-p {
          font-size: 12px;
          font-weight: 400;
          color: #999999;
          text-decoration: line-through;
          margin-left: auto;
        }
      }
    }
  }

  .server-info-box {
    padding: 16px;
    margin-bottom: 24px;
    background: #ffffff;
    border-radius: 8px;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      margin-bottom: 12px;
    }

    .info {
      font-size: 13px;
      font-weight: 400;
      color: #666;
      margin-bottom: 12px;
    }

    .ser-img {
      width: 100%;
    }
  }

  .pay-btn {
    height: 44px;
    line-height: 44px;
    background: #f78c3e;
    border-radius: 22px 22px 22px 22px;
    opacity: 1;
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
    text-align: center;
    position: relative;

    .tip {
      width: 82px;
      height: 30px;
      position: absolute;
      right: 120px;
      top: -15px;
    }
  }
}
</style>
