<template>
  <div>
    <div
      v-for="(labelItem,index) in columns"
      :key="index"
      :style="{'justifyContent': justify }"
      class="app-descriptions-box"
    >
      <template v-if="renderData(labelItem)">
        <div
          :style="{'minWidth': labelWidthComputed}"
          class="app-descriptions-title"
        >
          {{ labelItem.title }}
        </div>
        <div
          :style="{ 'maxWidth': valueWidthComputed }"
          class="app-descriptions-value"
        >
          <!-- {{renderData(labelItem)}} -->
          <u-parse :content="renderData(labelItem)" />
        </div>
      </template>
    </div>
    <!-- <template #phone="{ text }">
          <span>{{ text | formatPhoneNumber }}</span>
        </template>
        <template #appealyType="{ text }">
          <span>{{ formatAppealyType(text) }}</span>
        </template>
        <template #amountGrade="{ text }">
          <span>{{ formatAmountGrade(text) }}</span>
        </template>
        <template #urgent="{ text }">
          <span :class="{ 'text-red': !text }">{{
            text ? "已加急" : "未加急"
          }}</span>
        </template>
        <template #perfectionQuestion="{ text }">
          <span :class="{ 'text-red': !text }">{{
            text ? "已完善" : "待完善"
          }}</span>
        </template> -->
  </div>
</template>

<script>
/**
 * 封装的通用描述列表组件，JSX语法
 * 也可以使用JSON配合插槽的方式
 * 插槽中会传出一个对象：
 * @type {text:string, record:object}
 * text是当前值，record是整个对象
 * <AUTHOR>
 * @Version 2.10.0
 * @Description 该版本改动信息
 * @Date 2022/4/23
 * @FilePath components\app-descriptions\index.vue
 */
export default {
  name: "AppDescriptions",
  props: {
    /**
     * 内容的描述
     */
    columns: {
      type: Array,
      default: () => [],
    },
    /**
     *   涉及金额
     */
    amountGradeRes: {
      type: Array,
      default: () => [],
    },
    /**
     *   涉及金额
     */
    appealTypeRes: {
      type: Array,
      default: () => [],
    },
    /**
     * 需要展示的数据
     */
    dataSource: {
      type: Object,
      default: () => {},
    },
    /** 水平排列方式 */
    justify: {
      type: String,
      default: "start",
      validator: (val) =>
        ["start", "end", "center", "space-around", "space-between"].includes(
          val
        ),
    },
    /** title的宽度，会转换成vw */
    labelWidth: {
      type: [Number, String],
      default: "85px",
    },
  },
  computed: {
    /** 将title的宽度转换成vw */
    labelWidthComputed() {
      const pxToVw = (px) => `${(px / 375) * 100}vw`;

      if (typeof this.labelWidth === "number") {
        return pxToVw(this.labelWidth);
      }

      const width = parseInt(this.labelWidth.replace("px", ""));

      return pxToVw(width);
    },
    valueWidthComputed(){
      return `calc(100% - ${this.labelWidthComputed})`;
    }
  },
  methods: {
    formatPhoneNumber(val) {
      return val?.replace(/(\d{3})(\d{0,4})(\d{0,4})/, "$1 $2 $3") || "";
    },
    /**
     * 转换案件诉求，没匹配到时返回无
     * @param {number} appealyType
     */
    formatAppealyType(appealyType) {
      return (
        this.appealTypeRes?.find((item) => Number(item.value) === appealyType)
          ?.label || "无"
      );
    },
    /**
     * 转换涉及金额
     * @param {number} amountGrade
     */
    formatAmountGrade(amountGrade) {
      return this.amountGradeRes?.find(
        (item) => Number(item.value) === amountGrade
      )?.label;
    },
    /**
     * 从dataSource取出指定的数据
     */
    getDataInLabel(labelItem) {
      console.log("dataSource:", this.dataSource);
      const { dataSource } = this;
      const { dataIndex } = labelItem;
      const data = dataSource[dataIndex] ?? undefined;
      const changeArr = ["phone", "appealyType", "amountGrade", "urgent", "perfectionQuestion"];
      let dataFomat = "";
      if(dataIndex === "phone"){
        dataFomat = this.formatPhoneNumber(data);
      }
      if(dataIndex === "appealyType"){
        dataFomat = this.formatAppealyType(data);
      }
      if(dataIndex === "amountGrade"){
        dataFomat = this.formatAmountGrade(data);
      }
      if(dataIndex === "urgent"){
        dataFomat = data ? "已加急" : "<span style=\"color: #EB4738\">未加急</span>";
      }
      if(dataIndex === "perfectionQuestion"){
        dataFomat = data ? "已完善" : "<span style=\"color: #EB4738\">待完善</span>";
      }
      return [changeArr.includes(dataIndex) ? dataFomat : data, dataSource];
    },
    /**
     * 条件渲染，如果label中有render函数则不再从dataSource中取得数据
     * * 如果对应的插槽中有值，则也不再从dataSource中取得数据
     */
    renderData(labelItem) {
      const data = this.getDataInLabel(labelItem);

      if (!data) return null;
      // 如果有对应的插槽
      // if (this.$scopedSlots[labelItem.dataIndex]) {
      //   return this.$scopedSlots[labelItem.dataIndex]({
      //     text: data[0],
      //     record: data[1],
      //   })
      // }

      // 如果label中含有render函数，则将当前对应的值传入render函数中
      // console.log('labelItem.render:', labelItem.render)
      // if (labelItem.render) {
      //   console.log('labelItem.render:', labelItem.render)
      //   console.log('labelItem.render(...data):', labelItem.render(...data))
      //   return labelItem.render(...data)
      // }

      // 将 \n 转换为 <br> 标签以支持换行显示
      let result = data[0];
      if (typeof result === "string") {
        result = result.replace(/\n/g, "<br>");
      }
      return result;
    },
  },
};
</script>

<style lang="scss" scoped>
.app-descriptions {
  &-box {
    display: flex;
    gap: 10px;
    margin-bottom: 16px;
  }

  &-title {
    font-size: 14px;
    font-weight: 400;
    color: #999999;
    line-height: 16px;
  }

  &-value {
    font-size: 14px;
    font-weight: 400;
    color: #222222;
    line-height: 16px;
    word-wrap: break-word;
  }
  .text-red {
    color: #EB4738;
  }
}
</style>
