<template>
  <app-popup :round="16" :show="show" :zIndex="9990">
    <div class="app-address-base">
      <!-- 标题部分 -->
      <div class="app-address-base-title">
        <p class="cancel" @click="handleCancel">
          取消
        </p>
        <p class="title">
          所在城市
        </p>
        <p class="confirm" @click="handleConfirm">
          确定
        </p>
      </div>
      <div class="app-address-base-body">
        <!-- 左边的部分 -->
        <scroll-view class="left" scrollY>
          <div
            v-for="item in leftColumns"
            :key="item.code"
            :class="{ 'left-item--active': item.code === leftActiveCode.code }"
            class="left-item text-ellipsis"
            @click="handleLeftClick(item)"
          >
            {{ item.name }}
          </div>
        </scroll-view>
        <!-- 右边的部分 -->
        <scroll-view class="right" scrollY>
          <div
            v-for="item in rightColumns"
            :key="item.code"
            class="right-item"
            @click="handleRightClick(item)"
          >
            <div class="flex flex-align-center flex-space-between">
              <span>{{ item.name }}</span>
              <!-- 选中的状态 -->
              <img
                v-if="item.code === rightActiveCode.code"
                alt=""
                class="right-item-selected"
                src="@/assets/common/selected.png"
              />
            </div>
          </div>
        </scroll-view>
      </div>
    </div>
  </app-popup>
</template>

<script>
import { currencyGetAddress, getArea } from "@/api/index.js";
import AppPopup from "@/components/app-components/app-popup/index.vue";
import { getLocation } from "@/libs/getLocation";

export default {
  name: "AppAddressBase",
  components: { AppPopup },
  props: {
    /** 控制弹窗的显示隐藏 */
    visible: {
      type: Boolean,
      default: false
    },
    value: {
      type: [String, Object],
      default: ""
    },
    /** 是否默认当前城市 */
    isDefaultCity: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      /** 左侧列表的值 */
      leftColumns: [],
      /** 右侧列表的值 */
      rightColumns: [],
      /** 左边选中的值 */
      leftActiveCode: {},
      /** 右边选中的值 */
      rightActiveCode: {}
    };
  },
  computed: {
    show: {
      get() {
        return this.visible;
      },
      set(val) {
        console.log(val);
        console.log(this.visible);
        this.$emit("update:visible", val);
      }
    }
  },
  async created() {
    try {
      this.handleDefaultCity();
      const { data } = await this.getArea(1);
      this.leftColumns = data;
      // 默认选中第一个
      this.leftActiveCode = this.leftColumns[0];
      this.setRightColumns(data[0].code);
    } catch (e) {
      console.log(e);
    }
  },
  methods: {
    /** 默认当前城市 */
    handleDefaultCity() {
      if (this.isDefaultCity)
        getLocation()
          .then(data => {
            this.$emit("input", data);
          })
          .catch(() => {
            currencyGetAddress().then(res => {
              this.$emit("input", res.data);
            });
          });
    },
    getArea(type, code) {
      return getArea({ type, code });
    },
    /** 给 rightColumns 赋值 */
    setRightColumns(code) {
      this.getArea(2, code).then(res => {
        this.rightColumns = res.data || [];
        // 默认选中第一个
        this.rightActiveCode = this.rightColumns[0] || {};
      });
    },
    /** 选择左边的值后触发的事件 */
    handleLeftClick(item) {
      this.leftActiveCode = item;
      this.setRightColumns(item.code);
    },
    /** 选择右边的值后触发的事件 */
    handleRightClick(item) {
      this.rightActiveCode = item;
    },
    /** 点击确定按钮 */
    handleConfirm() {
      this.show = false;
      this.$emit("input", {
        cityCode: this.rightActiveCode.code,
        provinceName: this.rightActiveCode.provinceName,
        cityName: this.rightActiveCode.name,
        provinceCode: this.leftActiveCode.code
      });
    },
    /** 点击取消按钮 */
    handleCancel() {
      this.show = false;
      this.$emit("cancel");
    }
  }
};
</script>

<style lang="scss" scoped>
.app-address-base {
  overflow: hidden;
  color: #333;

  &-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 14px;

    .confirm {
      font-size: 14px;
      font-weight: 400;
      color: #3887f5;
    }

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #222222;
    }

    .cancel {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
    }
  }

  &-body {
    display: grid;
    grid-template-columns: 130px 245px;
    height: 442px;

    .left {
      background: #f5f5f7;
      height: 442px;

      &-item {
        width: 100%;
        box-sizing: border-box;
        padding-left: 16px;
        line-height: 44px;
        height: 44px;
        background: #f5f5f7;
        opacity: 1;

        &--active {
          background: #ffffff;
        }
      }
    }

    .right {
      background: #fff;
      height: 442px;

      &-item {
        @extend .left-item;
        padding-right: 16px;
        background: #ffff;

        &-selected {
          display: block;
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}
</style>
