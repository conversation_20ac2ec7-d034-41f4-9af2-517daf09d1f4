<template>
  <div class="rounded-[8px] flex items-center justify-between relative">
    <div
      v-if="data.buyStatus === 1"
      class="px-[8px] py-[3px] bg-[#EBF7F0] rounded-tl-none rounded-br-[8px] rounded-tr-none rounded-bl-none text-[12px] text-[#22BF7E] absolute -left-[16px] -top-[16px]"
    >
      已购买
    </div>
    <div class="flex">
      <img
        src="img/1.png"
        alt=""
        class="w-[32px] h-[32px] block mr-[8px]"
      >
      <div>
        <div class="text-[15px] w-[180px] font-bold text-[#333333] text-ellipsis">
          {{ data.contractName }}
        </div>
        <div class="flex items-center mt-[4px]">
          <div class="text-[12px] text-[#3887F5]">
            #{{ data.contractTypeLabel }}
          </div>
          <div class="flex text-[12px] text-[#999999] ml-[12px]">
            <div>{{ data.downloadCount }}</div>
            <div class="ml-[2px]">
              次下载
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="w-[88px] h-[32px] bg-[#EBF3FE] rounded-[49px] text-[14px] text-[#3887F5] flex items-center justify-center shrink-0"
      @click="handleClick"
    >
      <div>查看文档</div>
    </div>
  </div>
</template>
<script>
import { toContractTemplatesDetailPage } from "@/libs/turnPages";

export default {
  name: "ContractLawyerItem",
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    handleClick(){
      toContractTemplatesDetailPage({ id: this.data.id });
    }
  }
};
</script>
