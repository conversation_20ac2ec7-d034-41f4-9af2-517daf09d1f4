<template>
  <div class="position-relative">
    <img
      alt=""
      class="w-full h-[146px] absolute top-0 left-0 -z-10"
      src="@/pages/rapid-consultation-confirm-order/mall/img/DAlCibhZ.png"
    >
    <div class="h-[106px]" />
    <entrance-ask-lawyer
      clickAskLawyerBuryPoint="LAW_APPLET_LAWYER_NEWEST_ANSWER_SECOND_PAGE_ONLINE_ASK_CLICK"
      clickPhoneConsultBuryPoint="LAW_APPLET_LAWYER_NEWEST_ANSWER_SECOND_PAGE_PHONE_CONSULT_CLICK"
    />
    <div class="mt-[12px] mx-[12px]">
      <entrance-lawyer-topic
        :bgImgIndex="2"
        :dataSource="rankList"
        clickLawyerCardBuryPoint="LAW_APPLET_LAWYER_NEWEST_ANSWER_SECOND_PAGE_LAWYER_CARD_CLICK"
        title="一问多答·月度解答榜"
      />
    </div>
    <u-sticky>
      <div class="bg-[#f5f5f7] pt-[20px]">
        <div class="text-[18px] font-bold text-[#333333] px-[16px]">
          全部解答
        </div>
        <app-tabs-other
          v-model="typeValue"
          :list="tabList"
          bgClassName="bg-FFFFFF"
          @change="changeTab"
        />
      </div>
    </u-sticky>
    <div class="px-[16px] min-h-[500px]">
      <div>
        <div
          v-for="(item, index) in quesInfoList"
          :key="index"
          class="mb-[12px] last:mb-0"
        >
          <lawyer-card
            :data="item"
            radius="8px"
          />
        </div>
      </div>
    </div>
    <u-safe-bottom />
  </div>
</template>

<script>
import LawyerCard from "@/pages/index/component/lawyerCard.vue";
import { qaMessagePageList } from "@/api/index.js";
import { indexQaMessageTypeValue } from "@/api/common.js";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { toAskLawyer } from "@/libs/turnPages";
import { isArrNull } from "@/libs/basics-tools.js";
import EntranceAskLawyer from "@/pages/lawyer-home/components/EntranceAskLawyer.vue";
import EntranceLawyerTopic from "@/pages/lawyer-home/components/EntranceLawyerTopic.vue";
import { lawyerNewRank } from "@/api/lawyer";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import USticky from "@/uview-ui/components/u-sticky/u-sticky.vue";
import AppTabsOther from "@/pages/lawyer-home/components/app-tabs/other.vue";
import { setNavigationBarAlipay } from "@/libs/tools";

export default {
  name: "LawyerHomeAllAnswer",
  components: {
    AppTabsOther,
    USticky,
    EntranceLawyerTopic,
    EntranceAskLawyer,
    USafeBottom,
    LawyerCard
  },
  data() {
    return {
      quesInfoList: [],
      /** tab数据 */
      tabList: [],
      /** 总数 */
      pageTotal: 0,
      pageParams: {
        /** 页码 */
        currentPage: 1,
        /** 每页条数 */
        pageSize: 20
      },
      typeValue: "",
      isEnd: false,
      /** 排行榜数据 */
      rankList: []
    };
  },
  onLoad(query) {
    setNavigationBarAlipay({
      frontColor: "#000000",
      backgroundColor: "#DFF3EC"
    });

    buryPointChannelBasics({
      code: "LAW_APPLET_LAWYER_NEWEST_ANSWER_SECOND_PAGE",
      behavior: BURY_POINT_CHANNEL_TYPE.VI,
      type: 1
    });

    this.typeValue = query.typeValue || null;
    this.getRankList();
    this.getTabData();
  },
  onReachBottom() {
    this.scrollToLower();
  },
  onHide() {
    clearTimeout(this.timer);
  },
  methods: {
    /** 获取排行榜数据 */
    getRankList() {
      lawyerNewRank({
        manType: 5,
        type: 12,
        rankType: 2
      }).then(({ data = [] }) => {
        this.rankList = data;
      });
    },
    toAskLawyer() {
      toAskLawyer();
    },
    /**
     * 获取优秀律师解答
     */
    getQaMessagePageList() {
      if (this.isEnd) return;
      qaMessagePageList({
        qaMessageRequestType: 2,
        ...this.pageParams,
        sortType: 0,
        typeValue: this.typeValue === null ? "" : Number(this.typeValue)
      }).then(({ code, data: { records = [] } }) => {
        if (code === 0) {
          const list = records || [];
          this.quesInfoList = [...this.quesInfoList, ...list];
          /* 判断有没有加载完全部数据*/
          this.isEnd =
            isArrNull(list) || list.length < this.pageParams.pageSize;
        }
      });
    },
    /** 获取tab栏数据 */
    getTabData() {
      indexQaMessageTypeValue().then(({ data = [] }) => {
        this.tabList = data;

        // this.tabList.unshift({
        //   name: "全部",
        //   value: null,
        // });

        this.typeValue = this.typeValue || this.tabList[0].value;
        this.getQaMessagePageList();
      });
    },
    /** 更改标签后重新获取数据 */
    changeTab(value) {
      this.pageParams.currentPage = 1;
      this.quesInfoList = [];
      this.typeValue = value;
      this.isEnd = false;
      this.getQaMessagePageList();
    },
    scrollToLower() {
      if (this.isEnd) return;
      this.pageParams.currentPage++;
      this.getQaMessagePageList();
    }
  }
};
</script>
