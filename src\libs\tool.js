// 获取路由指定参数
import { getCaseSourceV2, getKeyCode, servicePayStatus } from "@/api/index";
import { isNull, isNumber, isObjNull } from "@/libs/basics-tools";
import { globalInfo, tencentAmapKey } from "@/libs/config";
// import { Toast } from "vant";
import qs from "qs";

export const getQueryVariable = (variable) => {
  var query = window.location.search.substring(1);
  var vars = query.split("&");
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split("=");
    if (pair[0] == variable) {
      return pair[1];
    }
  }
  return false;
};

// 第三方方法
export const thirdPartyEventCallback = () => {
  /* 极准*/
  if (getQueryVariable("yocJsCallBack") === "jizhun") {
    // eslint-disable-next-line no-undef
    JTrack.track({
      event: "event1",
    });
  }
};

export const getCaseSourceV2String = () => {
  return new Promise((resolve, reject) => {
    getCaseSourceV2()
      .then(({ data }) => {
        const caseSource = data || {};
        Object.keys(caseSource).forEach((key) => {
          caseSource[key] = isNumber(caseSource[key])
            ? String(caseSource[key])
            : caseSource[key];
        });
        resolve(caseSource);
      })
      .catch(() => {
        reject({});
      });
  });
};
// 添加script
export const addScript = (src) => {
  return new Promise((resolve) => {
    const script = document.createElement("script");
    script.type = "text/javascript";
    script.src = src;
    document.body.appendChild(script);
    script.onload = () => resolve();
  });
};

// 防抖
export const debounce = (func, wait = 0) => {
  if (typeof func !== "function") {
    throw new TypeError("need a function arguments");
  }
  let timeid = null;
  let result;
  return function () {
    let context = this;
    let args = arguments;

    if (timeid) {
      clearTimeout(timeid);
    }
    timeid = setTimeout(function () {
      result = func.apply(context, args);
    }, wait);

    return result;
  };
};

/**
 * 将分转换成为元，如果小数位数少于2位，则补全2位
 * 如果小数位数大于2位，则保留原始值
 * @param {number | string | undefined | null} val 
 * @returns {string}
 */
export const priceNumber = (val) => {
  if (val) {
    const numVal = Number(val);
    
    // 使用大整数运算避免浮点数精度问题
    // 将输入值乘以100再除以10000，等同于除以100但避免浮点数运算
    const integerPart = Math.floor(numVal / 100);
    const decimalPart = numVal % 100;
    
    // 重构为字符串拼接的方式
    const result = integerPart + (decimalPart / 100);
    
    // 使用parseFloat确保结果为数字，然后转换为字符串处理精度
    // 先用较高精度处理
    const resultNum = parseFloat(result.toFixed(10)); 
    const resultString = resultNum.toString();
    const decimalIndex = resultString.indexOf(".");
    
    if (decimalIndex === -1 || resultString.length - decimalIndex - 1 < 2) {
      // 没有小数点或小数位数少于2位，使用toFixed(2)
      return resultNum.toFixed(2);
    } else {
      // 有2位或更多小数位，返回原始值的字符串形式
      return resultString;
    }
  }
  return "0.00";
};

export const formatTime = (val) => {
  let valNum = Number(val);

  if(valNum === 0) return 0;

  let timeStr = "";
  if (valNum / 3600 < 1) {
    timeStr = valNum / 60 + "分钟";
  } else if (valNum / 3600 < 25) {
    timeStr = valNum / 3600 + "小时";
  } else {
    timeStr = valNum / (3600 * 24) + "天";
  }
  return timeStr;
};

/** 1.7.0获取系统配置的服务时间 案源默认有效时间(秒) 3天 */
export const getServiceCaseSetTime = () => {
  return new Promise((resolve) => {
    getKeyCode({ paramName: "case_source_v2_valid_time" }).then(({ data }) => {
      if (data) {
        resolve({ data: formatTime(data.paramValue), value: data.paramValue });
      } else {
        resolve({ data: "3天", value: 259200 });
      }
    });
  });
};
/** 1.7.0获取系统配置的服务时间 案源付费默认有效时间(秒) 7天 */
export const getServiceCasePaySetTime = () => {
  return new Promise((resolve) => {
    getKeyCode({ paramName: "case_source_v2_pay_valid_time" }).then(
      ({ data }) => {
        if (data) {
          resolve({
            data: formatTime(data.paramValue),
            value: data.paramValue,
          });
        } else {
          resolve({ data: "7天", value: 604800 });
        }
      }
    );
  });
};

export const isMobile = () => {
  return navigator.userAgent.match(
    /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
  );
};

/**
 * @description: 查询订单
 * @author:djsong
 * @date:2022/5/11
 * @param:
 * @return:
 */
export const orderTracking = (
  data,
  maxQueryNum = 8,
  isShowToast = true,
  returnType = "Promise",
  callback = () => {}
) => {
  let i = maxQueryNum;
  const param = {
    orderId: data.orderId,
  };
  let timer;
  // let toast = {
  //     clear() {
  //     }
  // }

  if (isShowToast) {
    // toast = Toast.loading({
    //     duration: 0, // 持续展示 toast
    //     forbidClick: true,
    //     message: '正在查询订单'
    // });
  }
  const clearQueryOrder = () => {
    clearInterval(timer);
  };
  const startQueryOrder = () => {
    return new Promise((resolve, reject) => {
      timer = setInterval(() => {
        servicePayStatus(param)
          .then(({ data }) => {
            i--;
            if (data.status === 1001) {
              resolve();
              // toast.clear()
              clearQueryOrder();
            }
            if (i === 0) {
              clearQueryOrder();
              // toast.clear()
              reject();
            }
            callback(i);
          })
          .catch(() => {
            clearQueryOrder();
            reject();
          });
      }, 1000);
    });
  };
  if (returnType === "Promise") return startQueryOrder();
  else if (returnType === "Function")
    return {
      startQueryOrder,
      clearQueryOrder,
    };
};

/* 获取下载页地址*/
export const getDownloadPageAddress = (data = {}) => {
  if (isObjNull(data)) {
    return (
      globalInfo.downloadPageAddress +
      "?channelId=" +
      localStorage.getItem("channelId")
    );
  } else {
    return globalInfo.downloadPageAddress + "?" + qs.stringify(data);
  }
};

export const second = (value) => {
  var theTime = parseInt(value); // 秒
  var middle = 0; // 分
  var hour = 0; // 小时

  if (theTime >= 60) {
    middle = parseInt(theTime / 60);
    theTime = parseInt(theTime % 60);
    if (middle >= 60) {
      hour = parseInt(middle / 60);
      middle = parseInt(middle % 60);
    }
  }
  var result = "" + parseInt(theTime) + "秒";
  if (middle > 0) {
    result = "" + parseInt(middle) + "分" + result;
  }
  if (hour > 0) {
    result = "" + parseInt(hour) + "小时" + result;
  }
  return result;
};

export const setPayPath = (
  path = "loginPageThirtyFiveRemainConsultationPayPlus"
) => {
  localStorage.setItem("PayPathV2", path);
};
export const getPayPath = () => {
  return localStorage.getItem("PayPathV2");
};
export const removePayPath = () => {
  localStorage.removeItem("PayPathV2");
};

export const is_weixn = () => {
  var ua = navigator.userAgent.toLowerCase();
  return ua.match(/MicroMessenger/i) == "micromessenger";
};

export const randomNumBoth = (Min, Max) => {
  var Range = Max - Min;
  var Rand = Math.random();
  var num = Min + Math.round(Rand * Range); // 四舍五入
  return num;
};

/* 为空Promise.resolve*/
export const isCaseSourceNull = () => {
  /* 获取案源信息*/
  return new Promise((resolve, reject) => {
    getCaseSourceV2String()
      .then((data = {}) => {
        if (isObjNull(data)) {
          resolve(data);
        }
        return reject();
      })
      .catch(() => {
        return resolve();
      });
  });
};

/* 获取当前的运行平台*/
export const getCurrentPlatform = () => {
// #ifdef  MP-WEIXIN
  return "MP-WEIXIN";
  // #endif
  // #ifdef  MP-BAIDU
  return "MP-BAIDU";
  // #endif
  // #ifdef  MP-TOUTIAO
  return "MP-TOUTIAO";
  // #endif
  // #ifdef  MP-KUAISHOU
  return "MP-KUAISHOU";
// #endif
};

