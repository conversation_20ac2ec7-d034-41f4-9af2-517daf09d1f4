/**
 * 微信小程序自动上传脚本
 */
const getMiniConfig = require("./config");
const ci = require("miniprogram-ci");
const path = require("path");


/**
 * 上传文件到微信小程序
 * @param appid
 * @param projectPath
 * @param privateKeyPath
 * @param isDev
 * @param version
 * @return {Promise<void>}
 */
async function wxUploadFile({ appid, projectPath, privateKeyPath, isDev, version }) {
  const picName = `微信${isDev ? "测试" : "生产"}二维码.png`;

  const project = new ci.Project({
    appid,
    type: "miniProgram",
    projectPath,
    privateKeyPath,
  });

  const params = {
    project,
    version,
    desc: isDev ? `${version} - 测试版请不要提审` : `${version} - 版本更新`,
    // desc: isDev ? "测试版请不要上传" : "版本更新",
    // 1 号机器人上传保证 上传人唯一
    robot: 1,
    qrcodeFormat: "image",
    qrcodeOutputDest: path.resolve(__dirname, `../img/${picName}`),
    setting: {
      es6: true,
      minifyJS: true,
      minify: true,
      minifyWXML: true,
      minifyWXSS: true,
    },
    onProgressUpdate: console.log,
  };

  const uploadResult = await ci.upload(params);

  console.log(uploadResult);
}

module.exports = ({ PROJECT_PATH, FILE_NAME, isDev, version }) => {
  const config = getMiniConfig[FILE_NAME];
  
  // 处理数组格式的配置
  if (Array.isArray(config)) {
    config.forEach((configItem, index) => {
      console.log(`开始上传第 ${index + 1} 个配置...`);
      wxUploadFile({
        ...configItem,
        projectPath: PROJECT_PATH,
        isDev,
        version
      });
    });
  } else {
    // 处理对象格式的配置
    wxUploadFile({
      ...config,
      projectPath: PROJECT_PATH,
      isDev,
      version
    });
  }
};
