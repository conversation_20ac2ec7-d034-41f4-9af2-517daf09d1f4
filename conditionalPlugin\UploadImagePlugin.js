const { ConcatSource } = require("webpack-sources");

class UploadImagePlugin {
  constructor(options) {
    this.options = options;
  }

  apply(compiler) {
    compiler.hooks.emit.tap("UploadImagePlugin", async (compilation) => {
      const assets = compilation.assets;
      const entries = Object.entries(assets);

      // 处理 html
      for (let i = 0; i < entries.length; i++) {
        const [file, originalSource] = entries[i];

        const code = originalSource.source().toString();

        const result = s.toString();

        if (result !== code) {
          const source = new ConcatSource(result);
          compilation.updateAsset(file, source);
        }
      }
    });
  }
}

module.exports = UploadImagePlugin;
