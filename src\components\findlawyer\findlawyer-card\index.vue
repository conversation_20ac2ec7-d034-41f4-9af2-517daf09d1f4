<template>
  <div
    :style="[
      {
        width: `${containerSize * 2}rpx`,
      },
    ]"
    class="findlawyer-card-warp"
  >
    <div
      :style="containerStyle"
      class="flex flex-1 findlawyer-card-content position-relative"
      @transitionend="transitionend"
    >
      <img
        v-for="i in comments"
        :key="i.id"
        :src="i.url"
        :style="[
          {
            width: `${size * 2}rpx`,
            height: `${size * 2}rpx`,
          },
        ]"
        alt=""
        class="img"
      >
    </div>
    <img
      alt=""
      class="toast"
      src="../../../pages/submit-question/findlawyer/imgs/<EMAIL>"
    >
  </div>
</template>

<script>
import { isArrNull } from "@/libs/basics-tools.js";

export default {
  name: "FindLawyerCard",
  props: {
    /** 头像的大小 */
    size: {
      type: Number,
      default: 28,
    },
    /** 头像容器的大小 */
    containerSize: {
      type: Number,
      default: 68,
    },
  },
  data() {
    return {
      containerStyle: "left:0px",
      comments: [
        {
          url: "https://oss.imlaw.cn/test/image/2022/12/28/c31f38b5361c49279467828694924ce1.png",
          id: 1,
        },
        {
          url: "https://oss.imlaw.cn/test/image/2022/12/28/3cf21fc8d4bf4592b3bfa70049edbcac.png",
          id: 2,
        },
        {
          url: "https://oss.imlaw.cn/test/image/2022/12/28/67e92bee9c534141a6ba31ea07796cde.png",
          id: 3,
        },
        {
          url: "https://oss.imlaw.cn/test/image/2022/12/28/ebcfa2e3f7e24295be19325b062a5535.png",
          id: 4,
        },
        {
          url: "https://oss.imlaw.cn/test/image/2022/12/28/bcd12d76576c41edb41857173a699b46.png",
          id: 5,
        },
        {
          url: "https://oss.imlaw.cn/test/image/2022/12/28/c19ea77513124e36806fd46662b46c5f.png",
          id: 6,
        },
        {
          url: "https://oss.imlaw.cn/test/image/2022/12/28/c08da023b1334b58ab08d7a4fb54019e.png",
          id: 7,
        },
        {
          url: "https://oss.imlaw.cn/test/image/2022/12/28/1c785c70c0444770ae99050f4259f666.png",
          id: 8,
        },
        {
          url: "https://oss.imlaw.cn/test/image/2022/12/28/ed740fc16006488180e2218cba0c4a98.png",
          id: 9,
        },
        {
          url: "https://oss.imlaw.cn/test/image/2022/12/28/ab3741d2eaae4a79afc17a98260dae42.png",
          id: 10,
        },
      ],
      timer: "",
      transitionIndex: 0,
    };
  },
  mounted() {
    this.startInterval();
  },
  destroyed() {
    this.clearInterval();
  },
  methods: {
    isArrNull,
    transitionend() {
      this.comments.unshift(this.comments.pop());
      this.containerStyle = "left:0;";
    },
    clearInterval() {
      clearInterval(this.timer);
    },
    startInterval() {
      this.clearInterval();
      this.timer = setInterval(() => {
        this.containerStyle = `left:${38}rpx;transition: left 0.3s ease-in-out;`;
      }, 2000);
    },
  },
};
</script>

<style lang="scss" scoped>
.findlawyer-card-warp {
  width: 68px;
  overflow: auto;
  display: flex;
  justify-content: flex-end;
  position: relative;
  @keyframes move1 {
    0% {
      transform: scale(0);
    }
    100% {
      transform: scale(1);
    }
  }

  .img {
    width: 28px;
    height: 28px;
    flex-shrink: 0;
    margin-right: -8px;
    position: relative;
    transform: scale(0);

    &:last-child {
      transform: scale(1);
      margin-right: 0;
    }

    &:nth-last-child(2) {
      transform: scale(1);
    }

    &:nth-last-child(3) {
      transform: scale(0);
      animation: move1 1s forwards;
    }
  }

  .toast {
    animation: move1 2s infinite;
    position: absolute;
    right: 0;
    top: 0;
    width: 8px;
    height: 8px;
  }
}
</style>
