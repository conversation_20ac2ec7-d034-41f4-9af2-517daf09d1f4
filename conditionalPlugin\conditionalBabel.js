const { getPlatform } = require("./utils");
const { appPlatform } = require("./default");

function operation({ path, name, currPlatform, callback }) {
  const platform = appPlatform;
  const splitName = name?.split("_") || [];

  // splitName 中包含平台名称
  if (splitName.length > 1) {
    for (const item of splitName) {
      if (platform.includes(item) && (!currPlatform || currPlatform !== item)) {
        callback?.(path);
        return;
      }
    }
  }
}

module.exports = (babel) => {
  const currPlatform = getPlatform();

  return {
    visitor: {
      // 变量声明
      VariableDeclaration(path) {
        operation({
          path,
          name: path.node.declarations[0].id.name,
          currPlatform,
          callback: (p) => {
            p.remove();
          },
        });
      },
      // 属性声明
      ObjectProperty(path) {
        operation({
          path,
          name: path.node.key.name,
          currPlatform,
          callback: (p) => {
            p.remove();
          },
        });
      },
      // 函数调用
      CallExpression(path) {
        operation({
          path,
          name: path.node.callee.name,
          currPlatform,
          callback: (p) => {
            p.replaceWithSourceString("function() {}");
          },
        });
      },
      // 方法调用
      MemberExpression(path) {
        operation({
          path,
          name: path.node.property.name,
          currPlatform,
          callback: (p) => {
            p.replaceWithSourceString("function() {}");
          },
        });
      },
    },
  };
};

