const path = require("node:path");
const fs = require("node:fs");

/**
 * 由于在 uniapp-cli 命令下，无法通过 -mode 来修改环境变量，所以这里在脚本运行的时候会动态注入以达到修改 .env.production 文件
 * 在脚本结束后，将 .env.production 文件还原
 */
class EnvChange {
  constructor() {
    this._development = fs.readFileSync(path.resolve(__dirname, "../.env.development"), "utf-8");
    this._production =  fs.readFileSync(path.resolve(__dirname, "../.env.production"), "utf-8");
  }

  _write(){
    fs.writeFileSync(path.resolve(__dirname, "../.env.production"), this._changeString);
  }


  development() {
    this._changeString = this._development;

    this._write();
  }

  production() {
    this._changeString = this._production;

    this._write();
  }
}

const envChange = new EnvChange();

module.exports = envChange;
