<template>
  <app-popup
    :overlay="appOverlay"
    :round="16"
    :show="visible"
    :zIndex="zIndex"
    position="bottom"
    @closed="closed"
    @input="setVisible"
    @click-overlay="clickOverlay"
  >
    <!-- v-bind="$attrs" -->
    <!-- :show="orderVisible" :round="10" border-radius="14" mode="center" -->
    <view
      v-show="showTitle"
      class="select-button flex flex-space-between flex-align-center"
    >
      <view class="select-button-box">
        <view
          v-show="cancellable"
          class="button-cancel"
          @click="clickCancel"
        >
          取消
        </view>
      </view>
      <view class="button-title select-button-box">
        咨询类型
      </view>
      <view
        class="button-define select-button-box"
        @click="clickOK"
      >
        确定
      </view>
    </view>
    <!-- 选项 -->
    <div
      v-if="!hiddenSelectValue"
      class="select-list"
    >
      <div
        v-for="item in dataSource"
        :key="item.value"
        :class="{
          'select-list-item--selected': item.value === selectValue.typeValue
        }"
        class="select-list-item flex flex-align-center flex-space-center"
        @click="handleSelect(item)"
      >
        <span>{{ item.label }}</span>
      </div>
    </div>
    <div
      v-else
      class="click-button flex flex-align-center flex-space-center"
    >
      <view
        class="btn"
        @click="buttonClick"
      >
        请选择咨询类型
      </view>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";

export default {
  name: "AppPopupSelect",
  components: {
    AppPopup
  },
  props: {
    /** 是否显示弹窗，支持.sync修饰符 */
    visible: {
      type: Boolean,
      required: true,
      default: false
    },
    /** 是否显示遮罩层 */
    overlay: {
      type: Boolean,
      default: true
    },
    /** 弹窗中item的数据 */
    dataSource: {
      type: Array,
      default: () => []
    },
    /** 选中的值 */
    value: {
      type: Object,
      default: () => ({})
    },
    /** 是否显示取消按钮 */
    cancellable: {
      type: Boolean,
      default: true
    },
    /** 是否显示标题栏 */
    showTitle: {
      type: Boolean,
      default: true
    },
    /** 是否点击切换按钮 */
    clickToSwitch: {
      type: Boolean,
      default: false
    },
    zIndex: {
      type: [String, Number],
      default: 1000
    }
  },
  data() {
    return {
      /** 选中的值 */
      selectValue: this.value,
      /** 是否隐藏选择的内容 */
      hiddenSelectValue: false
    };
  },
  onLoad() {
    console.log("visible:", this.visible);
  },
  computed: {
    appOverlay() {
      if (this.hiddenSelectValue) return false;

      return this.overlay;
    }
  },
  watch: {
    value(val) {
      this.selectValue = val;
    }
  },
  methods: {
    /** 控制弹窗显影 */
    setVisible(visible) {
      this.$emit("update:visible", visible);
    },
    /** 点击item时触发 */
    handleSelect(value) {
      this.selectValue = {
        typeLabel: value.label,
        typeValue: value.value
      };

      // 如果没有标题栏，则点击直接选择
      if (!this.showTitle) this.clickOK();
    },
    /** 点击确定按钮 */
    clickOK() {
      this.$emit("input", this.selectValue);
      this.$emit("confirm", this.selectValue);
      this.setVisible(false);
    },
    /** 点击取消按钮 */
    clickCancel() {
      this.setVisible(false);
    },
    /** 关闭时重新设置一次值 */
    closed() {
      this.selectValue = this.value;
    },
    /** 点击蒙层时触发 */
    clickOverlay() {
      if (this.clickToSwitch) this.hiddenSelectValue = true;
    },
    buttonClick() {
      if (this.clickToSwitch) this.hiddenSelectValue = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.select-button {
  height: 48px;
  padding: 0 16px;
  border-bottom: 1px solid #eeeeee;
  .select-button-box {
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .button {
    &-cancel {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
    }

    &-title {
      font-size: 16px;
      font-weight: bold;
      color: #000000;
    }

    &-define {
      font-size: 14px;
      font-weight: 400;
      color: $theme-color;
    }
  }
}

.select-list {
  padding: 0 20px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-column-gap: 24px;
  grid-row-gap: 20px;
  margin-top: 22px;
  margin-bottom: 12px;

  &-item {
    background: #f5f5f5;
    color: #333333;
    border-radius: 20px 20px 20px 20px;
    opacity: 1;
    font-size: 13px;
    font-weight: 400;
    height: 38px;

    &--selected {
      background: $theme-color;
      color: #ffffff;
    }
  }
}

.click-button {
  ::v-deep .app-button {
    width: 180px;
    height: 38px;
    font-size: 13px;
    font-weight: 400;
    color: #ffffff;
  }

  padding: 12px 0;
}
</style>
