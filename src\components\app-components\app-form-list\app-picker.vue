<template>
  <app-form-item-layout
    :label="data.typeLabel || placeholder"
    :labelStyle="labelStyle"
    @click="handleClick"
  >
    <options-popup-select
      v-model="data"
      :clickToSwitch="clickToSwitch"
      :closeOnClickOverlay="false"
      :dataSource="dataSource"
      :layout="layout"
      :overlay="overlay"
      :showTitle="showTitle"
      :titleText="titleText"
      :visible.sync="show"
    />
  </app-form-item-layout>
</template>

<script>
import props from "@/components/options-popup-select/props.js";
import OptionsPopupSelect from "@/components/options-popup-select/index.vue";
import AppFormItemLayout from "@/components/app-components/app-form-list/app-form-item-layout.vue";

export default {
  name: "AppPicker",
  components: { AppFormItemLayout, OptionsPopupSelect },
  mixins: [props],
  props: {
    value: {
      type: [String, Object],
      default: "",
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
  },
  data() {
    return {
      show: false,
    };
  },
  computed: {
    data: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", {
          typeLabel: val.typeLabel,
          typeValue: val.typeValue,
        });
      },
    },
    labelStyle() {
      return {
        "font-weight": "400",
        color: this.data.typeLabel ? "#333" : "#CCCCCC",
        "font-size": "15px",
      };
    },
  },
  methods: {
    handleClick() {
      this.show = true;
    },
  },
};
</script>
