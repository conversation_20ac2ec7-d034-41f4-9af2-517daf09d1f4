


export const interceptMinxs = {
  data() {
    return {
      historyPushState: false
    };
  },
  methods: {
  },
  mounted() {
    if(this.historyPushState){
      if (window.history && window.history.pushState) {
        window.history.pushState(null, null, document.URL);
        // 给window添加一个popstate事件，拦截返回键，执行this.onBrowserBack事件，addEventListener需要指向一个方法
        window.addEventListener("popstate", this.onBrowserBack, false);
      }
    }

  },
  destroyed() {
    if(this.historyPushState){
      // 当页面销毁必须要移除这个事件，vue不刷新页面，不移除会重复执行这个事件
      window.removeEventListener("popstate", this.onBrowserBack, false);
    }
  },
};
