<template>
  <app-popup
    :closeOnClickOverlay="false"
    :value="show"
    class="pc-max-w pc-max-w-fixed-center"
    position="bottom"
  >
    <div class="actions flex flex-space-between">
      <span @click="$emit('update:show', false)">取消</span>
      <p class="title">
        {{ title || "案件地点" }}
      </p>
      <span
        class="text-theme-color"
        @click="Confirm"
      >确定</span>
    </div>
    <u-picker
      v-model="cascaderValue"
      :closeable="false"
      :fieldNames="fieldNames"
      :options="options"
      activeColor="#3887F5"
      @change="onChange"
      @close="$emit('update:show', false)"
      @finish="cascaderConfirm"
    />
  </app-popup>
</template>

<script>
import { currencyGetAddress, getArea } from "@/api";
import AppPopup from "@/components/app-components/app-popup/index.vue";
import UPicker from "@/uview-ui/components/u-picker/u-picker.vue";

export default {
  name: "AddrPopup",
  components: { UPicker, AppPopup },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    afieldNames: {
      type: Object,
      default: () => ({})
    },
    /** 弹窗的标题 */
    title: {
      type: String,
      default: undefined
    }
  },
  data() {
    return {
      fieldNames: {
        text: "name",
        value: "code",
        children: "children"
      },
      selectedOptions: [],
      options: [],
      cascaderValue: "",
      selectedCodes: []
    };
  },
  mounted() {
    this.getArea(1).then(({ data }) => {
      this.options = data;
    });
  },
  methods: {
    getArea(type, code) {
      return getArea({ type, code });
    },
    onChange({ value, tabIndex }) {
      if (tabIndex === 1) return false;
      if (this.selectedCodes.indexOf(value) > -1) return false;
      const currentCityIndex = this.options.findIndex(
        item => item.code === value
      );
      const currentCity = this.options[currentCityIndex];
      this.selectedCodes.push(value);
      this.getArea(2, currentCity.code).then(({ data }) => {
        this.$set(this.options[currentCityIndex], "children", data);
      });
    },
    cascaderConfirm({ selectedOptions }) {
      this.selectedOptions = selectedOptions;
    },
    Confirm() {
      if (this.selectedOptions.length > 1) {
        const data = this.selectedOptions[this.selectedOptions.length - 1];
        let label = data.provinceName + data.name;
        let value = data.code;
        // 省和市 直辖市两个值相同 只显示一个
        if (data.code === data.province) {
          label = data.provinceName;
          value = data.code;
        }
        this.$emit("onAddrConfirm", {
          [this.afieldNames.label || "label"]: label,
          [this.afieldNames.value || "value"]: value
        });
      }
      this.$emit("update:show", false);
    },
    turnOnTargeting() {
      currencyGetAddress().then(({ data }) => {
        this.selectedOptions = [
          {
            province: data.provinceCode,
            provinceName: data.provinceName,
            code: data.cityCode,
            name: data.cityName
          },
          {
            province: data.provinceCode,
            provinceName: data.provinceName,
            code: data.cityCode,
            name: data.cityName
          }
        ];
        this.Confirm();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.actions {
  width: 375px;
  height: 48px;
  box-sizing: border-box;
  padding: 14px 16px;
  background: #ffffff;
  border-radius: 16px 16px 0px 0px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #969799;
  line-height: 20px;
  margin-bottom: -48px;

  p {
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: bold;
    color: #323233;
    line-height: 22px;
  }
}
</style>
