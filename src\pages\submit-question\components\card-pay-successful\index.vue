<template>
  <div v-if="hasServer">
    <img
      class="block w-[343px] h-[161px] mt-[16px]"
      alt=""
      src="@/pages/submit-question/imgs/Frame1321316434.png"
      @click="handleGoTo"
    >
  </div>
</template>

<script>
import { userLastServerInfo } from "@/api/index.js";
import { bindOnHook } from "@/libs/hooks";
import { toConsultation, toImChatPage } from "@/libs/turnPages.js";

export default {
  name: "CardPaySuccessful",
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    theme: {
      type: String,
      default: "white"
    }
  },
  data() {
    return {
      serverInfo: {},
      timer: null
    };
  },
  computed: {
    /** 判断是否有im */
    haveIm() {
      return !!this.serverInfo.imSessionId;
    },
    /** 判断是否有服务 */
    hasServer() {
      return !!this.serverInfo.caseSourceServerV2Id;
    }
  },
  mounted() {
    this.getUserLastServerInfo();
    // 每隔5秒请求一次用户最后一次服务信息
    this.timer = setInterval(() => {
      this.getUserLastServerInfo();
    }, 5000);

    bindOnHook.call(this, "onHide", () => {
      clearInterval(this.timer);
    });

    bindOnHook.call(this, "onShow", () => {
      clearInterval(this.timer);
      this.getUserLastServerInfo();
      this.timer = setInterval(() => {
        this.getUserLastServerInfo();
      }, 5000);
    });
  },
  destroyed() {
    clearInterval(this.timer);
  },
  methods: {
    /** 点击前往按钮触发的事件 */
    handleGoTo() {
      if (this.haveIm) {
        // 如果有im，跳转至IM页
        return toImChatPage({
          lawyerId: this.serverInfo.lawyerId,
          conversationId: this.serverInfo.imSessionId,
          caseSourceId: this.serverInfo.caseSourceServerV2Id
        });
      }

      toConsultation({
        index: 1
      });
    },
    getUserLastServerInfo() {
      userLastServerInfo().then(({ data = {} }) => {
        this.serverInfo = data;
      });
    }
  }
};
</script>