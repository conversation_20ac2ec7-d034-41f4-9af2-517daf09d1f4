<template>
  <div>
    <app-popup
      :show="show"
      :zIndex="99999999"
    >
      <div class="popup-container">
        <div class="title flex flex-space-between flex-align-center">
          电话咨询服务
          <img
            alt=""
            class="close"
            src="@/components/telephone-consultation-popup/img/close.png"
            @click="close"
          >
        </div>
        <div class="lawyer-info-container position-relative">
          <if f="hjls">
            <img
              alt=""
              class="background-image"
              src="@/components/telephone-consultation-popup/img/<EMAIL>"
            >
          </if>
          <if t="hjls">
            <img
              alt=""
              class="background-image"
              src="@/components/telephone-consultation-popup/img/Frame1321314799.png"
            >
          </if>
          <div class="lawyer-info">
            <div class="advisory-box position-relative">
              <div class="lawyer-info flex-align-center">
                <div
                  class="lawyer-info-avatar position-relative flex flex-align-center flex-space-center"
                >
                  <img
                    alt=""
                    class="background-image"
                    src="@/pages/lawyer-home/img/<EMAIL>"
                  >
                  <img
                    :src="lawyerInfo.imgUrl"
                    alt=""
                    class="lawyer-info-icon"
                  >
                </div>
                <div>
                  <div class="lawyer-info-name flex flex-align-center">
                    {{ lawyerInfo.realName }}
                    <div
                      class="lawyer-card-work-city mg-l-12 flex flex-align-center"
                    >
                      <span class="city">{{
                        lawyerInfo.workCityName || ""
                      }}</span>
                      <span
                        v-if="lawyerInfo.lawyerOffice"
                        class="dividing-line"
                      >|</span>
                      <span class="office">{{
                        lawyerInfo.lawyerOffice || ""
                      }}</span>
                    </div>
                  </div>
                  <div class="lawyer-info-text">
                    <div class="flex flex-align-center">
                      <img
                        alt=""
                        class="lawyer-info-star"
                        src="@/pages/index/imgs/star.png"
                      >
                      <span class="text-f2af30 text-bold">{{
                        lawyerInfo.score || 0
                      }}</span>
                    </div>
                    <div class="mg-l-12 text-[#666666]">
                      执业<span class="text-[#333333] text-bold mg-l-4">{{
                        lawyerInfo.workTime
                      }}</span>年
                    </div>
                    <div class="mg-l-12 text-[#666666]">
                      平均
                      <span class="text-[#333333] text-bold mg-l-4">{{
                        timeData.time
                      }}</span>{{ timeData.unit }}响应
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <if f="hjls">
          <img
            alt=""
            class="service-process"
            src="@/components/telephone-consultation-popup/img/<EMAIL>"
          >
        </if>
        <if t="hjls">
          <img
            alt=""
            class="service-process"
            src="@/components/telephone-consultation-popup/img/Frame1321314804.png"
          >
        </if>
        <div class="mg-tp-24 mg-b-20">
          <auto-change-lawyer
            v-model="autoChangeLawyerState"
            tipAfterText="若律师$time未响应，平台将推荐在线优选律师为您服务"
          />
        </div>
        <div
          class="btn"
          @click="toPay"
        >
          ¥{{
            (serverInfo.servicePrice ? serverInfo.servicePrice : 0)
              | amountFilter
          }}/{{ serverInfo.serviceNum + serverInfo.unitLabel }} 立即咨询
        </div>
      </div>
      <u-safe-bottom v-if="safeAreaInsetBottom" />
    </app-popup>
    <lawyer-service-pay-popup
      :clearLawyerInfo="false"
      :lawyerInfo="lawyerInfo"
      :safeAreaInsetBottom="safeAreaInsetBottom"
    />
  </div>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import { formatTimeTwoUnit } from "@/libs/filter.js";
import AutoChangeLawyer from "@/components/auto-change-lawyer/AutoChangeLawyer.vue";
import { caseSourceV2SaveGeneral } from "@/api/lawyer.js";
import { getCurrentPageRoute } from "@/libs/turnPages";
import store from "@/store";
import {
  BOOT_POLICY_USER_ACTION_STORE_ENUM,
  setBootPolicyUserActionStoreByField
} from "@/libs/bootPolicy.js";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE, POINT_CODE } from "@/enum/burypoint";
import LawyerServicePayPopup from "@/components/lawyer-service-pay-popup/index.vue";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "TelephoneConsultationPopup",
  components: {
    USafeBottom,
    LawyerServicePayPopup,
    AutoChangeLawyer,
    AppPopup
  },
  props: {
    safeAreaInsetBottom: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      autoChangeLawyerState: 0,
      show: false
    };
  },
  computed: {
    timeData() {
      return formatTimeTwoUnit(this.lawyerInfo.responseTime);
    },
    lawyerInfo() {
      return this.$store.getters["lawyerHome/getPhoneConsultation"].lawyerInfo;
    },
    serverInfo() {
      return this.$store.getters["lawyerHome/getPhoneConsultation"].serverInfo;
    }
  },
  watch: {
    show() {
      if (this.show) {
        /* 进入电话咨询访问数*/
        setBootPolicyUserActionStoreByField(
          BOOT_POLICY_USER_ACTION_STORE_ENUM.THE_NUMBER_OF_LAWYERS_SENT_FAKE_IM_PHONE,
          this.lawyerInfo.id
        );
      }
    }
  },
  mounted() {
    let currentPageRoute = getCurrentPageRoute().fullPath;

    // ! 这里是为了兼容百度，因为百度用上面的方法获取到当前的路由为空
    // #ifdef MP-BAIDU
    this.$nextTick(() => {
      currentPageRoute = getCurrentPageRoute().fullPath;

      console.log(currentPageRoute, "电话咨询弹窗当前路由");
    });
    // #endif

    const showFn = () => {
      console.log(getCurrentPageRoute().fullPath, "电话咨询弹窗当前路由");

      // 如果不是当前页面触发，则关闭弹窗
      if (getCurrentPageRoute().fullPath !== currentPageRoute) {
        this.close();
        return;
      }

      buryPointTransformationPath.add(1062);

      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_SERVICE_CARD_PAGE,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      buryPointChannelBasics(
        {
          code:
            POINT_CODE.LAW_APPLET_SERVICE_COMMON_PHONE_CONSULT_POP_WINDOW_PAGE,
          behavior: BURY_POINT_CHANNEL_TYPE.CK,
          type: 1
        },
        {
          fromPage: true
        }
      );

      // 当前页面触发，则打开弹窗
      this.show = true;
    };

    store.commit("lawyerHome/ADD_PHONE_CONSULTATION_CALLBACK", showFn);

    this.$once("hook:beforeDestroy", () => {
      store.commit("lawyerHome/REMOVE_PHONE_CONSULTATION_CALLBACK", showFn);
    });
  },
  methods: {
    close() {
      this.show = false;
    },
    toPay() {
      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_SERVICE_CARD_CLICK,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      caseSourceV2SaveGeneral({
        lawyerId: this.lawyerInfo.id,
        authChangeLawyer: this.autoChangeLawyerState,
        serverCode: this.serverInfo.serviceCode
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.popup-container {
  padding: 0 12px 12px;
}

.title {
  height: 57px;
  font-size: 18px;
  font-weight: 600;
  color: #222222;

  .close {
    width: 24px;
    height: 24px;
  }
}

.lawyer-info-container {
  height: 168px;
  // #ifdef  MP-HJLS
  height: 192px;
  // #endif
  padding: 12px 16px 0;
  box-sizing: border-box;
}

.advisory-box {
  box-sizing: border-box;
  width: 100%;

  .tag {
    position: absolute;
    top: -0.5px;
    right: -0.5px;
    width: 69px;
    height: 20px;
  }
}

.lawyer-info-avatar {
  flex-shrink: 0;
  margin-right: 12px;
  width: 58px;
  height: 58px;

  .background-image {
    z-index: 1;
  }
}

.advisory-button {
  width: 343px;
  height: 41px;
  display: block;
}

.lawyer-info {
  display: flex;

  &-icon {
    display: block;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    opacity: 1;
  }

  &-name {
    font-size: 15px;
    font-weight: bold;
    color: #333333;
    margin-bottom: 8px;

    .city {
      font-size: 12px;
      font-weight: 400;
      color: #9f6310;
    }

    .dividing-line {
      font-size: 12px;
      color: #cccccc;
      padding: 0 4px;
    }

    .office {
      font-size: 12px;
      font-weight: 400;
      color: #666666;
    }
  }

  &-text {
    display: flex;
    align-items: center;
    font-size: 13px;
    font-weight: 400;
    color: #666666;
  }

  &-star {
    display: block;
    width: 14px;
    height: 14px;
  }
}

.service-process {
  width: 100%;
  height: 111px;
  margin-top: 16px;
}

.btn {
  text-align: center;
  line-height: 44px;
  background: #3887f5;
  border-radius: 68px 68px 68px 68px;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}
</style>
