<template>
  <div
    :class="[leftClass]"
    :style="[formLineStyle]"
    class="form-line"
  >
    <span class="title">
      {{ title }}<i
        v-if="isRequired"
        class="form-line-required inline-block"
      >*</i>
    </span>
    <slot />
  </div>
</template>

<script>
export default {
  name: "AppFormListItem",
  props: {
    title: {
      type: String,
      default: "",
    },
    isRequired: {
      type: Boolean,
      default: false,
    },
    labelPosition: {
      type: String,
      default: "left",
    },
    /** 是否显示下边框 */
    borderBottom: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    leftClass() {
      if (this.labelPosition === "left")
        return "flex flex-space-between flex-align-center form-line-left left-class";

      return "";
    },
    formLineStyle() {
      if (!this.borderBottom) {
        return {
          borderBottom: "none",
        };
      }

      return {};
    },
  },
};
</script>

<style lang="scss" scoped>
.form-line {
  padding: 13px 0;
  border-bottom: 0.5px solid #f5f5f5;
  color: #333333;
  font-size: 14px;

  &-required {
    font-size: 14px;
    color: #f00;
    margin-left: 1px;
  }

  .title {
    margin-bottom: 12px;
    color: #333333;
    font-size: 15px;
    font-weight: 400;
  }
}

.form-line-left {
  .title {
    margin-bottom: 0;
    font-size: 14px;
    font-weight: 400;
  }
}

.left-class {
  .form-radio-more-item {
    background: #f5f5f7 !important;
    border: 0 !important;

    &:nth-child(n) {
      margin-right: 15px;
    }

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
