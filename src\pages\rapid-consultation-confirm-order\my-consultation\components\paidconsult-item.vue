<template>
  <div class="box-item">
    <div class="order-item">
      <div class="info">
        <div class="left">
          <div class="title flex items-center">
            <div>{{ data.typeLabel }}</div>
            <div
              v-if="isDirectionalConsultation"
              class="px-[4px] h-[17px] bg-[#EBF3FE] rounded-[4px] text-[12px] text-[#3887F5] box-border flex items-center justify-center ml-[4px] font-normal"
            >
              定向律师咨询
            </div>
          </div>
        </div>
        <div
          :style="{ color: formatCaseType.color }"
          class="status"
        >
          {{ formatCaseType.txt }}
        </div>
      </div>
      <div class="line">
        <!-- <p class="name">{{data.lawyerId ? '提问' : '留言' }}内容：</p> -->
        <p class="name">
          咨询内容：
        </p>
        <p class="value text-ellipsis">
          {{ data.info }}
        </p>
      </div>
      <div class="line">
        <!-- <p class="name">{{data.lawyerId ? '提问' : '留言' }}时间：</p> -->
        <p class="name">
          咨询时间：
        </p>
        <p class="value">
          {{ data.createTime }}
        </p>
      </div>
      <div
        v-if="isShowLawyerInfo"
        class="line line-lawyer"
      >
        <!-- <p class="name">{{ data.lawyerId ?'解答律师：' : '服务律师：' }}</p> -->
        <p class="name">
          服务律师：
        </p>
        <div
          v-if="showLawyerInfo"
          class="value flex flex-align-center"
        >
          <div class="mg-r-8">
            <find-lawyer-card />
          </div>
          <div class="flex flex-align-center flex-shrink-0">
            已有<span class="text-eb4738">{{ pdCount }}位</span>律师在为您分析问题
          </div>
        </div>
        <div
          v-else
          class="value flex flex-align-center"
        >
          <div v-if="showLawyer">
            <img
              :src="data.imgUrl"
              alt=""
              class="lawyer-avatar"
            >
          </div>
          <div
            v-else
            class="mg-r-8"
          >
            <find-lawyer-card />
          </div>
          <!-- {{data.lawyerId ?data.lawyerName+'律师': '正在匹配中...'}} -->
          {{ showLawyer ? data.lawyerName + "律师" : "平台正在努力匹配中..." }}
        </div>
      </div>
      <div
        v-if="data.closeReason"
        class="line"
      >
        <p class="name">
          关闭原因：
        </p>
        <p class="value">
          {{ data.closeReason }}
        </p>
      </div>
      <div>
        <div
          v-if="tipText"
          class="text-[13px] text-[#EB4738] mt-[12px]"
        >
          <!-- {{data.lawyerId ? '平台已为您匹配律师，赶紧联系律师吧' : '平台正全力匹配中，预计2分钟内匹配成功'}} -->
          {{ tipText }}
        </div>
        <div
          class="space-x-[8px] flex items-center justify-end shrink-0 mt-[8px]"
        >
          <div
            v-if="showAskOtherLawyer"
            class="text-[14px] text-[#333333] w-[130px] h-[32px] rounded-[68px] border-[1px] border-solid border-[#CCCCCC] flex items-center justify-center"
            @click="clickAskOtherLawyer"
          >
            问一问其他律师
          </div>
          <div
            v-if="showCheckMatch"
            class="btn"
            @click="clickCheckMatch"
          >
            查看匹配情况
          </div>
          <!-- 有律师服务才展示 -->
          <div
            v-if="showBtn"
            class="btn"
            @click.stop="toPage"
          >
            {{ btnText }}
          </div>
        </div>
      </div>
    </div>
    <card-invite-comment-popup
      v-if="showAskOtherLawyer"
      v-model="changeLawyerPopupVisible"
      :data="data"
      :graphicConsultationCallbackState="true"
      :historyId="data.caseSourceServerV2Id"
      :historyInfo="data.info"
      :isDirect="false"
      :path="5039"
      :speciality="data.typeValue"
      uvCode="LAW_APPLET_AFTER_PAY_SECOND_CONSULT_POPUP_PAGE"
    />
    <app-modal
      :show="showChangeLawyerPopup"
      cancelText="我要换律师"
      confirmText="取消"
      content="当前律师尚未接单，选择换律师服务，平台将派选优质在线律师尽快为您服务"
      title="换律师确认"
      @cancel="clickChangeLawyerPopupLeft"
      @confirm="clickChangeLawyerPopupRight"
    />
  </div>
</template>

<script>
// status 服务状态（0待锁定，1服务中，2待完成，3已完成，9取消锁定 ，91用户退单，99律师反无效）
import {
  toChangeLawyer,
  toImChatPage,
  toMyCase,
} from "@/libs/turnPages.js";
import { isNull } from "@/libs/basics-tools";
import "./common.scss";
import FindLawyerCard from "@/components/findlawyer/findlawyer-card/index.vue";
import nightTime from "@/mixins/nightTime.js";
import orderTimeMixin from "@/pages/myorder/order-item/mixins/orderTimeMixin.js";
import AppModal from "@/components/app-components/app-modal/index.vue";
import CardInviteCommentPopup from "@/components/CardInviteCommentPopup.vue";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import { toWeChatCustomerService } from "@/libs/tools";

export default {
  name: "PaidConsultItem",
  components: {
    CardInviteCommentPopup,
    AppModal,
    FindLawyerCard,
  },
  mixins: [nightTime, orderTimeMixin],
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      /** 展示律师信息和按钮 */
      showLawyerBtn: [1, 2, 3],
      showChangeLawyerPopup: false,
      changeLawyerPopupVisible: false,
    };
  },
  computed: {
    /** 关联已派单律师数 */
    pdCount() {
      // 如果 pdCount 大于3，显示pdCount 否则显示3
      const pdCount = Number(this.data.pdCount);
      return pdCount > 3 ? pdCount : 3;
    },
    /** 展示律师信息 */
    showLawyer() {
      return [0, 1, 2, 3, 91].includes(this.data.status);
    },
    /** 是否显示服务律师栏 */
    isShowLawyerInfo() {
      // 当订单已经关闭并且没有律师信息时，不展示律师信息
      return !([3, 91].includes(this.data.status) && !this.data.lawyerId);
    },
    /**
     * 已完成服务待评价、已完成服务已评价、未付费关闭的付费咨询提示，新增问问其他律师按钮
     */
    showAskOtherLawyer() {
      return [2, 3, 9, 91].includes(this.data.status);
    },
    /* 过滤状态 3失效,0审核，1正常，2不通过 */
    formatCaseType() {
      let obj = {
        0: {
          txt: this.is1V1OrderType ? "等待律师接单" : "正在匹配律师",
          color: "#999999",
        },
        9: { txt: "已关闭", color: "#EB4738" },
        91: { txt: "已关闭", color: "#EB4738" },
      };

      if (obj[this.data.status]) {
        return obj[this.data.status];
      }

      if (this.isNeedEvaluate) {
        return { txt: "待评价", color: "#999999" };
      }

      if (this.isEvaluate) {
        return { txt: "已评价", color: "#999999" };
      }

      if (this.data.lawyerId) {
        return { txt: "律师已回复", color: "#999999" };
      }

      return { txt: "正在匹配律师", color: "#999999" };
    },
    /** 是否展示律师信息 */
    showLawyerInfo() {
      return !this.data.lawyerId && this.data.status === 0;
    },
    /** 提示内容 */
    tipText() {
      if (this.timeInConfigNoOrder1V1 || this.timeInConfigNoOrderDirectional) {
        return "平台已通知到律师，律师即将到达，如律师长时间不接单可申请换律师服务";
      }

      if (this.showApplyAfterSale) {
        if (this.isDirectionalConsultation) {
          return "当前您指定的律师正忙，可能无法回复您的咨询，您可以选择更换律师解答";
        }

        return this.is1V1OrderType
          ? "您的问题长时间未被接单，可申请换律师"
          : "检测到您的问题暂时没有律师接单，可联系客服为您催单";
      }

      if (this.showLawyerBtn.includes(this.data.status)) {
        return this.getText;
      }

      if (this.data.status === 0) {
        // 如果有律师信息，并且是夜间时间
        if (this.is1V1OrderType && this.timeIsNight)
          return `非工作时间(${this._startTime}-${this._endTime})律师可能正在休息。工作时间律师将第一时间为您服务。`;

        if (this.timeIsNight) {
          return `当前为非工作时间段（${this._startTime}-${this._endTime}），值班律师较少。您的问题平台已加急派送，如长时间无人接单，工作时间律师将会优先为您解答。`;
        }
      }

      if (this.data.status === 0) {
        if (this.isDirectionalConsultation)
          return "当前您指定的律师正忙，可能无法回复您的咨询，平台已为您推荐其他律师解答";

        return this.is1V1OrderType
          ? "律师正在赶来的路上，请稍等..."
          : "平台全力匹配律师中，请稍等...";
      }

      return "";
    },
    /** 按钮文案 */
    btnText() {
      if (this.showApplyAfterSale) {
        return this.is1V1OrderType || this.directionConsultationAndLawyer
          ? "换律师"
          : "呼叫客服";
      }

      if (this.data.status === 1 && this.data.lawyerId) return "进入会话";

      return this.isNeedEvaluate ? "去评价" : "查看会话";
    },
    /** 是否需要评价 */
    isNeedEvaluate() {
      return (
        this.data.status === 3 &&
        !this.data.userEvaluateId &&
        !(this.data.timeOutEvaluateStatus === 1)
      );
    },
    /** 是否已经评价 */
    isEvaluate() {
      return this.data.status === 3 && this.data.userEvaluateId;
    },
    /** 是否是1V1下单 */
    is1V1OrderType() {
      return this.data.createFrom === 1;
    },
    /**
     * https://lanhuapp.com/web/#/item/project/product?tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&pid=f06c01ec-f380-459b-b7a5-b97b5dc690d5&versionId=913f647f-92d8-4c27-8970-9aa1a176fcdf&docId=54e78569-d185-4ac2-bf2d-c95cd329ac8b&docType=axure&pageId=c1b353087f964c35a678c77c1690f6a9&image_id=54e78569-d185-4ac2-bf2d-c95cd329ac8b&parentId=083e6a82-0e23-4bd5-a1bb-b658fa9da5d5
     * 是否在配置的时间段内
     */
    timeInConfig() {
      return Number(this.data.startMinutes) <= this.payTime;
    },
    /** 显示查看匹配情况 */
    showCheckMatch() {
      if (this.is1V1OrderType || this.isDirectionalConsultation) return false;

      return this.timeInConfigNoOrder;
    },
    /** 在时间段内未接单 */
    timeInConfigNoOrder() {
      return this.timeInConfig && this.data.status === 0;
    },
    /** 购买律师1V1服务配置时间内未接单状态 */
    timeInConfigNoOrder1V1() {
      return this.timeInConfigNoOrder && this.is1V1OrderType;
    },
    /** 购买定向咨询服务配置时间内未接单状态 */
    timeInConfigNoOrderDirectional() {
      return this.timeInConfigNoOrder && this.isDirectionalConsultation;
    },
    /** 购买定向咨询服务并且指定了律师 */
    directionConsultationAndLawyer() {
      return this.data.lawyerId && this.isDirectionalConsultation;
    },
    /**
     * 申请售后
     * 10分钟未接单且工作时间段（早8:30-晚20:30）律师未回复时
     */
    showApplyAfterSale() {
      return this.data.status === 0 && !this.timeIsNight && !this.timeInConfig;
    },
    // 有律师服务才展示且有会话id
    showBtn() {
      return (
        (this.showLawyerBtn.includes(this.data.status) &&
          !isNull(this.data.imSessionId)) ||
        this.showApplyAfterSale
      );
    },
    getText() {
      if (this.data.status === 3 && !this.data.userEvaluateId) {
        return "您的评价将让律师做得更好！";
      } else {
        return "";
      }
    },
    /** 是否是定向咨询 */
    isDirectionalConsultation() {
      return this.data.directionalLawyerConsult === 1;
    },
  },
  mounted() {},
  methods: {
    isNull,
    /** 点击问问其它律师 */
    clickAskOtherLawyer() {
      // 已完成
      if (this.data.status === 3) {
        buryPointChannelBasics({
          code: "LAW_APPLET_MY_PAID_CONSULT_FINISHED_ASK_OTHER_LAWYER_BUTTON_CLICK",
          type: 1,
          behavior: BURY_POINT_CHANNEL_TYPE.VI,
        });
      }

      // 已关闭
      if ([9, 91].includes(this.data.status)) {
        buryPointChannelBasics({
          code: "LAW_APPLET_MY_PAID_CONSULT_CLOSED_ASK_OTHER_LAWYER_BUTTON_CLICK",
          type: 1,
          behavior: BURY_POINT_CHANNEL_TYPE.VI,
        });
      }

      this.changeLawyerPopupVisible = true;
    },
    toPage() {
      if (this.showApplyAfterSale) {
        if (this.is1V1OrderType || this.directionConsultationAndLawyer) {
          this.showChangeLawyerPopup = true;
          return;
        }

        toWeChatCustomerService();
      }

      uni.setStorageSync("data.imSessionId", this.data.imSessionId);
      // 点击立即升级，跳转电话咨询的确认订单页面
      // 同时显示“进入会话按钮”，点点击按钮跳转付费服务im
      // 去免费咨询IM页面
      toImChatPage({
        lawyerId: this.data.lawyerId,
        conversationId: this.data.imSessionId,
        caseSourceId: this.data.caseSourceServerV2Id,
      });
    },
    /** 换律师左侧按钮 */
    clickChangeLawyerPopupLeft() {
      this.showChangeLawyerPopup = false;

      toChangeLawyer({
        orderId: this.data.orderId,
      });
    },
    /** 换律师右侧按钮 */
    clickChangeLawyerPopupRight() {
      this.showChangeLawyerPopup = false;
    },
    /** 点击查看匹配情况 */
    clickCheckMatch() {
      buryPointChannelBasics({
        code: "LAW_APPLET_MY_PAID_COONSULT_MATCHING_SITUATION_BUTTON_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
      });

      toMyCase({
        id: this.data.caseSourceV2Id,
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
