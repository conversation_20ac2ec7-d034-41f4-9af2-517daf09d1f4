<template>
  <div
    class="notification-banner bg-[#F78C3E] px-[16px] py-[10px] flex items-center justify-between"
    @click="handleGoTo"
  >
    <div class="flex items-center">
      <div class="bell-animation">
        <img
          class="block w-[24px] h-[24px]"
          alt=""
          src="@/assets/common/Communication.png"
        >
      </div>
      <div class="text-[14px] text-[#FFFFFF] ml-[8px]">
        您有进行中的服务，请点击查看   
      </div>
    </div>
    <div class="arrow-animation">
      <img
        class="block w-[16px] h-[16px]"
        alt=""
        src="@/assets/common/Frame.png"
      >
    </div>
  </div>
</template>

<script>


export default {
  name: "HasPaidOrderTips",
  methods: {
    handleGoTo(){
      this.$emit("click");
    }
  },
};
</script>

<style scoped lang="scss">
@keyframes shake {
  0%,
  100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-15deg);
  }
  50% {
    transform: rotate(0deg);
  }
  75% {
    transform: rotate(15deg);
  }
}
@keyframes move {
  0%,
  100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(8px);
  }
}
.bell-animation {
  animation: shake 1.5s ease-in-out infinite;
}
.arrow-animation {
  animation: move 1.2s ease-in-out infinite;
}
.notification-banner {
  transition: transform 0.3s ease;
}
.notification-banner:hover {
  transform: scale(1.02);
}
</style>
