<template>
  <div class="title flex flex-align-center flex-space-between">
    <div class="flex flex-align-center">
      <img
        alt=""
        class="title__icon"
        src="@/components/submit-discount-popup/img/<EMAIL>"
      >
      <div>叮~首次咨询专属优惠到账</div>
    </div>
    <card-result-countdown
      v-if="countDown > 0"
      :time="countDown"
    />
  </div>
</template>
<script>
import CardResultCountdown from "@/components/CardResultCountdown.vue";
import countDownMixin from "@/pages/rapid-consultation-confirm-order/my-consultation/js/countDownMixin";

export default {
  name: "SubmitDiscountTitle",
  components: { CardResultCountdown },
  mixins: [countDownMixin],
  props: {
    createTime: {
      type: [Number, String, undefined],
      default: undefined
    }
  }
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;

  &__icon {
    display: block;
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }
}
</style>
