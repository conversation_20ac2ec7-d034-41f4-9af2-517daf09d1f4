<template>
  <login-layout>
    <div class="pb-[115px]">
      <img
        class="block w-full h-[112px]"
        src="@/pages/submit-question/caculator/imgs/<EMAIL>"
        alt=""
      >
      <div class="px-[16px] mt-[-22px]">
        <div class="bg-[#FFFFFF] scroll-contaier rounded-[8px] px-[16px]">
          <caculator-item-form
            ref="caculatorItemForm"
            :formData="formData"
            :rules="formRules"
          >
            <div
              class="h-[68px] border-[0px] border-b-[1px] border-solid border-[#EEEEEE]"
            >
              <caculator-item
                title="入职年限"
                prop="entryYear"
              >
                <caculator-item-input
                  v-model="formData.entryYear"
                  type="digit"
                  maxlength="4"
                  placeholder="请输入入职年限"
                />
              </caculator-item>
            </div>

            <div
              class="h-[68px] border-[0px] border-b-[1px] border-solid border-[#EEEEEE]"
            >
              <caculator-item
                title="所在地区"
                prop="regionCode"
              >
                <caculator-item-popup-location
                  v-model="formData.regionCode"
                  placeholder="请选择"
                  @setCity="setCity"
                />
              </caculator-item>
            </div>


            <div
              class="h-[68px] border-[0px] border-b-[1px] border-solid border-[#EEEEEE]"
            >
              <caculator-item
                prop="averageSalary"
                title="平均税前月工资"
              >
                <caculator-item-input
                  v-model="formData.averageSalary"
                  unit="元"
                  type="digit"
                  maxlength="9"
                  placeholder="请输入平均税前月工资"
                />
              </caculator-item>
            </div>

            <div
              class="py-[22px] border-[0px] border-b-[1px] border-solid border-[#EEEEEE]"
            >
              <caculator-item
                prop="contractType"
                title="劳动合同类型"
                layout="column"
              >
                <caculator-item-check-block
                  v-model="formData.contractType"
                  :list="contractTypeList"
                  placeholder="请选择"
                  @change="handleChange($event, 'contractType')"
                />
              </caculator-item>
            </div>
          </caculator-item-form>

          <div class="flex items-center justify-between h-[64px]">
            <caculator-item-check
              v-model="formData.illegalContract"
              title="违法解除劳动合同"
            />
            <caculator-item-check
              v-model="formData.notNotice"
              title="未提前30天通知"
            />
          </div>
        </div>
        <div class="mt-[12px] bg-[linear-gradient(_180deg,_#DAE8FF_0%,_#F0F8FF_50%,_#FFFFFF_100%)] rounded-[8px]">
          <div class="min-h-[100px] position-relative">
            <img
              class="background-image"
              src="@/pages/submit-question/caculator/imgs/<EMAIL>"
              alt=""
            >
            <div v-if="!showCalculateResult||(showCalculateResult&&needShowCalculateAmount)">
              <p class="text-[16px] text-[#333333] flex items-center justify-center pt-[24px] pb-[4px]">
                预估补偿金额
              </p>
              <div class="font-[600] text-[20px] text-[#3887F5] flex items-center justify-center pt-[4px]">
                <div class="flex items-baseline justify-center leading-[1]">
                  <span class="text-[16px]">¥</span> {{ !showCalculateResult?0:calculateResult }}
                </div>
              </div>
            </div>
            <div
              v-if="showCalculateResult&&!needShowCalculateAmount"
              class="flex flex-col items-center justify-center"
            >
              <p class="text-[18px] text-[#333333] pt-[24px]">
                {{ labels.contractTypeDesc }}
              </p>
              <p class="font-[600] text-[26px] text-[#F34747] pt-[4px] pb-[8px]">
                属违法行为
              </p>
              <p class="text-[14px] text-[#3A88F5] px-[8px] py-[2px] bg-[#DFF0FF] rounded-[2px]">
                建议直接咨询律师，争取自己的合法权益
              </p>
            </div>
          </div>
          <div
            v-if="showCalculateResult"
            class=" pb-[24px]"
          >
            <div class="pt-[16px] px-[16px] text-[12px] text-[#666666] whitespace-pre-wrap">
              <p class="font-bold text-[12px] text-[#666666]">
                1. 补偿月数(N)
              </p>
              <p class="text-[12px] text-[#666666] pt-[4px] leading-[20px]">
                {{ '工龄<6个月：N=0.5' }}<br>
                {{ '6个月≤工龄<1年：N=1' }}<br>
                {{ '工龄≥1年：N=取整(工龄年数)，小数≥0.5时加0.5' }}
              </p>
              <p class="font-bold text-[12px] text-[#666666] pt-[8px]">
                2. 基础补偿金
              </p>
              <p class="text-[12px] text-[#666666] pt-[4px]">
                经济补偿金=N×月工资基数
              </p>
              <p class="font-bold text-[12px] text-[#666666] pt-[8px]">
                3. 附加项
              </p>
              <p class="text-[12px] text-[#666666] pt-[4px] leading-[20px]">
                未提前30天通知：+1个月工资基数<br>
                违法解除劳动合同：补偿金×2
              </p>
              <p class="font-bold text-[12px] text-[#666666] pt-[8px]">
                4. 总计算公式
              </p>
              <p class="text-[12px] text-[#666666] pt-[4px]">
                总赔偿金=经济补偿金+附加项
              </p>
            </div>
            <div class="pt-[8px] px-[16px] text-[12px] text-[#999999]">
              * 计算结果仅供参考，实际金额以当地最新政策为准
            </div>
          </div>
        </div>
        <div class="flex items-center justify-center pt-[14px]">
          <caculator-item-check
            v-model="protocolChecked"
            :checkIcon="icons.check"
          >
            <p class="text-[12px] text-[#CCCCCC]">
              使用服务即表示同意律师提供免费法律指导
            </p>
          </caculator-item-check>
        </div>
        <u-safe-bottom />
      </div>

      <div class="fixed left-0 bottom-0 bg-[#FFFFFF]">
        <div class="w-[375px] h-[75px] flex justify-between items-center px-[16px] pt-[23px] pb-[8px] box-border">
          <div
            class="w-[144px] h-[44px] bg-[#EEEEEE] rounded-[12px] box-border flex items-center justify-center"
            @click="resetForm"
          >
            <div class="font-[500] text-[16px] text-[#666666]">
              重置
            </div>
          </div>
          <div class="w-[186px] h-[44px] ml-[13px]">
            <caculator-item-button
              :clickInterval="3000"
              @handleClick="handleCalculate"
            >
              计算
            </caculator-item-button>
          </div>
        </div>
        <u-safe-bottom />
      </div>
    </div>
    <caculator-civil-popup
      v-model="payPopupShow"
      buriedType="laborArbitration"
      :bgImg="payImg.backImg"
    />
    <scroll-monitor
      targetClass="scroll-contaier"
      :offset="-8"
      @reach-top="handleReachTop"
      @leave-top="handleLeaveTop"
    />
  </login-layout>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import CaculatorItem from "@/pages/submit-question/components/caculator-item/index.vue";
import CaculatorItemInput from "@/pages/submit-question/components/caculator-item/input.vue";
import CaculatorItemCheck from "@/pages/submit-question/components/caculator-item/check.vue";
import { isNull } from "@/libs/basics-tools";
import CaculatorCivilPopup from "@/pages/submit-question/caculator/civil/popup.vue";
import CaculatorItemForm from "@/pages/submit-question/components/caculator-item/form.vue";
import { saveCaseSource } from "@/pages/submit-question/caculator";
import { formatAmount, setNavigationBarTitle, whetherToLogIn } from "@/libs/tools";
import CaculatorItemPopupLocation from "@/pages/submit-question/components/caculator-item/popup-location.vue";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import LoginLayout from "@/components/login/login-layout.vue";
import CaculatorItemButton from "@/pages/submit-question/components/caculator-item/button.vue";
import ScrollMonitor from "@/pages/submit-question/components/scroll-monitor/index.vue";
import timeConfig from "@/libs/timeConfig";
import CaculatorItemCheckBlock from "@/pages/submit-question/components/caculator-item/check-block.vue";
import { setNavigationBarAlipay } from "@/libs/tools";

const payImg = {
  backImg: require("@/pages/submit-question/caculator/imgs/<EMAIL>")
};
const icons = {
  check: require("@/pages/submit-question/caculator/imgs/icon@2x_1.png")
};
export default {
  name: "CaculatorCivil",
  components: {
    CaculatorItemCheckBlock,
    ScrollMonitor,
    CaculatorItemButton,
    LoginLayout,
    CaculatorItemPopupLocation,
    CaculatorItemForm,
    USafeBottom,
    CaculatorItem,
    CaculatorItemInput,
    CaculatorItemCheck,
    CaculatorCivilPopup
  },
  data() {
    return {
      icons,
      // 支付弹窗背景图
      payImg,
      // 支付弹窗
      payPopupShow: false,
      // 协议勾选状态
      protocolChecked: true,
      // 劳动合同类型list
      contractTypeList: [{
        label: "固定年限",
        value: "1",
        // 保存案源时存的文案
        info: "签的劳动合同是固定年限"
      }, {
        label: "终身合同",
        value: "2",
        // 描述
        description: "企业解除终身合同",
        // 保存案源时存的文案
        info: "签的劳动合同是终身合同"
      }, {
        label: "未签订合同",
        value: "3",
        // 描述
        description: "企业未签订劳动合同",
        // 保存案源时存的文案
        info: "没有签劳动合同"
      }],
      // 表单数据
      formData: {
        // 入职年限
        entryYear: "",
        // 所在地区
        regionCode: "",
        // 平均税前月工资
        averageSalary: "",
        // 劳动合同类型
        contractType: "",
        // 违法解除劳动合同
        illegalContract: false,
        // 未提前30天通知
        notNotice: false
      },
      // 劳动合同类型选择数据
      labels: {
        // 所在地区
        happenAddress: "",
        // 合同类型
        contractType: "",
        // 合同类型info存的时候文案
        contractTypeInfo: "",
        // 合同类型显示的文案
        contractTypeDesc: ""
      },
      //   计算结果
      calculateResult: null,
      formRules: {
        entryYear: [{
          required: true,
          message: "请填写入职年限"
        }, {
          type: "number",
          message: "入职年限请输入数字"
        },
        // 请输入整数或最多一位小数
        {
          pattern: /^\d+(\.\d{1})?$/,
          message: "入职年限请输入整数或最多一位小数"
        }
        ],
        regionCode: [{
          required: true,
          message: "请选择所在地区"
        }],
        averageSalary: [{
          required: true,
          message: "请填写平均税前月工资"
        }, {
        //   整数
          type: "integer",
          message: "平均税前月工资请输入整数"
        }],
        contractType: [{
          required: true,
          message: "请选择劳动合同类型"
        }],
      }
    };
  },
  computed: {
    // 选择的劳动合同类型
    selectedContractType() {
      return this.formData.contractType;
    },
    // 需不需要展示计算金额 只有劳动合同类型是固定年限时才需要展示
    needShowCalculateAmount() {
      return this.selectedContractType === "1" || isNull(this.selectedContractType);
    },
    // 是否展示计算结果
    showCalculateResult(){
      return !isNull(this.calculateResult);
    }
  },
  mounted() {
    setNavigationBarAlipay({
      frontColor: "#000000",
      backgroundColor: "#def2ff",
    });

    buryPointChannelBasics({
      code: "LAW_APPLET_UTILS_LABOR_ARBITRATION_CALCULATOR_PAGE",
      type: 1,
      behavior: BURY_POINT_CHANNEL_TYPE.CK
    });
  },
  methods: {
    handleChange(event, key){
      this.labels[key] = event.label;
      // 劳动合同类型 单独处理一下
      if(key === "contractType"){
        this.labels.contractTypeInfo = event.info;
        this.labels.contractTypeDesc = event.description;
        this.calculateResult = null;
      }
    },
    // 城市选择
    setCity({ city, label }){
      this.formData.regionCode = String(city.code);
      this.labels.happenAddress = label;
    },
    // 保存案源
    async saveCaseSource(info){
      saveCaseSource({
        info,
        label: "劳资纠纷",
        value: "10",
        data: {
          happenAddress: this.labels.happenAddress,
          regionCode: this.formData.regionCode
        }
      }).then(() => {
        // 2s后 弹出支付弹窗
        timeConfig.toolDelay().then(time => {
          // 2s后 弹出支付弹窗
          setTimeout(() => {
            this.payPopupShow = true;
          }, time);
        });
      });
    },
    resetForm() {
      const data = this.$options.data();
      this.formData = data.formData;
      this.labels = data.labels;
      this.calculateResult = data.calculateResult;
      buryPointChannelBasics({
        code: "LAW_APPLET_UTILS_LABOR_ARBITRATION_CALCULATOR_PAGE_RESET_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });
    },
    // 预估补偿金额
    estimatedCompensationAmount() {
      // 判断是否需要展示金额 不需要直接是0
      if(!this.needShowCalculateAmount) {
        this.calculateResult = 0;
        return;
      }

      const { entryYear, averageSalary, illegalContract, notNotice } = this.formData;
      const calculateResult = this.calculateTotalCompensation({
        entryYear: Number(entryYear),
        averageSalary: Number(averageSalary),
        illegalContract,
        notNotice
      });
      this.calculateResult = formatAmount(calculateResult);
    },
    // 计算总赔偿金
    calculateTotalCompensation({ entryYear, averageSalary, illegalContract, notNotice }) {
      // 拆分工作年限为年和月
      const years = Math.floor(entryYear);
      const months = Math.round((entryYear - years) * 12); // 将小数部分转换为月数
      
      // 计算补偿月数N
      let N = 0;
      
      // 处理年数部分：每满一年支付一个月工资
      N += years;
      
      // 处理月数部分
      if (months >= 6) {
        N += 1;  // 满6个月按一年计算
      } else if (months > 0) {
        N += 0.5;  // 不满6个月支付半个月工资
      }

      // 计算基础补偿金
      let baseCompensation = N * averageSalary;

      // 计算附加项
      let additionalCompensation = 0;
      if (notNotice) {
        additionalCompensation += averageSalary; // 未提前30天通知
      }
      if (illegalContract) {
        baseCompensation *= 2; // 违法解除劳动合同
      }

      // 计算总赔偿金
      const totalCompensation = baseCompensation + additionalCompensation;
      
      return Math.round(totalCompensation);
    },
    // 计算
    handleCalculate(){

      buryPointChannelBasics({
        code: "LAW_APPLET_UTILS_LABOR_ARBITRATION_CALCULATOR_PAGE_CALCULAT_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });
      whetherToLogIn(() => {
        if(!this.protocolChecked){
          uni.showToast({
            title: "请先同意律师提供免费法律指导",
            icon: "none"
          });
          return;
        }
        this.$refs.caculatorItemForm.setRules(this.formRules);
        this.$refs.caculatorItemForm.validate().then(() => {
          /*
          * "你好，我想咨询一下劳动仲裁补偿金，我在北京工作了5年，月工资5000元，签的劳动合同是固定年限"
          "你好，我想咨询一下劳动仲裁补偿金，我在北京工作了5年，月工资5000元，签的劳动合同是终身合同"
          "你好，我想咨询一下劳动仲裁补偿金，我在北京工作了5年，月工资5000元，没有签劳动合同"
          * **/
          let info = `你好，我想咨询一下劳动仲裁补偿金，我在北京工作了${this.formData.entryYear}年，月工资${this.formData.averageSalary}元，${this.labels.contractTypeInfo}`;
          this.estimatedCompensationAmount();
          // this.saveCaseSource(info);
        });
      });
    },
    handleReachTop() {
      setNavigationBarTitle({
        title: "劳动仲裁计算器",
        backgroundColor: "#FFFFFF"
      });
    },
    handleLeaveTop() {
      setNavigationBarTitle({
        backgroundColor: "#DEF2FF"
      });
    },
  },

  // 必须在页面中添加 onPageScroll 处理函数
  onPageScroll(e) {
    // 触发全局事件，让ScrollMonitor组件能够接收到
    uni.$emit("onPageScroll", e.scrollTop);
  }
};
</script>
