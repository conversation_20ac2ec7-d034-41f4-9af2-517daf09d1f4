<template>
  <div class="fixed bottom-[12px] right-[12px]">
    <div
      v-if="!showThumbnail"
      class="relative"
      @click="handleClick"
    >
      <img
        alt=""
        class="w-[351px] h-[108px] block"
        src="../../pages/submit-question/findlawyer/imgs/Frame1321315356.png"
      >
      <img
        alt=""
        class="w-[12px] h-[12px] absolute top-[44px] block right-[4px]"
        src="../../pages/submit-question/findlawyer/imgs/close.png"
        @click.stop="switchThumbnail"
      >
    </div>
    <img
      v-if="showThumbnail"
      alt=""
      class="w-[80px] h-[68px] block"
      src="../../pages/submit-question/findlawyer/imgs/Frame1321315358.png"
      @click="handleClick"
    >
  </div>
</template>

<script>
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";

export default {
  name: "RetainCapitalIcon",
  data() {
    return {
      /** 显示缩略 */
      showThumbnail: false
    };
  },
  mounted() {
    buryPointChannelBasics({
      code: "LAW_APPLET_FIND_LAWYER_STAY_BOTTOM_POP_UP_PAGE",
      type: 1,
      behavior: BURY_POINT_CHANNEL_TYPE.CK
    });
  },
  methods: {
    handleClick() {
      buryPointChannelBasics({
        code:
          "LAW_APPLET_FIND_LAWYER_STAY_BOTTOM_POP_UP_PAGE_CONSULT_NOW_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      this.$emit("click");
    },
    switchThumbnail() {
      this.showThumbnail = !this.showThumbnail;
    }
  }
};
</script>
