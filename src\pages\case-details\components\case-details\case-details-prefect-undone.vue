<template>
  <div class="formlist-case">
    <!-- <FormList
      ref="formList"
      :data="caseInfo"
      :form-list="list"
      label-position="left"
      @picker="handlePicker"
      @updateData="updateDataFun"
    /> -->
    <!-- :real-time-validate.sync="formDataRealTimeValidate" -->
    <app-form-list
      ref="formList"
      :data="caseInfo"
      :formList="list"
      labelPosition="left"
      @picker="handlePicker"
      @updateSelectList="updateDataFun"
    />
    <div
      class="btn"
      @click="submit"
    >
      保存
    </div>
  </div>
</template>

<script>
import AppFormList from "@/components/app-components/app-form-list/index.vue";
import { caseSourceV2Save, problemsList } from "@/api";
import { isObjNull } from "@/libs/basics-tools.js";
// import {caseDetailsPageSAVESUBMITBuried} from './js/index.js'
/**
 * 完善案件资料-未完成
 * @FilePath components\case-details\case-details-prefect-undone.vue
 */
export default {
  name: "CaseDetailsPrefectUndone",
  components: {
    AppFormList,
  },
  props: {
    caseSourceData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      routeObj: {}, //
      /** 控制返回拦截弹窗 */
      // backDialogVisible: false,
      otherList: [],
      formData: {},
      /** 当前的案件类型 */
      caseInfo: {
        typeValue: "",
        typeLabel: "",
      },
      /** 案件详情是否已经填写完毕 */
      done: false,
    };
  },
  onLoad(options) {
    this.routeObj = options;
  },
  computed: {
    list() {
      return [...this.otherList];
    },
  },
  watch: {
    caseSourceData: {
      handler() {
        if (this.caseSourceData.length < 0) return;

        if (!isObjNull(this.caseSourceData)) this.init();
      },
      immediate: true,
      deep: true,
    },
  },

  methods: {
    // 获取数据列
    async init() {
      this.filterList();
    },
    filterList() {
      const { typeValue, typeLabel } = this.caseSourceData;

      this.caseInfo = {
        typeValue,
        typeLabel,
      };

      /** 请求案源问题 */
      problemsList({
        typeValue,
      }).then(({ data }) => {
        const inputOptions = {
          inputType: "tel",
          maxLength: 7,
        };
        this.otherList = data.map((item) => {
          return {
            key: item.id,
            title: item.problemInfo,
            type: item.type === 0 ? "radio" : "input",
            unit: item.inputType === 1 ? "元" : "",
            reg: item.inputType === 1 ? ["number"] : [],
            arr: (item.resultDetails || []).map((item) => ({
              label: item.answerLabel,
              value: Number(item.answerValue),
            })),
            typeValue: item.typeValue,
            inputType: item.inputType,
            labelPosition: "left",
            ...(item.inputType === 1 ? inputOptions : {}),
          };
        });
      });
    },
    /** 更新表单项 */
    updateDataFun(val) {
      this.formData = val;
    },
    handlePicker(currentList, data) {
      if (currentList.key !== "typeValue") return false;
      this.filterList(data.value);
    },
    submit() {
      //   /**
      //  * 完善案件资料保存按钮埋点
      //  * @Version 2.1.6 埋点
      //  */
      //   caseDetailsPageSAVESUBMITBuried()
      this.$refs.formList.validate().then(() => {
        this.handleDescribeFormClick();
      });
    },
    handleDescribeFormClick() {
      // 找到含有输入框的特殊值的key
      const specialKey = this.otherList.map((item) => {
        if (String(item.type) === "input") {
          return item.key;
        }
      });

      const caseSourceV2ResultList = this.formData.map((item) => {
        // 金额需要*100传给后端
        let resultValue =
            specialKey.findIndex((key) => key === item.key) > -1
              ? item.value * 100
              : item.value;

        return {
          caseSourceV2ProblemId: item.key,
          resultValue,
        };
      });

      // id: this.routeObj?.id,
      caseSourceV2Save({
        isDistributed: true,
        id: this.caseSourceData.caseSourceId,
        ...this.caseSourceData,
        caseSourceV2ResultList,
      }).then(({ data }) => {
        // uni.navigateTo({ url: "/pages/case-details/index?id=" + data.id });
        uni.navigateBack();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
// ::v-deep .van-dialog__message {
//   font-weight: 400;
// }
.formlist-case {
  padding-bottom: 25px;

  .btn {
    width: 343px;
    height: 44px;
    background: #3887F5;
    border-radius: 22px 22px 22px 22px;
    font-size: 16px;
    color: #fff;
    line-height: 44px;
    text-align: center;
    position: fixed;
    bottom: 20px;
    left: 16px;
  }
}
</style>
