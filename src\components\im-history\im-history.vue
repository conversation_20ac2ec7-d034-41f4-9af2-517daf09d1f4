<template>
  <div
    v-if="historyTextList && historyTextList.length > 0"
    :style="[addStyle(customStyle)]"
    class="history-box flex flex-align-center"
  >
    <p class="title">
      {{ title }}
    </p>
    <div class="content flex-1 flex flex-align-center">
      <im-history-item
        v-for="(item, index) in historyTextList"
        :key="index"
        :item="item"
        :theme="theme"
        class="select-item"
        @click="handleClick(item)"
      />
    </div>
  </div>
</template>

<script>
import { getParalegalData } from "@/libs/paralegalData.js";
import ImHistoryItem from "@/components/im-history/im-history-item.vue";
import { addStyle } from "@/uview-ui/libs/function/index.js";

export default {
  name: "ImHistory",
  components: { ImHistoryItem },
  props: {
    theme: {
      type: String,
      default: "gray"
    },
    /** 自定义样式 */
    customStyle: {
      type: [Object, String],
      default: () => ({})
    },
    title: {
      type: String,
      default: "历史咨询"
    }
  },
  data() {
    return {
      historyList: []
    };
  },
  computed: {
    historyTextList() {
      return this.historyList.map(item => item.info);
    },
    isLogin() {
      return this.$store.getters["user/getToken"];
    }
  },
  mounted() {
    this.getHistoryData();
  },
  methods: {
    /** 请求历史数据 */
    getHistoryData() {
      if (this.isLogin) {
        getParalegalData().then(data => {
          this.historyList = data?.data || [];
          // 将历史记录条数传给父组件
          this.$emit("history", this.historyList);
        });
      }
    },
    addStyle,
    handleClick(value) {
      console.log(value, "11111111111111");
      this.$emit("history-select", value);
    }
  }
};
</script>

<style lang="scss" scoped>
.history-box {
  width: 100%;
  background: #f5f5f7;
  opacity: 1;
  // overflow-x: auto;
  overflow-y: hidden;
  padding: 8px 0 8px 16px;
  box-sizing: border-box;
}
.content {
  overflow-x: auto;
}

.title {
  font-size: 12px;
  font-weight: 400;
  color: #666666;
  flex-shrink: 0;
  padding-right: 12px;
}

.select-item {
  margin-left: 12px;
  flex-shrink: 0;

  &:nth-child(1) {
    margin-left: 0;
  }
}
</style>
