<template>
  <view>
    <login-layout>
      <view class="tabs">
        <if f="hjls">
          <u-tabs
            :activeStyle="{
              color: '#333',
              fontSize: '14px',
              fontWeight: 'bold',
            }"
            :current="tabIndex"
            :inactiveStyle="{
              color: '#999',
              fontSize: '14px',
            }"
            :list="listTabs"
            :scrollable="false"
            itemStyle="flex:1;height: 88rpx;"
            lineColor="#3887F5"
            lineHeight="3"
            lineWidth="14"
            @click="tabChange"
          />
        </if>
        <if t="hjls">
          <u-tabs
            :activeStyle="{
              color: '#333',
              fontSize: '14px',
              fontWeight: 'bold',
            }"
            :current="tabIndex"
            :inactiveStyle="{
              color: '#999',
              fontSize: '14px',
            }"
            :list="listTabs"
            :scrollable="false"
            itemStyle="flex:1;height: 88rpx;"
            lineColor="#50B08C"
            lineHeight="3"
            lineWidth="14"
            @click="tabChange"
          />
        </if>
        <view class="line" />
        <has-paid-order-tips
          v-if="hasPaidOrder"
          @click="handleGoTo"
        />
      </view>
      <app-placeholder
        :height="hasPaidOrder ? 85 : 45"
        :showSafe="false"
      />
      <!--      <my-order-tips />-->
      <div class="order-box px-[12px]">
        <div :class="{ myorder: true, 'myorder-nodata': isNoData }">
          <!-- 登录授权处理 -->
          <view
            v-if="!isLogin"
            class="no-data no-login"
          >
            <text class="desc">
              您还未登录，登录后可查看订单信息
            </text>
            <view
              class="btn"
              @click="toLogin"
            >
              登录
            </view>
          </view>
          <!-- 已登录 -->
          <view v-else>
            <!-- 加载效果添加 -->
            <u-loading-icon :show="!requestEnd" />
            <!-- 无数据处理 -->
            <!-- <view class="no-data" v-else> -->
            <view
              v-if="isNoData && requestEnd"
              class="no-data"
            >
              <img
                :src="noDataObj.img"
                alt=""
                class="no-data-img"
                srcset=""
              >
              <text class="desc">
                {{ noDataObj.msg || "" }}
              </text>
            </view>
            <view v-if="!!list.length && requestEnd">
              <!-- <OrderItem v-for="(item,index) in list" :data="item" :key="index+'orderlist'" @finishedCountDown="finishedCountDown" v-show="!tabIndex"/> -->
              <order-item-all
                v-for="(item, index) in list"
                v-if="!tabIndex"
                :key="index"
                :data="item"
                @finishedCountDown="finishedCountDown"
              />
              <order-item-unpaid
                v-for="(item, index) in list"
                v-if="tabIndex === 1"
                :key="index"
                :data="item"
                @finishedCountDown="finishedCountDown"
              />
              <order-item-paid
                v-for="(item, index) in list"
                v-if="tabIndex === 2"
                :key="index"
                :data="item"
              />
              <order-item-close
                v-for="(item, index) in list"
                v-if="tabIndex === 3"
                :key="index"
                :data="item"
              />
              <order-item-refund
                v-for="(item, index) in list"
                v-if="tabIndex === 4"
                :key="index"
                :data="item"
              />
              <u-loadmore :status="loadStatus" />
            </view>
          </view>
          <!-- <DownloadBanner positionId="136"/> -->
          <!-- <PublicConsultationCard :scene="sceneType.wddd" :bury-param="buryParam" /> -->
        </div>
      </div>
    </login-layout>
  </view>
</template>

<script>
import OrderItemAll from "./order-item/item/order-all.vue";
import OrderItemUnpaid from "./order-item/item/unpaid-item.vue";
import OrderItemPaid from "./order-item/item/paid-item.vue";
import OrderItemClose from "./order-item/item/close-item.vue";
import OrderItemRefund from "./order-item/item/refund-item.vue"; 
import LoginLayout from "@/components/login/login-layout.vue"; 
import { whetherToLogIn } from "@/libs/tools"; 
import { ordersList } from "@/api/order.js";
import { sceneType, speedOrderType } from "@/libs/config"; 
import { getUserTokenStorage } from "@/libs/token";
import { getCurrentPageRoute, toConsultationPage } from "@/libs/turnPages.js";
import UTabs from "@/uview-ui/components/u-tabs/u-tabs.vue";
import ULoadingIcon from "@/uview-ui/components/u-loading-icon/u-loading-icon.vue";
import ULoadmore from "@/uview-ui/components/u-loadmore/u-loadmore.vue";
import AppPlaceholder from "@/components/app-components/app-placeholder/index.vue";
import HasPaidOrderTips from "@/components/HasPaidOrderTips.vue";
import { verifyLogin } from "@/libs/login";
export default {
  name: "MyOrder",
  components: {
    AppPlaceholder,
    ULoadmore,
    ULoadingIcon,
    UTabs,
    OrderItemAll,
    OrderItemUnpaid,
    OrderItemPaid,
    OrderItemClose,
    OrderItemRefund,
    HasPaidOrderTips,
    LoginLayout,
  },
  data() {
    return {
      sceneType,
      staticValue: 10, // //去详情页面后回来 不刷新数据
      tabIndex: 0,
      noDataObj: {
        img: require("./assets/no-order.png"),
        msg: "目前一个订单也没有",
      },
      // componentsName:['OrderItem','QuestionItem'],
      listTabs: [
        { name: "全部", status: "" },
        { name: "待支付", status: speedOrderType.UNPAID },
        { name: "已支付", status: speedOrderType.PAID },
        { name: "已关闭", status: speedOrderType.CLOSED },
        { name: "退款", status: speedOrderType.REFUNDED },
      ],
      list: [],
      loadingType: "",
      // 购买订单请求参数
      query: {
        status: "",
        currentPage: 1,
        pageSize: 10,
      },
      paidOrderInfo: {},
      pages: 0,
      isLast: false,
      loadStatus: "nomore",
      requestEnd: false, // 请求数据完成
      // buryParam: {
      // 	way: 'new',
      // 	pointCode: POINT_CODE.LAW_PAGE_ORDER_LIST_BOTTOM_FIXED_QUICKLY_CONSULT_CLICK,
      // 	transPath: globalTransformationPath.BOTTOM_OF_MY_ORDER,
      // },
      // countDownRequest: true, // 防多次请求
    };
  },
  onShow() {
    // 页面埋点
    // myorderPageBuriedPoint()
    if (this.staticValue === this.tabIndex && this.isLogin) {
      this.checkListStatus();
      return false;
    } else {
      Object.assign(this.$data, this.$options.data());
      this.init();
    }
    this.checkPaidOrder();
  },
  computed: {
    /* 是否登录*/
    isLogin() {
      return this.$store.getters["user/getToken"] && getUserTokenStorage();
    },
    isNoData() {
      return this.list.length === 0;
    },
    /** 判断是否有已支付的订单 */
    hasPaidOrder() {
      return !!this.paidOrderInfo.id;
    },
  },
  methods: {
    handleGoTo() {
      toConsultationPage({ index: 1 });
    },
    /** 查询是否有已支付的订单 */
    checkPaidOrder() {
      if (!verifyLogin()) return;

      ordersList({
        status: speedOrderType.PAID,
        currentPage: 1,
        pageSize: 1,
      })
        .then(({ data = {} }) => {
          this.paidOrderInfo = data.records?.[0] || {};
        })
        .catch(() => {
          this.paidOrderInfo = {};
        });
    },
    toLogin() {
      whetherToLogIn(() => {
        this.init();
        this.checkPaidOrder();
      });
    },
    tabChange(item) {
      Object.assign(this.$data, this.$options.data());
      console.log("tabChange:", item);
      this.tabIndex = item.index;
      this.staticValue = item.index;
      this.query.status = this.listTabs[this.tabIndex].status;
      this.loadData();
    },
    finishedCountDown() {
      // if (!this.countDownRequest) return false
      Object.assign(this.$data, this.$options.data());
      this.init();
    },
    loginSuccess() {
      this.init();
      console.log("登录成功");
    },
    init() {
      if (this.isLogin) {
        const index = Number(getCurrentPageRoute().query?.index);

        if (index) {
          console.log("传入了缓存", index);
          this.tabChange({
            index,
          });
        } else {
          this.loadData();
        }
      }
    },
    onPullDownRefresh() {
      console.log("refresh");
      // uni.stopPullDownRefresh();
    },
    onReachBottom() {
      this.loadStatus = "loading";
      this.loadData(this.tabIndex);
    },
    loadData() {
      if (!this.isLogin) {
        return;
      }
      console.log("loadData+query:", this.query);
      if (this.isLast) {
        this.loadStatus = "nomore";
        return uni.showToast({ title: "暂无更多数据", icon: "none" });
      }
      console.log("this.isLast:", this.isLast);
      if (this.query.currentPage === this.pages) {
        this.isLast = true;
      }
      try {
        ordersList(this.query)
          .then((res) => {
            let data = res.data;
            this.loadStatus = "nomore";
            this.pages = data.pages;
            // if (!data) return (this.isLast = true);
            if (!data || this.list?.length === data.total)
              return (this.isLast = true);
            const list = data?.records || [];
            this.query.currentPage++;
            this.list = [...this.list, ...(list || [])];
            console.log("orderlist111:", this.list);
          })
          .finally(() => {
            this.requestEnd = true;
          });
      } catch (error) {
        this.loadStatus = "nomore";
        this.requestEnd = true;
        console.log(error);
      }
    },
    // 更新数据
    checkListStatus() {
      const query = { ...this.query };
      query.currentPage -= 1;
      const id = uni.getStorageSync("myOrder.imSessionId");
      if (id) {
        ordersList(query).then(({ data = [] }) => {
          const res = data.records.find((el) => el.imSessionId === id);
          if (res) {
            const index = this.list.findIndex((el) => el.imSessionId === id);
            this.$set(this.list, index, res);
            uni.removeStorageSync("myOrder.imSessionId");
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.tabs {
  position: fixed;
  top: 0;
  height: 44px;
  background: #fff;
  width: 100%;
  z-index: 2;

  .line {
    position: absolute;
    width: 100%;
    bottom: 0;
    height: 1px;
    background: #ececec;
    // transform-origin: 0 0;
    // transform: scale(.5, .5);
  }
}

.myorder {
  box-sizing: border-box;
  width: 100%;
  background: #f5f5f7;
  padding-bottom: 64px;

  &-nodata {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  ::v-deep .box-item {
    margin-bottom: 12px;
  }

  ::v-deep .steps .steps-text {
    color: #999;
  }

  ::v-deep .steps-text--done {
    color: #3887f5 !important;
  }

  ::v-deep .u-count-down__text {
    color: #eb4738 !important;
    font-size: 12px !important;
  }

  .no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 25%;

    .no-data-img {
      width: 240px;
      height: 180px;
    }

    .desc {
      font-size: 14px;
      color: #999999;
      text-align: center;
    }
  }

  .no-login {
    .desc {
      color: #666;
    }

    .btn {
      width: 200px;
      height: 44px;
      background: #3887f5;
      border-radius: 68px 68px 68px 68px;
      font-size: 16px;
      color: #fff;
      text-align: center;
      line-height: 44px;
      margin-top: 24px;
    }
  }
}
</style>
