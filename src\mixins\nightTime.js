import { getDayOrderTime, getNightOrderTime } from "@/api/special.js";

function padNum(num) {
  return num < 10 ? "0" + num : num;
}
export default {
  data() {
    return {
      /** 夜间时间 */
      nightTime: [],
      isNight: false,
    };
  },
  mounted() {
    getNightOrderTime().then(({ data }) => {
      this.nightTime = JSON.parse(data.paramValue);
    });

    getDayOrderTime().then(({ data }) => {
      this.isNight = data === 0;
    });
  },
  computed: {
    timeIsNight() {
      return this.isNight;
    },
    _startTime() {
      const [start] = this.nightTime;
      return `${padNum(Math.floor(start / 60))}:${padNum(start % 60)}`;
    },
    _endTime() {
      const [, end] = this.nightTime;
      return `${padNum(Math.floor(end / 60))}:${padNum(end % 60)}`;
    },
  },
};
