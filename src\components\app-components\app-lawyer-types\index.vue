<template>
  <app-popup
    :round="16"
    :show="show"
    :zIndex="1005"
    mode="bottom"
  >
    <div class="goodat-box">
      <div class="select-button flex flex-space-between flex-align-center">
        <div class="select-button-placeholder" />
        <div class="button-title">
          全部类型
        </div>
        <div>
          <div
            class="button-cancel"
            @click="$emit('close', false)"
          >
            <img
              alt=""
              class="button-cancel-icon"
              src="@/pages/submit-question/imgs/cancellable.svg"
            >
          </div>
        </div>
      </div>
      <find-lawyer-types
        :list="list"
        @getCaseType="$emit('select', $event)"
      />
      <u-safe-bottom />
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import FindLawyerTypes from "@/components/findlawyer/find-lawyer-types/index.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "AppLawyerTypes",
  components: { USafeBottom, FindLawyerTypes, AppPopup },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
  },
};
</script>

<style lang="scss" scoped>
.goodat-box {
  background: #f5f5f7;
  border-radius: 16px 16px 0 0;
}

.select-button {
  height: 48px;
  padding: 0 16px;
  border-bottom: 0.5px solid #eeeeee;

  &-placeholder {
    width: 24px;
  }

  .button {
    &-cancel {
      font-size: 14px;
      font-weight: 400;
      color: #999999;

      &-icon {
        display: block;
        width: 24px;
        height: 24px;
      }
    }

    &-title {
      font-size: 16px;
      font-weight: bold;
      color: #000000;
    }

    &-define {
      font-size: 14px;
      font-weight: 400;
      color: $theme-color;
    }
  }
}
</style>
