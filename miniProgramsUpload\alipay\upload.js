const { minidev } = require("minidev");
const path = require("path");
const { writeFileSync, readFileSync, existsSync } = require("fs");
const axios = require("axios");

/**
 * 这个key值对应的是 package.json 中的 FILE_NAME
 */
const getMiniConfig = {
  "mp-alipay": {
    appId: "2021004126600177",
  },
  "mp-alipay-test": {
    appId: "2021004126600177",
  },
  "mp-al-hjls": {
    appId: "2021004125635453",
  },
  "mp-al-hjls-test": {
    appId: "2021004125635453",
  },
};

/**
 * 修改 mini.project.json 文件，添加 transpile 配置
 * @param {string} projectPath 项目路径
 */
const updateMiniProjectConfig = (projectPath) => {
  const configPath = path.resolve(projectPath, "mini.project.json");

  let config = {
    format: 2,
    compileOptions: {
      component2: true,
      transpile: {
        script: {
          ignore: ["node_modules/**"],
        },
      },
    },
  };

  // 写回文件
  try {
    writeFileSync(configPath, JSON.stringify(config, null, 2), "utf8");
    console.log("成功更新 mini.project.json 配置");
  } catch (error) {
    console.error("写入 mini.project.json 失败:", error.message);
    throw error;
  }
};

const uplpad = async ({ PROJECT_PATH, isDev, FILE_NAME, version }) => {
  // 上传前先更新 mini.project.json 配置
  updateMiniProjectConfig(PROJECT_PATH);

  const params = {
    ...getMiniConfig[FILE_NAME],
    project: PROJECT_PATH,
    deleteVersion: version,
    // version: version,
    // 添加 experience 选项的话可以一并把刚上传的版本设置为体验版
    experience: true,
  };

  // 开发环境不需要版本号，让支付宝自动生成
  if (isDev) {
    delete params.version;
  }

  const uploadResult = await minidev.upload(params, {
    onLog: (data) => {
      // 输出日志
      console.log(data);
    },
  });
  // 打印上传版本
  console.log(uploadResult, "打印上传版本");

  const { experienceQrCodeUrl } = uploadResult;

  const picName = `支付宝${isDev ? "测试" : "生产"}二维码.png`;

  // 上传成功后，将二维码地址写入文件
  writeFileSync(
    path.resolve(__dirname, `../img/${picName}`),
    await axios
      .get(experienceQrCodeUrl, { responseType: "arraybuffer" })
      .then((res) => res.data)
  );
};

module.exports = uplpad;
