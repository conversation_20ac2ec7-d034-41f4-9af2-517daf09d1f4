<template>
  <view
    v-if="bannerUrl"
    class="banner"
    @click="toDownloadPage"
  >
    <img
      :src="bannerUrl"
      alt=""
      class="img"
    >
    <!-- <img v-else class="img" src="./fix-banner-img.png" alt=""> -->
  </view>
</template>

<script>
import { advertListPosition } from "@/api/lawyer.js";

export default {
  props: {
    positionId: {
      type: [Number, String],
      required: true,
    },
  },
  data() {
    return {
      bannerUrl: "",
      addressUrl: "",
    };
  },
  mounted() {
    this.getBannerData();
  },
  methods: {
    toDownloadPage() {
      if (this.addressUrl) {
        if (this.addressUrl.indexOf("http") > -1) {
          uni.navigateTo({
            url:
              "/pages/downloadpage/index?addressUrl=" +
              encodeURIComponent(this.addressUrl),
          });
        } else {
          uni.navigateTo({
            url: `/pages/${this.addressUrl}/index`,
          });
        }
      }
    },
    /** 获取广告位数据 */
    getBannerData() {
      advertListPosition({ positionId: this.positionId }).then(({ data }) => {
        this.bannerUrl = data?.[0]?.advertContents?.[0]?.imageUrl || "";
        this.addressUrl = data?.[0]?.advertContents?.[0]?.addressUrl || "";
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.banner {
  position: fixed;
  width: 100%;
  height: 64px;
  bottom: 0;
  left: 0;
  .img {
    width: 100%;
    height: 64px;
    object-fit: cover;
  }
}
</style>
