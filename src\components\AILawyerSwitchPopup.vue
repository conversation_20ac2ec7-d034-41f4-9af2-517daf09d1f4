<template>
  <app-popup
    :show="show"
    :zIndex="zIndex"
    mode="center"
    showCancelButton
    @cancel="onCancel"
  >
    <div class="w-[311px] h-[334px] position-relative box-border">
      <img
        class="background-image"
        alt=""
        src="@/pages/myorder/assets/2dnxfj.png"
      >
      <div class="absolute bottom-[24px] absolute-center-x">
        <div
          class="w-[279px] h-[44px] bg-[linear-gradient(_119deg,_#3887F5_0%,_#1CDCFB_100%)] rounded-[12px] flex items-center justify-center mx-auto"
          @click="onAIUpgrade"
        >
          <div class="font-[500] text-[16px] text-[#FFFFFF] [text-shadow:0px_4px_4px_rgba(56,135,245,0.25)]">
            AI换律师
          </div>
        </div>
        <div
          class="text-[13px] text-[#CCCCCC] mt-[12px] text-center"
          @click="onRefund"
        >
          仍要退款
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import { toFastAskPage } from "@/libs/turnPages";

export default {
  name: "AiLawyerSwitchPopup",
  components: { AppPopup },
  props: {
    // 控制弹窗显示
    value: {
      type: Boolean,
      default: false
    },
    // 弹窗层级
    zIndex: {
      type: Number,
      default: 9997
    }
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
  methods: {
    /**
     * 点击AI换律师按钮
     */
    onAIUpgrade() {
      this.$emit("ai-upgrade");
    },
    
    /**
     * 点击仍要退款按钮
     */
    onRefund() {
      this.$emit("refund");
    },
    
    /**
     * 点击取消按钮
     */
    onCancel() {
      this.show = false;
      this.$emit("cancel");
    }
  }
};
</script>