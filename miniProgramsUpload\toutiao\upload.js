const tma = require("tt-ide-cli");
const path = require("path");
const { readFileSync, writeFileSync } = require("node:fs");

/** 由于现在新增了5个抖音小程序，所以需要将appId进行替换上传 */
function replaceAppId(appid) {
  const fileContent = readFileSync(
    path.resolve(__dirname, "../../dist/build/mp-toutiao/project.config.json"),
    "utf-8"
  );

  const fileJson = JSON.parse(fileContent);

  fileJson.appid = appid;

  writeFileSync(
    path.resolve(__dirname, "../../dist/build/mp-toutiao/project.config.json"),
    JSON.stringify(fileJson),
    "utf-8"
  );
}

function createdProjectConfig({ appid, name }) {
  return {
    appid,
    name
  };
}

async function uploadFile({ PROJECT_PATH, isDev, version }) {

  const TIKTOK_APP_ID = isDev
    ? [
      createdProjectConfig({
        appid: "tt594f2ff2c6714c3a01",
        name: "法临法律咨询"
      })
    ]
    : [
      // createdProjectConfig({
      //   appid: "ttedb09b0d7287fdf401",
      //   name: "法临24小时法律咨询"
      // }),
      // createdProjectConfig({
      //   appid: "ttfdb58b086282edf001",
      //   name: "法临来问律师"
      // }),
      // createdProjectConfig({
      //   appid: "ttb0e5c6582fd2a61c01",
      //   name: "法临律师"
      // }),
      // createdProjectConfig({
      //   appid: "tte32a95977c1df58e01",
      //   name: "法临律师在线咨询平台"
      // }),
      // createdProjectConfig({
      //   appid: "ttb3bdc5002c8ab56601",
      //   name: "法临律师咨询"
      // }),
      createdProjectConfig({
        appid: "tt594f2ff2c6714c3a01",
        name: "法临法律咨询"
      })
    ];

  for (const data of TIKTOK_APP_ID) {
    console.log(data.name, data.appid);

    replaceAppId(data.appid);

    const picName = `抖音${data.name}${isDev ? "测试" : "生产"}二维码.png`;

    const uploadResult = await tma.upload({
      project: {
        path: PROJECT_PATH // 项目地址
      },
      qrcode: {
        format: "imageFile", // imageSVG | imageFile | null | terminal
        // imageSVG 用于产出二维码 SVG
        // imageFile 用于将二维码存储到某个路径
        // terminal 用于将二维码在控制台输出
        // null 则不产出二维码
        output: path.resolve(__dirname, `../img/${picName}`), // 只在 imageFile 生效，填写图片输出绝对路径
        options: {
          small: false // 使用小二维码，主要用于 terminal
        }
      },
      copyToClipboard: true, // 是否将产出的二维码链接复制到剪切板
      // 本次更新日志
      changeLog: isDev
        ? `${version} - 测试版本，请勿提审`
        : `${version} - 版本更新`,
      // 本次更新版本，可选参数，默认值为前序版本号末位加一
      // version: getMiniVersion(),
      needUploadSourcemap: true
    });

    console.log(uploadResult);
  }
}

module.exports = ({ PROJECT_PATH, isDev, version }) => {
  uploadFile({
    PROJECT_PATH,
    isDev,
    version
  });
};
