<template>
  <view class="downloadpage">
    <!-- <web-view :webview-styles="webviewStyles" src="https://uniapp.dcloud.io/static/web-view.html"></web-view> -->
    <web-view
      :src="href"
      :webviewStyles="webviewStyles"
    />
  </view>
</template>

<script>

export default {
  props: {},
  data() {
    return {
      href: "",
      webviewStyles: {
        progress: {
          // color: '#3887F5'
        }
      }
    };
  },
  onLoad(option) {
    let title = "关注法临公众号";
    if (option.title) {
      title = option.title;
    }
    uni.setNavigationBarTitle({
      title
    });

    // this.href = 'https://testdownload.imlaw.cn/download_toutiao.html'
    if (option.addressUrl) {
      this.href = decodeURIComponent(option.addressUrl);
    } else {
      this.href = process.env.VUE_APP_ENV_DOWNLOADHREF;
    }
  }
};
</script>
