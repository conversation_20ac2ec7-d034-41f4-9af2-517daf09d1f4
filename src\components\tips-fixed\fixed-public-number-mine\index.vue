<template>
  <div>
    <!-- #ifdef MP-WEIXIN -->
    <div
      :style="[containerStyle]"
      class="share-container"
    >
      <div
        v-if="showShare"
        class="w-[375px] h-[64px] relative"
      >
        <img
          class="background-image img"
          src="@/components/tips-fixed/fixed-public-number-mine/imgs/<EMAIL>"
          alt=""
        >
        <img
          class="button-animation absolute top-[18px] right-[44px] w-[78px] h-[28px]"
          src="@/components/tips-fixed/fixed-public-number-mine/imgs/<EMAIL>"
          alt=""
          @click.stop="follow"
        >
        <img
          class="absolute top-[10px] right-[10px] w-[16px] h-[16px]"
          src="@/components/tips-fixed/fixed-public-number-mine/imgs/<EMAIL>"
          alt=""
          @click.stop="iconClick"
        >
      </div>
      <u-safe-bottom v-if="showSafe" />
    </div>
    <!-- #endif -->
  </div>
</template>

<script>
import { getGzhInfo } from "@/api/index.js";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { BURY_POINT_CHANNEL_TYPE, POINT_CODE } from "@/enum/burypoint.js";
import { buryPointChannelBasics } from "@/api/burypoint.js";
import { whetherToLogIn } from "@/libs/tools";
import { toOfficialAccountMini } from "@/libs/turnPages";
import { bindOnHook } from "@/libs/hooks";

export default {
  name: "FixedPublicNumberMine",
  components: { USafeBottom },
  props: {
    position: {
      type: String,
      default: "bottom",
      validator(value) {
        return ["none", "bottom", "top"].includes(value);
      },
    },
    /** 是否显示安全距离 */
    showSafe: {
      type: Boolean,
      default: false,
    },
    /** 是否跳转到公众号文章页 */
    isArticle: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showShare: false,
    };
  },
  computed: {
    containerStyle() {
      let style = {};

      switch (this.position) {
      case "bottom":
        style = {
          position: "fixed",
          left: 0,
          right: 0,
          bottom: 0,
        };
        break;

      case "top":
        style = {
          position: "fixed",
          left: 0,
          right: 0,
          top: 0,
        };
        break;

      default:
        style = {};
      }

      return uni.$u.addStyle(style);
    },
  },
  mounted() {
    buryPointChannelBasics({
      code: "LAW_APPLET_GO_FOLLOW_PUBLIC_PAGE",
      behavior: BURY_POINT_CHANNEL_TYPE.CK,
      type: 1,
    });

    bindOnHook.call(this, "onShow", () => {
      this.getGzhInfo();
    });

    this.getGzhInfo();
  },
  methods: {
    getGzhInfo() {
      // #ifdef MP-WEIXIN
      getGzhInfo().then(({ data }) => {
        if (data.gzhStatus === 0) {
          this.showShare = true;
        }
        if (data.gzhStatus === 1) {
          this.showShare = false;
        }
      });
      // #endif
    },
    iconClick() {
      this.showShare = false;
      this.$emit("iconClick");
    },
    /** 点击关注 */
    follow() {
      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_GO_FOLLOW_PUBLIC_BUTTON_CLICK,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
      });
      whetherToLogIn(() => {
        toOfficialAccountMini();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.share-container {
  z-index: 9999;
}
</style>
