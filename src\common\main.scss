* {
  padding: 0;
  margin: 0;
}

a {
  text-decoration: none;
}

html {
  height: 100%;
}

body {
  min-height: 100%;
  background: $floor-color;
}

/*.law-layouts-container{
  min-height: 100%;
}*/
ul {
  li {
    list-style: none;
  }
}

.container {
  min-width: 1100px;
  width: 1100px;
  max-width: 1100px;
  overflow: hidden;
}

.mg-auto {
  margin: 0 auto;
}

.flex-vertically-centered {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-column-centered {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-space-between {
  justify-content: space-between;
}

.flex-space-around {
  justify-content: space-around;
}

.flex-space-center {
  justify-content: center;
}

.flex-space-end {
  justify-content: flex-end;
}

.flex-align-center {
  align-items: center;
}

.flex-align-end {
  align-items: flex-end;
}

.flex-1 {
  flex: 1;
}

.font-size-0 {
  font-size: 0;
}

.auxiliary-text {
  color: $auxiliary-text-color;
  font-size: 14px;
}

.cursor-pointer {
  cursor: pointer;
}

.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-wrap: break-word;
  word-break: break-all;
}

.van-ellipsis {
  @extend .text-ellipsis;
}

.text-ellipsis-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
}

.van-multi-ellipsis--l3 {
  @extend .text-ellipsis-3
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
}

.van-multi-ellipsis--l2 {
  @extend .text-ellipsis-2
}

.title-content {
  font-size: 16px;
  color: $key-headlines-color;
}

.more {
  font-size: 14px;
  color: $theme-color;
}

.mg-tp-12 {
  margin-top: 12px;
}

.pd-tp-10 {
  padding-top: 10px;
}

.pd-tp-16 {
  padding-top: 16px;
}

.pd-tp-20 {
  padding-top: 20px;
}

.pd-tp-24 {
  padding-top: 24px;
}

.pd-tp-30 {
  padding-top: 30px;
}

.pd-lt-16 {
  padding-left: 16px;
}

.pd-lt-20 {
  padding-left: 20px;
}

.pd-rt-16 {
  padding-right: 16px;
}

.pd-lt-12 {
  padding-left: 12px;
}

.pd-lt-5 {
  padding-left: 5px;
}

.pd-rt-20 {
  padding-right: 20px;
}

.pd-bm-16 {
  padding-bottom: 16px;
}

.pd-bm-20 {
  padding-bottom: 20px;
}

.pd-bm-30 {
  padding-bottom: 30px;
}

.background-white {
  background: white;
}

.text-align-rt {
  text-align: right;
}

.text-align-center {
  text-align: center;
}

.floor-color {
  background: $floor-color;
}

.sub-title {
  background: #fff;
  line-height: 40px;
  font-size: 14px;
  color: #46474B;
  font-weight: bold;
  padding: 0 16px;
  border-bottom: 1px solid #ECECEC;
}


.position-relative {
  position: relative;
}

.position-absolute {
  position: absolute;
}

.position-fixed {
  position: fixed;
}

.font11 {
  font-size: 11px;
}

.font12 {
  font-size: 12px;
}

.font13 {
  font-size: 13px;
}

.font14 {
  font-size: 14px;
}

.font15 {
  font-size: 15px;
}

.font16 {
  font-size: 16px;
}

.font17 {
  font-size: 17px;
}

.font18 {
  font-size: 18px;
}

.font19 {
  font-size: 19px;
}

.line-12 {
  height: 12px;
  background: #F5F5F7;
}

.line-1 {
  height: 1px;
  background: #F5F5F7;
}

.none-data-placeholder-parent {
  text-align: center;
  position: relative;

  .none-data-placeholder {
    width: 7rem;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);

  }
}

// 提交按钮
.to-submit {
  // background: #fff;
  height: 45px;

  .submit {
    width: 343px;
    height: 40px;
    background: #4A549E;
    border-radius: 20px;
    color: #FFFFFF;
    line-height: 40px;
    text-align: center;
  }

  .no-submit {
    background: #C8C8C8;
  }
}

// 短的小按钮
.btn-short {
  width: 74px;
  height: 32px;
  background: #4A549E;
  border-radius: 20px;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
}

// 详情下方tag标签
.detail-tag {
  display: flex;
  padding-top: 12px;
  padding-left: 16px;
  overflow-y: auto;
  scrollbar-width: none; /* firefox */
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }

  .tag {
    text-align: center;
    margin-right: 12px;
    // width: 89px;
    height: 20px;
    line-height: 20px;
    background: rgba(166, 166, 166, 0.1);
    border-radius: 10px;
    font-size: 11px;
    color: #6B6B6F;
    padding: 0 2px;
    // box-sizing: border-box;
    min-width: 50px;
    max-width: 100px;
  }
}

.law-detail {
  padding-bottom: 80px;
}
