/**
 *
 * ! 因为小程序在切换页面和后退的时候存在缓存机制，会组件生命周期不会重新加载
 * ! 这里循环挂载到 page 页面 的 onShow 方法上，并且要在 page 界面中声明对应的生命周期
 * @param {string} hook 钩子名称
 * @param {function} callback 回调
 */
export function bindOnHook(hook, callback) {
  let parent = this.$parent || this;

  // 通过循环找到page页面，为后面调用onShow生命周期做准备
  while (parent.$parent) {
    parent = parent.$parent;
  }

  console.log(hook, "创建", parent.$on);

  parent.$on(`hook:${ hook }`, callback);
  return parent;
}
