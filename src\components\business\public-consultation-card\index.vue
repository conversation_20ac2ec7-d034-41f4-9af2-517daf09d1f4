<template>
  <PublicConsultationGuideEntrance
    :scene="scene"
    @getServiceManegeInfoCommon="initPrice"
  >
    <img
      alt=""
      class="lawyer"
      src="./assets/<EMAIL>"
      @click="toPayConfirm(data)"
    >
    <div class="flex-placeholder" />
  </PublicConsultationGuideEntrance>
</template>

<script>

import PublicConsultationGuideEntrance from "@/components/public-consultation-guide-entrance";
import { toConfirmOrderPlus, whetherToLogIn } from "@/libs/tools";
// import buryPointTransformationPath from "@/libs/buryPointTransformationPath.js";
// import { buryPointChannelBasics } from "@/api/burypoint.js";
// import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint.js";
export default {
  name: "PublicConsultationCard",
  components: {
    PublicConsultationGuideEntrance
  },
  inheritAttrs: false,
  props: {
    scene: {
      type: String,
      default: ""
    },
    /**
     * way: 'add',
     pointCode: POINT_CODE.LAW_PAGE_ORDER_LIST_BOTTOM_FIXED_QUICKLY_CONSULT_CLICK,
     transPath: globalTransformationPath.BOTTOM_OF_MY_ORDER
     */
    buryParam: {
      type: Object,
      default: () => ({ way: "add" })
    }
  },
  data() {
    return {
      data: {}
    };
  },
  methods: {
    toPayConfirm(){
      whetherToLogIn(() => {
        // buryPointChannelBasics({ // 2.1.6
        //   code: this.buryParam.pointCode,
        //   behavior: BURY_POINT_CHANNEL_TYPE.CK,
        // })
        // buryPointTransformationPath[this.buryParam.way](this.buryParam.transPath)
        toConfirmOrderPlus(this.scene);
      });
    },

    /** 获取服务价格信息 */
    initPrice(data) {
      this.data = data;
    },
  }
};
</script>

<style lang="scss" scoped>
.lawyer {
  width: 375px;
  height: 73px;
  position: fixed;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
}

.flex-placeholder {
  height: 100px;
}
</style>
