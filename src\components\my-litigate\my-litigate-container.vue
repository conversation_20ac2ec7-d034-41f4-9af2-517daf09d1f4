<template>
  <div
    :class="['my-litigate-container', positionClass]"
  >
    <div
      v-if="!!title"
      class="flex flex-space-between flex-align-center"
    >
      <div class="title">
        {{ title }}
        <span class="subtitle">{{ subtitle }}</span>
      </div>
      <slot name="tips" />
    </div>
    <slot />
  </div>
</template>

<script>
/**
 * 封装的界面板块容器
 * <AUTHOR>
 * @Version 2.10.0
 * @Description 该版本改动信息
 * @Date 2022/4/23
 * @FilePath components\my-litigate\my-litigate-container.vue
 */
export default {
  name: "MyLitigateContainer",
  props: {
    /** 标题 */
    title: {
      type: String,
      default: "",
    },
    /** 副标题 */
    subtitle: {
      type: String,
      default: "",
    },
    /** 位置 */
    position: {
      type: String,
      default: "",
      validator(value) {
        return ["top", "bottom", ""].includes(value);
      },
    },
  },
  computed: {
    /** 容器位置对应的类名 */
    positionClass() {
      switch (this.position) {
      case "top":
        return "my-litigate-container--top";
      case "bottom":
        return "my-litigate-container--bottom";
      default:
        return "";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.my-litigate-container {
  width: 100%;
  margin-bottom: 12px;
  background: #fff;
  border-radius: 12px;
  padding-left: 16px;
  padding-right: 16px;
  box-sizing: border-box;

  &--top {
    border-radius: 0 0 16px 16px;
  }

  &--bottom {
    border-radius: 16px 16px 0 0;
    margin-bottom: 0;
  }

  .title {
    height: 22px;
    font-size: 16px;
    font-weight: bold;
    padding: 16px 0;
    color: #333333;
  }

  .subtitle {
    font-size: 12px;
    font-weight: 400;
    color: #999999;
  }
}
</style>
