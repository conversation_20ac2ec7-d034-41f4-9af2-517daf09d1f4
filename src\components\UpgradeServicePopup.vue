<template>
  <div>
    <app-popup
      :show="show"
      :zIndex="9998"
      mode="center"
      showCancelButton
      @cancel="show = false"
    >
      <div class="w-[311px] h-[381px] box-border position-relative pt-[78px]">
        <img
          alt=""
          class="background-image"
          src="../pages/rapid-consultation-confirm-order/my-consultation/assets/bg1.png"
        >
        <div class="text-[14px] text-[#333333] px-[28px]">
          当前<span class="text-[#EB4738]">134</span>人排队中...免费咨询队列较长
          升级1V1咨询律师无须排队 最快<span class="text-[#EB4738]">10秒</span>接入
        </div>
        <div class="flex items-center px-[8px] mt-[25px] justify-between">
          <div
            class="w-[143px] h-[214px] position-relative pt-[142px] px-[10px] box-border"
            @click="handleClick1"
          >
            <img
              alt=""
              class="background-image"
              src="../pages/rapid-consultation-confirm-order/my-consultation/assets/Frame1321315927.png"
            >
            <div class="font-bold text-[16px] text-[#F34747] text-center">
              ¥{{ serviceInfo1.servicePrice | amountFilter }}
            </div>
            <div
              class="w-[123px] h-[32px] mt-[12px] bg-[linear-gradient(_109deg,_#FF913E_0%,_#F54A3A_100%)] rounded-[46px] text-[13px] text-[#FFFFFF] flex items-center justify-center box-border"
            >
              升级到专家律师
            </div>
          </div>
          <div
            class="w-[143px] h-[214px] position-relative pt-[142px] px-[10px] box-border"
            @click="handleClick2"
          >
            <img
              alt=""
              class="background-image"
              src="../pages/rapid-consultation-confirm-order/my-consultation/assets/Frame1321315928.png"
            >
            <div class="font-bold text-[16px] text-[#F34747] text-center">
              ¥{{ serviceInfo2.servicePrice | amountFilter }}
            </div>
            <div
              class="w-[123px] h-[32px] mt-[12px] bg-[linear-gradient(_109deg,_#FF913E_0%,_#F54A3A_100%)] rounded-[46px] text-[13px] text-[#FFFFFF] flex items-center justify-center box-border"
            >
              升级到高级律师
            </div>
          </div>
        </div>
      </div>
    </app-popup>
    <div
      v-if="!show && countDown > 0"
      class="w-[148px] h-[50px] block fixed right-0 bottom-[159px] pl-[56px] pt-[8px] box-border"
      @click="open"
    >
      <img
        alt=""
        class="background-image"
        src="../pages/rapid-consultation-confirm-order/my-consultation/assets/111.png"
      >
      <div class="text-[12px] text-[#333333]">
        专家律师咨询
      </div>
      <div class="flex items-center">
        <div class="font-bold text-[13px] text-[#F34747] flex items-center">
          <div>{{ minutes }}</div>
          <div class="mx-[6px]">
            :
          </div>
          <div>{{ seconds }}</div>
        </div>
        <img
          alt=""
          class="w-[20px] h-[8px] block ml-[6px]"
          src="../pages/rapid-consultation-confirm-order/my-consultation/assets/<EMAIL>"
        >
      </div>
    </div>
  </div>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import countDownMixin from "@/pages/rapid-consultation-confirm-order/my-consultation/js/countDownMixin";
import { serviceManegeInfoCommon } from "@/api";
import { sceneType } from "@/libs/config";
import { payServiceCommon } from "@/libs/payServiceCommon";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath";

export default {
  name: "UpgradeServicePopup",
  components: { AppPopup },
  mixins: [countDownMixin],
  props: {
    /** 是否展开 */
    isOpen: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      serviceInfo1: {},
      serviceInfo2: {},
      countDown: 300,
      show: this.isOpen
    };
  },
  watch: {
    show: {
      handler(val) {
        if (!val) {
        } else {
          buryPointChannelBasics({
            code: "LAW_APPLET_QA_MESSAGE_UPGRADE_LAWYER_POP_UP_PAGE",
            type: 1,
            behavior: BURY_POINT_CHANNEL_TYPE.CK
          });
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.getServiceInfo();
  },
  methods: {
    /** 获取服务信息 */
    getServiceInfo() {
      serviceManegeInfoCommon(sceneType.sjfw1).then(({ data }) => {
        this.serviceInfo1 = data;
      });

      serviceManegeInfoCommon(sceneType.sjfw2).then(({ data }) => {
        this.serviceInfo2 = data;
      });
    },
    /** 去付款 */
    handleClick1() {
      buryPointTransformationPath.add(5044);

      buryPointChannelBasics({
        code:
          "LAW_APPLET_QA_MESSAGE_UPGRADE_LAWYER_POP_UP_PAGE_EXPERT_LAWYER_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      payServiceCommon({
        serviceCode: this.serviceInfo1.serviceCode
      });
    },
    handleClick2() {
      buryPointTransformationPath.add(5045);

      buryPointChannelBasics({
        code:
          "LAW_APPLET_QA_MESSAGE_UPGRADE_LAWYER_POP_UP_PAGE_ADVANCED_LAWYER_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      payServiceCommon({
        serviceCode: this.serviceInfo2.serviceCode
      });
    },
    /** 点击展开弹窗 */
    open() {
      this.show = true;
    }
  }
};
</script>
