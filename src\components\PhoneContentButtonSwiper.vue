<template>
  <div class="card position-relative">
    <div class="tabs-mark-white tabs-mark-white-left" />
    <div class="tabs-mark-white tabs-mark-white-right" />
    <div
      :style="{ transform: `translateX(${translateX * 2 + 'rpx'})` }"
      class="swiper-container flex flex-align-center"
    >
      <div
        :style="{ left: container1X * 2 + 'rpx' }"
        class="swiper flex flex-align-center"
      >
        <div
          v-for="(item, index) in swiperList"
          :key="index"
          class="swiper-item"
        >
          <img
            alt=""
            class="swiper-item-image"
            src="@/pages/rapid-consultation-confirm-order/img/<EMAIL>"
          >
          <p>{{ item }}</p>
        </div>
      </div>
      <div
        :style="{ left: container2X * 2 + 'rpx' }"
        class="swiper flex flex-align-center"
      >
        <div
          v-for="(item, index) in swiperList"
          :key="index"
          class="swiper-item"
        >
          <img
            alt=""
            class="swiper-item-image"
            src="@/pages/rapid-consultation-confirm-order/img/<EMAIL>"
          >
          <p>{{ item }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "PhoneContentButtonSwiper",
  data() {
    return {
      swiperList: [
        "成都尾号2836,3秒前咨询了婚姻家事问题",
        "湖南尾号3556,7秒前咨询了债权债务问题",
        "广州尾号6489,10秒前咨询了交通事故问题",
        "吉林尾号2578,11秒前咨询了劳动纠纷问题",
        "甘肃尾号1845,15秒前咨询了劳动工伤问题",
        "湖北尾号7383,16秒前咨询了经济纠纷问题",
        "山西尾号2839,18秒前咨询了债权债务问题",
        "西安尾号2899,20秒前咨询了交通事故问题",
        "北京尾号9377,22秒前咨询了婚姻家庭问题",
        "上海尾号3192,25秒前咨询了交通事故问题",
      ],
      translateX: 0,
      /** 容器1偏移量 */
      container1X: 0,
      /** 容器2偏移量 */
      container2X: 0,
    };
  },
  mounted() {
    this.startSwiper();
  },
  methods: {
    /** 让图片轮播起来 */
    startSwiper() {
      const length = this.swiperList.length;
      /** 间距 */
      const space = 277;
      /** 当前正在展示的容器 */
      let currentContainer = 1;
      this.container2X = space * length;
      /** 记录播放了几次 */
      let count = 1;

      const timer = setInterval(() => {
        this.translateX -= 3;

        // ! 这里间隔了一个space，是为了防止动画过程中消失的情况
        if (
          this.translateX <= -((space + 1) * count * this.swiperList.length)
        ) {
          count += 1;

          if (currentContainer === 1) {
            this.container1X = space * length * count;
            currentContainer = 2;
          } else {
            this.container2X = space * length * count;
            currentContainer = 1;
          }
        }
      }, 100);

      this.$once("hook:beforeDestroy", () => {
        clearInterval(timer);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.tabs-mark-white{
  &-left{
    left: 0;
    right: auto;
    background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 1) 0%,
            rgba(255, 255, 255, 0) 100%
    );
  }
}
.card {
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  background-color: #fff;
}


.swiper-container {
  transition: transform 0.3s;
  position: relative;
  height: 23px;
  margin-top: 8px;
  margin-bottom: 8px;

  .swiper {
    position: absolute;
    padding-left: 16px;

    &-item {
      display: flex;
      align-items: center;
      width: 267px;
      height: 23px;
      margin-right: 10px;
      font-size: 12px;
      font-weight: 400;
      color: #888888;
      background-color: #F7F8FA;
      border-radius: 33px;
      padding-left: 4px;
      box-sizing: border-box;

      &-image {
        display: block;
        width: 16px;
        margin-right: 4px;
        height: 16px;
      }
    }
  }
}
</style>
