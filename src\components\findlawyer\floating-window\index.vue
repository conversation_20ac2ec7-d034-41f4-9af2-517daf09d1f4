<template>
  <div>
    <div class="floating-window-placeholder" />
    <div class="position-relative floating-window">
      <img
        alt=""
        class="background-image"
        src="../../../pages/submit-question/findlawyer/imgs/<EMAIL>"
      >
      <img
        alt=""
        class="close"
        src="../../../pages/submit-question/findlawyer/imgs/<EMAIL>"
        @click="$emit('close')"
      >
      <div class="flex flex-space-between flex-align-center floating-content">
        <div class="info">
          <div class="title flex flex-align-center">
            <span class="text-ff8a36">快速</span>问律师
          </div>
          无需选择 自动匹配专业律师
        </div>
        <div
          class="btn"
          @click="toAskLawyer"
        >
          去咨询
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { setTodayTime } from "@/libs/token.js";
import { toAskLawyer } from "@/libs/turnPages.js";
import { buryPointChannelBasics } from "@/api/burypoint";
import {
  BURY_POINT_CHANNEL_TYPE,
  FROM_PAGE,
  POINT_CODE,
} from "@/enum/burypoint";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath";

export default {
  name: "FloatingWindow",
  mounted() {
    buryPointChannelBasics({
      code: POINT_CODE.LAW_APPLET_FIND_LAWYER_POPUP_PAGE,
      type: 1,
      behavior: BURY_POINT_CHANNEL_TYPE.CK,
    });

    setTodayTime();
    const timer = setTimeout(() => {
      this.$emit("close");
    }, 5000);
    this.$once("hook:beforeDestroy", () => {
      clearTimeout(timer);
    });
  },
  methods: {
    toAskLawyer() {
      buryPointTransformationPath.addDataSources({
        fromPage: FROM_PAGE.ZLS_DBBANNER,
      });

      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_FIND_LAWYER_POPUP_CLICK,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
      });

      toAskLawyer();
    },
  },
};
</script>

<style lang="scss" scoped>
.floating-window-placeholder {
  height: 92px;
}

.floating-window {
  height: 92px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;

  .close {
    position: absolute;
    width: 12px;
    height: 12px;
    z-index: 9;
    top: 16px;
    right: 16px;
  }

  .floating-content {
    padding-left: 78px;
    padding-top: 21px;
    padding-right: 36px;

    .info {
      font-size: 13px;
      font-weight: 400;
      color: #666666;

      .title {
        padding-bottom: 4px;
        font-size: 16px;
        font-weight: 600;
        color: #333333;
      }
    }

    .btn {
      width: 66px;
      line-height: 28px;
      background: #f78c3e;
      border-radius: 68px 68px 68px 68px;
      font-size: 14px;
      font-weight: 400;
      color: #ffffff;
      text-align: center;
    }
  }
}
</style>
