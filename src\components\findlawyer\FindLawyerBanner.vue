<template>
  <div
    class="relative -z-10 mx-auto pt-[12px] w-[351px] h-[74px] bg-[#f5f5f7]"
    @click="toAskLawyer"
  >
    <img
      alt=""
      class="w-full h-full block"
      src="../../pages/submit-question/findlawyer/imgs/banner1.png"
    >
    <swiper
      autoplay
      circular
      class="w-[215px] h-[22px] absolute left-[12px] bottom-[12px]"
      vertical
    >
      <swiper-item
        v-for="(item, index) in list"
        :key="index"
        class="w-full h-full"
      >
        <div
          class="box-border h-full flex items-center pl-[2px] pr-[8px] bg-[rgba(255,255,255,0.72)] rounded-[59px]"
        >
          <img
            :src="item.url"
            alt=""
            class="w-[18px] h-[18px] block rounded-full"
          >
          <div class="text-[12px] text-[#333333] ml-[4px]">
            {{ item.content }}
          </div>
        </div>
      </swiper-item>
    </swiper>
  </div>
</template>

<script>
import { toAskLawyer } from "@/libs/turnPages";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";

export default {
  name: "FindLawyerBanner",
  data() {
    return {
      list: [
        {
          url: require("@/pages/submit-question/findlawyer/imgs/banner/1.png"),
          content: "用户**36刚刚提交了婚姻家事问题"
        },
        {
          url: require("@/pages/submit-question/findlawyer/imgs/banner/2.png"),
          content: "用户**56刚刚提交了债权债务问题"
        },
        {
          url: require("@/pages/submit-question/findlawyer/imgs/banner/3.png"),
          content: "用户**89刚刚提交了交通事故问题"
        },
        {
          url: require("@/pages/submit-question/findlawyer/imgs/banner/4.png"),
          content: "用户**57刚刚提交了劳动纠纷问题"
        },
        {
          url: require("@/pages/submit-question/findlawyer/imgs/banner/5.png"),
          content: "用户**45刚刚提交了劳动工伤问题"
        },
        {
          url: require("@/pages/submit-question/findlawyer/imgs/banner/6.png"),
          content: "用户**83刚刚提交了经济纠纷问题"
        },
        {
          url: require("@/pages/submit-question/findlawyer/imgs/banner/7.png"),
          content: "用户**39刚刚提交了债权债务问题"
        },
        {
          url: require("@/pages/submit-question/findlawyer/imgs/banner/8.png"),
          content: "用户**99刚刚提交了交通事故问题"
        },
        {
          url: require("@/pages/submit-question/findlawyer/imgs/banner/9.png"),
          content: "用户**77刚刚提交了婚姻家庭问题"
        },
        {
          url: require("@/pages/submit-question/findlawyer/imgs/banner/10.png"),
          content: "用户**19刚刚提交了交通事故问题"
        }
      ]
    };
  },
  methods: {
    /** 跳转到问律师界面 */
    toAskLawyer() {
      buryPointChannelBasics({
        code: "LAW_APPLET_FIND_LAWYER_TOP_BANNER_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      toAskLawyer();
    }
  }
};
</script>
