<template>
  <div
    v-if="shouldShow"
    @click="handleFollow"
  >
    <img
      class="block w-[375px] h-[46px]"
      alt=""
      src="@/assets/imgs/5wsprj.png"
    >
  </div>
</template>

<script>
import { getGzhInfo } from "@/api/index.js";
import { toOfficialAccountMini } from "@/libs/turnPages.js";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint.js";
import { buryPointChannelBasics } from "@/api/burypoint.js";
import { whetherToLogIn } from "@/libs/tools";

export default {
  name: "FollowOfficialAccount",
  data() {
    return {
      isFollowed: false, // 是否已关注公众号
    };
  },
  computed: {
    // 是否应该显示组件
    shouldShow() {
      // 只有在微信小程序环境下且未关注时才显示
      // #ifdef MP-WEIXIN
      return !this.isFollowed;
      // #endif
      // #ifndef MP-WEIXIN
      return false;
      // #endif
    },
  },
  mounted() {
    this.checkFollowStatus();
  },
  methods: {
    /**
     * 检查当前用户是否已关注公众号
     */
    checkFollowStatus() {
      // #ifdef MP-WEIXIN
      getGzhInfo()
        .then(({ data }) => {
          // gzhStatus: 0=未关注, 1=已关注
          this.isFollowed = data.gzhStatus === 1;
        })
        .catch((error) => {
          console.error("获取公众号关注状态失败:", error);
          // 获取失败时默认显示组件
          this.isFollowed = false;
        });
      // #endif
    },

    /**
     * 处理点击关注事件
     */
    handleFollow() {
      buryPointChannelBasics({
        code: "LAW_APPLET_FREECONSULT_MESSAGE_PAGE_FOLLOW_NOW_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
      });

      whetherToLogIn(() => {
        // 跳转到关注公众号页面
        toOfficialAccountMini();
      });
    },
  },
};
</script>
