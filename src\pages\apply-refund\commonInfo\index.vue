<template>
  <div class="order-detail-info flex flex-align-center flex-space-between">
    <img
      :src="imgIcon"
      alt=""
      class="img"
    >
    <div
      class="order-detail-info-right flex-1 flex flex-column flex-space-between"
    >
      <p class="line flex flex-align-center flex-space-between">
        <span class="span font17 text-ellipsis">{{ data.serviceName }}</span>
        <span class="span font17">¥ {{ (data.refundAmount || data.payAmount || data.price) | amountFilter }}</span>
      </p>
      <p class="line flex flex-align-center flex-space-between">
        <span
          v-if="data.refundTypeStr === 'newOrder' && data.serviceLength"
          class="font12 span"
        >购买规格：{{ data.serviceLength + (data.unitLabel || "分钟") }}</span>
        <!-- 老订单退款 -->
        <span
          v-else-if="data.serviceLength"
          class="span font12"
        >购买规格：{{ data.serviceLength + data.serviceLengthUnit }}</span>
        <span
          v-if="data.createTime"
          class="span font13"
        >{{ data.createTime }}</span>
      </p>
    </div>
  </div>
</template>

<script>
/** 订单详情头部 */
/**
caseSourceType
  3: 快速咨询（极速咨询） 11:落地页
  4：图文付费资讯（一对一）
  5：案件委托案源
  6：电话咨询案源
  8：电话咨询案源（一对一）
 */
export default {
  name: "DetailCard",
  components: {},
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {};
  },
  computed: {
    imgIcon() {
      return require("./assets/order3.png");
      // let img = require('@/assets/order/order4.png')
      // if (Number(this.data.caseSourceType) === 11) {
      //   img = require('@/assets/order/order3.png')
      // } else {
      //   img = require(`@/assets/order/order${this.data.caseSourceType}.png`)
      // }
      // if (img) return img
      // else return require('@/assets/order/order4.png')
    },
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
.order-detail-info {
  box-sizing: border-box;

  .img {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    margin-right: 12px;
  }
  &-right {
    height: 50px;
    .line:nth-child(1) {
      color: #333333;
      font-weight: bold;
      .span:nth-child(1) {
        max-width: 200px;
      }
    }
    .line:nth-child(2) {
      color: #666666;
      margin-top: 3px;
    }
  }
}
</style>
