<template>
  <div>
    <div :style="[positionStyle]">
      <div
        v-if="show"
        :class="[needWidth?'w-[375px] px-[12px]':'']"
        class="box-border relative"
      >
        <div
          v-if="showHead"
          class="flex items-center justify-center pb-[12px]"
        >
          <div class="font-bold text-[15px] text-[#333333] pr-[4px]">
            <!--  没有看过视频           -->
            <slot
              v-if="havenTSeenTheAds"
              name="havenTSeenTheAds"
            />
            <!--  看过视频         -->
            <slot
              v-else
              name="watchedTheVideo"
            />
          </div>
          <img
            class="w-[15px] h-[15px]"
            src="@/components/AdPopup/img/<EMAIL>"
            alt=""
          >
        </div>
        <div class="flex items-center pt-[10px] bg-[#FFF2D6] pb-[21px] px-[12px]  rounded-[8px]">
          <img
            class="w-[18px] h-[18px] pr-[4px]"
            src="@/components/AdPopup/img/<EMAIL>"
            alt=""
          >
          <p
            v-if="havenTSeenTheAds"
            class="text-[13px] text-[#444444]"
          >
            当前免费咨询队列人数较多，排队咨询数<span class="text-[#E4393C]">200</span>
          </p>
          <p
            v-else
            class="text-[13px] text-[#444444]"
          >
            加速成功，您目前的咨询队列为第{{ rank }}
          </p>
        </div>
        <div class="px-[12px] pt-[10px] mt-[-10px] bg-[#FFFFFF] [box-shadow:0px_2px_12px_0px_rgba(0,0,0,0.06)] rounded-[8px]">
          <div class="flex items-center pb-[10px]">
            <div class="font-bold text-[15px] text-[#333333]  flex items-center">
              加速进度: <div class="pl-[7px] flex items-center">
                <gcm-digital-scroll
                  :value="speed"
                  :size="15"
                  color="#EB4738"
                /><span class="text-[15px] text-[#EB4738]">%</span>
              </div>
            </div>
            <i class="h-[16px] w-[1px] bg-[#EEEEEE] mx-[7px]" />
            <div class="font-bold text-[15px] text-[#333333] flex items-center">
              我的排名: <div class="pl-[7px]">
                <gcm-digital-scroll
                  :value="rank"
                  :size="15"
                  color="#EB4738"
                />
              </div>
            </div>
          </div>
          <div>
            <div class="relative">
              <!--总进度条              -->
              <div class="h-[6px] bg-[#F1F3F4] rounded-[30px]" />
              <!--当前进度              -->
              <div
                class="absolute left-0 top-50% flex items-center absolute-center-y"
                :style="progressStyle"
              >
                <!--      当前进度条          -->
                <div class="w-full h-[6px] bg-[linear-gradient(_109deg,_#FFB759_0%,_#FF7721_100%)] rounded-[30px]" />
                <!--      当前进度文案          -->
                <div class="ml-[-4px] relative">
                  <img
                    class="w-[14px] h-[14px]"
                    src="@/components/AdPopup/img/<EMAIL>"
                    alt=""
                  >
                  <div class="absolute top-[16px] absolute-center">
                    <img
                      class="w-[7px] h-[3px] block m-auto"
                      src="@/components/AdPopup/img/<EMAIL>"
                      alt=""
                    >
                    <div class=" px-[8px] py-[2px] font-bold text-[12px] text-[#FFFFFF] bg-[linear-gradient(_109deg,_#FFB759_0%,_#FF7721_100%)] [box-shadow:0px_0px_12px_2px_rgba(0,0,0,0.05)] rounded-[30px]">
                      {{ rank }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex items-center justify-between pt-[6px]">
              <p class="text-[13px] text-[#999999]">
                排队中...
              </p>
              <p class="text-[13px] text-[#999999]">
                咨询队列
              </p>
            </div>
          </div>
          <div class="flex justify-between items-center py-[13px]">
            <div>
              <p class="font-bold text-[15px] text-[#333333]">
                {{ promptCopywriting.title }}
              </p>
              <p class="text-[13px] text-[#999999] pt-[2px]">
                {{ promptCopywriting.desc }}
              </p>
            </div>
            <div
              class="font-bold text-[14px] w-[90px] h-[28px] bg-[linear-gradient(_90deg,_#FA700D_0%,_#F34747_100%)] rounded-[48px] flex items-center justify-center text-[#FFFFFF]"
              @click="showAd"
            >
              <img
                class="w-[16px] h-[16px]"
                src="@/components/AdPopup/img/<EMAIL>"
                alt=""
              >
              去加速
            </div>
          </div>
        </div>
        <!--        <img
          alt=""
          class="absolute top-0 right-0 w-[14px] h-[14px] block"
          src="@/components/AdPopup/img/close.png"
          @click="close"
        >
        <img
          alt=""
          class="w-[28px] h-[28px] rounded-[100px] shrink-0"
          src="@/components/AdPopup/img/<EMAIL>"
        >
        <div class="text-[14px] text-[#333333] ml-[6px]">
          <slot />
        </div>
        <div
          class="w-[80px] h-[28px] rounded-[48px] shrink-0 text-[14px] text-[#EB4738] flex items-center justify-center box-border border-[1px] border-solid border-[#EB4738]"
          @click="showAd"
        >
          立即加速
        </div>-->
      </div>
      <u-safe-bottom v-if="showSafe" />
    </div>

    <app-placeholder
      v-if="position === 'bottom'&&show"
      height="250"
    />
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import props from "./props";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import AdMixin from "@/mixins/adMixin";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import { getCommonConfigKey } from "@/api/order";
import { addStyle } from "@/uview-ui/libs/function";
import GcmDigitalScroll from "@/components/gcm-digital-scroll/index.vue";
import AppPlaceholder from "@/components/app-components/app-placeholder/index.vue";
import { pxToRpx } from "@/libs/tools";
import { isNull } from "@/libs/basics-tools";
import { getUserTokenStorage } from "@/libs/token";

export default {
  name: "AdPopupWrapper",
  components: { AppPlaceholder, GcmDigitalScroll, USafeBottom },
  mixins: [AdMixin],
  props,
  data() {
    return {
      show: false,
      /* 排名数*/
      rank: 200,
      /* 加速进度*/
      speed: 0,
      /* 总排名数*/
      totalRank: 300,
      isAnimation: false,
      /* 广告观看次数*/
      theNumberOfAdViews: 0,
      /* 点击时间 用于统计视频播放时长*/
      videoDate: "",
      oldRank: 0
    };
  },
  computed: {
    ...mapGetters({
      rewardedVideoAd: "adState/getRewardedVideoAd"
    }),
    /* 排名进度*/
    progress(){
      return `${Math.ceil(this.rank / this.totalRank * 100)}%`;
    },
    /* 是不是第一次看广告*/
    itSNotTheFirstTimeIVeSeenAnAd(){
      return this.theNumberOfAdViews === 1;
    },
    /* 是不是多次看广告*/
    isItToWatchTheAdManyTimes(){
      return this.theNumberOfAdViews > 1;
    },
    /* 是不是没有看过广告*/
    havenTSeenTheAds(){
      return this.theNumberOfAdViews === 0;
    },
    /* 提示文案*/
    promptCopywriting(){
      if(this.itSNotTheFirstTimeIVeSeenAnAd){
        return {
          title: "预计需等待4分钟",
          desc: "不想等待？看广告您还可以再次加速"
        };
      }
      if(this.isItToWatchTheAdManyTimes){
        return {
          title: "当前加速人数较多",
          desc: "平台已为您匹配极速通道"
        };
      }
      return{
        title: "预计需等待10分钟",
        desc: "看广告加速立减时间1-5分钟"
      };
    },
    progressStyle(){
      return addStyle({
        width: this.progress,
        /* 是否开启动画*/
        ...(this.isAnimation ? {
          transition: "width 0.5s"
        } : {})
      }, "string");
    },
    positionStyle() {
      if (this.position === "bottom") {
        return {
          position: "fixed",
          bottom: 0,
          left: 0,
          paddingTop: pxToRpx(12),
          borderRadius: `${pxToRpx(16)} ${pxToRpx(16)} 0 0`,
          backgroundColor: "#fff",
          boxShadow: ` 0px ${pxToRpx(2)} ${pxToRpx(12)} 0px rgba(0,0,0,0.12)`
        };
      }

      return {};
    }
  },
  watch: {
    show: {
      handler(val) {
        if (val) {
          this.buryThePoint("LAW_APPLET_ADS_FLOAT_POP_UP_PAGE");
        }
      },
      immediate: true
    }
  },
  created() {
    this.getProgress();
  },
  mounted() {
    this.getConfigTime();

    this.rewardedVideoAd.onLoad(() => {
      console.log("激励视频 广告加载成功");
    });

    this.rewardedVideoAd.onClose(res => {
      if (res.isEnded) {
        console.log("激励视频 广告完成");
        this.close();
        this.buryThePoint("LAW_APPLET_ADS_FLOAT_POP_UP_PAGE_HASTEN_SUCCESS_PAGE", isNull(this.videoDate) ? {} : {
          playDate: new Date().getTime() - this.videoDate
        });

        /* uni.showToast({
          title: this.toast,
          icon: "none",
          duration: 3000
        });*/
        this.goToAccelerate();
      } else {
        console.log("激励视频 广告未完成");
        this.buryThePoint("LAW_APPLET_ADS_FLOAT_POP_UP_PAGE_HASTEN_FAIL_PAGE", isNull(this.videoDate) ? {} : {
          playDate: new Date().getTime() - this.videoDate
        });
      }
    });
    /* 有访问数时在进行判断 是否达到了访问次数直接显示*/
    if(this.visitCount > 0 && !isNull(this.pageFlag)){
      this.setTheNumberOfTimesYourAdIsViewed(this.pageFlag);
      const visitCount = this.getTheNumberOfTimesYourAdIsViewed(this.pageFlag);
      if(visitCount >= this.visitCount){
        this.show = true;
      }
    }
  },
  methods: {
    getConfigTime() {
      getCommonConfigKey({
        paramName: "MINI_AD_SHOW_TIME"
      }).then(({ data }) => {
        try {
          const obj = JSON.parse(data.paramValue);

          setTimeout(() => {
            this.show = true;
          }, obj[this.timeCode]);
        } catch (e) {
          console.log(e);
        }
      });
    },
    goToAccelerate(){
      this.setTheNumberOfTimesYourAdIsViewed();
      this.getProgress();
      this.isAnimation = true;
      setTimeout(() => {
        this.isAnimation = false;
        if(this.oldRank - this.rank > 0){
          uni.showToast({
            title: `加速成功，你的咨询排名前进了${this.oldRank - this.rank}名`,
            icon: "none"
          });
        }
      }, 1000);
    },
    /* 获取进度*/
    /*
    * 看完第一广告后加速咨询从0变到80，我的排名从200减到23，有个滚动减少特效，滚动条从后往前滑动
    * 免费广告加速一次后，之后多次点击重新看完广告后，后续每次加速进度+5%，排名-5，滚动条数字-2，进度为100%，后不再累加。排名为0后不再累减，滚动条为0后不再累减
    * */
    getProgress(){
      const num = this.getTheNumberOfTimesYourAdIsViewed();
      this.theNumberOfAdViews = num;
      /* 排名*/
      let ranking = 200;
      /* 加速进度*/
      let speed = 0;
      /* 看完第一广告后加速咨询从0变到80，我的排名从200减到48*/
      if(num > 0){
        ranking = 63;
        speed = 80;
      }
      /* 这里是基于第一次后的数据变化*/
      /* 免费广告加速一次后，之后多次点击重新看完广告后，后续每次加速进度+5%，排名-5，进度为100%，后不再累加。排名为0后不再累减，滚动条为0后不再累减*/
      if(this.isItToWatchTheAdManyTimes){
        /* 总加速数*/
        const totalNum = (num - 1) * 5;
        /* 判断总数是不是达到了最大数 达到了统一为0*/
        ranking = totalNum >= ranking ? 1 : ranking - totalNum;
        speed = speed + totalNum > 100 ? 100 : speed + totalNum;
      }
      this.oldRank = this.rank;
      this.rank = ranking;
      this.speed = speed;

    },
    getTokenKey(){
      /* 获取token前3位做为标识*/
      const token = getUserTokenStorage();
      return token.substring(0, 8);
    },
    /* 设置观看广告次数*/
    setTheNumberOfTimesYourAdIsViewed(key = "theNumberOfTimesYourAdIsViewed"){
      const num = this.getTheNumberOfTimesYourAdIsViewed(key);
      uni.setStorageSync(this.getTokenKey() + key, num + 1);
    },
    /* 获取观看广告次数*/
    getTheNumberOfTimesYourAdIsViewed(key = "theNumberOfTimesYourAdIsViewed"){
      return uni.getStorageSync(this.getTokenKey() + key) || 0;
    },
    /* 埋点*/
    buryThePoint(code, extra = {}){
      if(isNull(this.adsPosition)) return false;
      buryPointChannelBasics({
        code: code,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
        extra: {
          adsPosition: this.adsPosition,
          ...extra
        }
      });
    },
    showAd() {
      this.buryThePoint("LAW_APPLET_ADS_FLOAT_POP_UP_PAGE_HASTEN_CLICK");
      this.videoDate = new Date().getTime();
      this.rewardedVideoAd.show();
      // this.goToAccelerate();
    },
    close() {
      // this.show = false;
    }
  }
};
</script>
