import { userActionInfo } from "@/api/user";
import store from "@/store";

/** 缓存的key */
const discountPriceKey = "discountPrice";

class DiscountPrice {
  constructor() {
    this.init();
  }

  /** 从缓存中读取上面的参数 */
  get _cachedData() {
    return uni.getStorageSync(discountPriceKey);
  }

  /** 如果 cachedData 发生了改变 */
  set _cachedData(data) {
    // 设置vuex中的值
    this._setVuex();

    // 设置缓存
    uni.setStorageSync(discountPriceKey, data);
  }

  /** 初始化 */
  init() {
    this.resetParams();

    if (this._cachedData) {
      this.isCancel = this._cachedData.isCancel;
      this.isFinish = this._cachedData.isFinish;
      this.isPaid = this._cachedData.isPaid;
      this.isHomeShowed = this._cachedData.isHomeShowed;

      this._setVuex();
    }

    // 登陆成功后才调用
    if(store.getters["user/getToken"]){
      userActionInfo().then(({ data }) => {
        if (data.paySuccessNum > 0) {
          this.setPaid();
        }else {
          this.setPaid(false);
        }
      });
    }
  }

  /** 设置vuex中的值 */
  _setVuex() {
    console.log("setVuex discountStore", store.state.discountStore.discount);

    store.commit("discountStore/SET_DISCOUNT", {
      isCancel: this.isCancel,
      isFinish: this.isFinish,
      isPaid: this.isPaid,
      isHomeShowed: this.isHomeShowed,
    });
  }

  /** 设置留资状态 */
  setFinish(status = true) {
    this.isFinish = status;

    this._cachedData = {
      ...this._cachedData,
      isFinish: status,
    };
  }

  /** 设置是否取消支付 */
  setCancel(status = true) {
    this.isCancel = status;

    this._cachedData = {
      ...this._cachedData,
      isCancel: status,
    };
  }

  /** 设置是否已经付费 */
  setPaid(status = true) {
    this.isPaid = status;

    this._cachedData = {
      ...this._cachedData,
      isPaid: status,
    };
  }

  /** 设置首页是否已经显示过 */
  setHomeShowed(status = true) {
    if(!store.getters["discountStore/getIsHomeShowed"]){
      return;
    }

    // 满足降价策略才能设置
    this.isHomeShowed = status;

    this._cachedData = {
      ...this._cachedData,
      isHomeShowed: status,
    };
  }

  /** 参数重置 */
  resetParams() {
    /** 是否取消支付 */
    this.isCancel = false;
    /** 是否走完了问律师留资流程 */
    this.isFinish = false;
    /** 用户是否已经有过付费 */
    this.isPaid = false;
    /** 首页是否已经显示过 */
    this.isHomeShowed = false;
  }

  /** 重置所有状态 */
  reset() {
    this.resetParams();

    uni.removeStorageSync(discountPriceKey);
  }
}

/** 新降价策略，完全废除老的 */
const discountPrice = new DiscountPrice();

export default discountPrice;
