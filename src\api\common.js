import { requestCommon, requestInfo } from "../libs/axios";
import { BEHAVIOR, POINT_CODE } from "@/libs/config.js";

export const buriedPoint = params =>
  requestCommon.post("/burypoint/point", params); // 页面埋点



export const buriedPointChannelDownload = (downloadScene) => {
  /* 下载场景 1：留资页1返回拦截弹窗下载【立即沟通】按钮 2:支付页返回拦截弹窗下载【不需要此服务】按钮 3：支付成功页下载*/
  return  buriedPointChannel({
    code: POINT_CODE.LAW_GUIDE_PAGE_DOWNLOAD_CLICK,
    type: 1,
    behavior: BEHAVIOR.CK,
    extra: {
      downloadScene: downloadScene
    }
  });
};

// 1.6.3落地页渠道埋点
export const pointType = (params) => requestCommon.post("/burypoint/point", params);

/**
 * 问答首页查询当前有配置首页展示问答的标签
 * 税先桃V2.2.9、2022-10-13
 */
export const indexQaMessageTypeValue = (params) => requestInfo.post("/qaMessage/indexQaMessageTypeValue", params);

/**
 * 根据城市名称，模糊 查询省份code和城市code
 */
export const getAreaLikeCityName = (params) => requestCommon.post("/core/area/getAreaLikeCityName", params);

export const qrcodeCreateQrcode = (params) => requestCommon.post("/wechat-mp/wechat/qrcode/createQrcode", params);

export const qrcodeNewCreateQrcode = (params) => requestCommon.post("/wechat-mp/wechat/qrcode/newCreateQrcode", params);

/** C端问答列表分页es搜索 */
export const qaMessage2cPageSearch = (params) => {
  const keyword = params.keyword?.slice(0, 50);

  return  requestCommon.post("/info/qaMessage/2c/pageSearch", { ...params, keyword });
};

/** 律师文章分页es搜索 */
export const articleV2LawyerPageSearch = (params) => {
  // 最多只取50个字符
  const keyword = params.keyword?.slice(0, 50);

  return requestCommon.post("/info/articleV2/lawyer/pageSearch", { ...params, keyword });
};

/** 律师法律意见书分页es搜索(法律指南) */
export const lawGuideServerPageSearch = (params) => {
  // 最多只取50个字符
  const keyword = params.keyword?.slice(0, 50);

  return requestCommon.post("/info/lawGuide/server/pageSearch", { ...params, keyword });
};

/** 合同搜索 */
export const contractTemplatesPageSearch = (params) => requestCommon.post("/info/contractTemplates/pageSearch", params);

/** 畅聊免费次数 */
export const aiEngineAskUnStreamAiChatRemainTimes = () =>
  requestCommon.post("/info/aiEngineAsk/unStream/unStreamAiChatRemainTimes");

/** ai畅聊接口 */
export const aiEngineAskUnStreamAiChat = (data) =>
  requestCommon.post("/info/aiEngineAsk/unStream/unStreamAiChat", data);


/** 豆包ai引擎调用法律咨询意见书（非流式调用） */
export const aiEngineAskDbUnStreamAskLegalOpinion = (data) =>
  requestCommon.post("/info/aiEngineAsk/db/unStream/askLegalOpinion", data);


/** 豆包ai引擎调用法律咨询意见书（非流式调用） */
export const aiEngineAskDbUnStreamAskNew = (data) =>
  requestCommon.post("/info/aiEngineAsk/db/unStream/askNew", data);

