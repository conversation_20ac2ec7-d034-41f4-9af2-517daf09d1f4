<template>
  <div class="position-relative">
    <img
      alt=""
      class="header-img"
      src="@/pages/lawyer-home/essay-detail/img/<EMAIL>"
    >
    <div class="share">
      <app-button-share>
        <div class="flex flex-align-center">
          <img
            alt=""
            class="share-icon"
            src="@/assets/imgs/common/share-icon.png"
          >
        </div>
      </app-button-share>
    </div>
  </div>
</template>

<script>
import AppButtonShare from "@/components/app-components/app-button-share/index.vue";

export default {
  name: "DetailHeader",
  components: { AppButtonShare }
};
</script>

<style lang="scss" scoped>
.header-img {
  display: block;
  width: 100%;
  height: 80px;
}

.share {
  position: absolute;
  right: 24px;
  top: 22px;

  &-icon {
    display: block;
    width: 48px;
    height: 20px;
  }
}
</style>
