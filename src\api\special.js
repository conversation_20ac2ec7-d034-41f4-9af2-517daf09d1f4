import { requestCommon, requestInfo } from "@/libs/axios.js";
import { wrapRequest } from "@/libs/tools.js";
import { commonDayOrNight, getCommonConfigKey } from "@/api/index.js";
import { storeRequest } from "@/libs/store-request";
import store from "@/store";
import { paralegalHistory } from "@/libs/paralegalTools";



/**
 * 问律师结果页接口
 * ! 这个接口连续调用会报错，所以针对该接口做了很多处理
 * @version 1.4.0
 */
export const caseSourceV2GetCaseOrZx = () => {
  if(!store.getters["user/getToken"]) return Promise.reject();

  return storeRequest(
    {
      api: () => requestInfo.post("/caseSourceV2/getCaseOrZx"),
      cacheName: "caseSourceV2GetCaseOrZx",
      storeMaxTime: 1000
    }
  ).then(res => {
    if(Number(res.data.type) === 0) {
      // 清除本地缓存 否则如果之前在问律师留资后，过了留资有效期，又在其它地方留资
      // 则再进入问律师时，会显示上一次的留资历史记录
      console.log("清除本地缓存");
      // 清除历史记录，不用判断，直接进行调用
      paralegalHistory.clearHistory();
    }

    return res;
  }).catch((res) => {
    // 一般情况下，经过缓存处理后，只会在未登录的情况下才会进入这里
    return Promise.reject(res);
  });
};

/** 查询夜间时间 */
export const getNightOrderTime = wrapRequest(
  () => getCommonConfigKey({ paramName: "NIGHT_ORDER_TIME" }),
  {
    isLogin: false,
    spacing: 50000,
  }
);

/** 查询白天时间 */
export const getDayOrderTime = wrapRequest(
  () => commonDayOrNight(),
  {
    isLogin: false,
    spacing: 50000,
  }
);

/** 获取系统参数订单结束后多久不显示售后按钮 */
export const getOrderEndAfterShowTime = wrapRequest(
  () =>
    getCommonConfigKey({ paramName: "ORDER_END_AFTER_SHOW_TIME" }).then(
      ({ data }) => {
        try {
          const obj = JSON.parse(data.paramValue);
          // 如果obj是对象，将值转换为数字
          if (typeof obj === "object") {
            for (const key in obj) {
              obj[key] = Number(obj[key]);
            }
            return obj;
          }
        } catch (e) {
          return data.paramValue;
        }
      }
    ),
  {
    isLogin: false,
    spacing: 50000,
  }
);

/** 案源分流规则接口上次调用时间 */
let caseSourceV2RuleCheckLastTime = 0;

/** 案源分流规则接口能否调用 */
const caseSourceV2RuleCheckCanCall = () => {
  return caseSourceV2RuleCheckLastTime === 0;
};

/** 案源分流规则接口数据 */
let caseSourceV2RuleCheckData = {};

/** 案源分流规则 */
export const caseSourceV2RuleCheck = () => {
  return new Promise((resolve, reject) => {
    // 如果没有到调用时机，则直接返回上次的数据
    if (!caseSourceV2RuleCheckCanCall())
      return resolve(caseSourceV2RuleCheckData);

    // 如果到了调用时机，则调用接口
    requestCommon
      .post("/info/caseSourceV2/rule/check")
      .then((res) => {
        caseSourceV2RuleCheckLastTime = 5;
        setTimeout(() => {
          // 5秒后才能再次调用
          caseSourceV2RuleCheckLastTime = 0;
        }, 5000);
        // 保存数据
        caseSourceV2RuleCheckData = res;
        return resolve(caseSourceV2RuleCheckData);
      })
      .catch((err) => {
        return reject(err);
      });
  });
};
