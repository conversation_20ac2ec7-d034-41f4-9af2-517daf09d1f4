<template>
  <button
    :class="[
      'app-button',
      { 'app-button-default--disabled': disabled },
      plain ? 'app-button-plain' : 'app-button-default',
      typeClass,
      sizeClass,
      { 'app-button--fixed': fixed },
    ]"
    :style="getBtnStyle"
    class="app-button flex flex-align-center flex-space-center"
    @click="onClick"
  >
    <span>
      <slot />
    </span>
  </button>
</template>

<script>
import { addStyle } from "@/uview-ui/libs/function";

export default {
  name: "AppButton",
  props: {
    /** 是否禁用按钮 */
    disabled: {
      type: Boolean,
      default: false,
    },
    /** 禁用状态下是否能点击 */
    disabledClick: {
      type: Boolean,
      default: false,
    },
    /** 是否为朴素按钮 */
    plain: {
      type: Boolean,
      default: false,
    },
    /** 按钮的大小 */
    size: {
      type: String,
      default: "large",
    },
    /** 是否将按钮固定在底部 */
    fixed: {
      type: <PERSON><PERSON>an,
      default: false,
    },
    /**  类型，可选值为 default primary info warning danger */
    type: {
      type: String,
      default: "primary",
    },
    btnStyle: {
      type: [Object, String],
      default: ""
    }
  },
  computed: {
    sizeClass() {
      return this.size ? `app-button--${this.size}` : "";
    },
    typeClass() {
      return this.type ? `app-button-plain--${this.type}` : "";
    },
    getBtnStyle(){
      return addStyle(this.btnStyle, "string");
    }
  },
  methods: {
    onClick(e) {
      if(!this.disabledClick && this.disabled) return false;
      this.$emit("click", e);
    },
  },
};
</script>


