<template>
  <div>
    <div
      class="flex w-[351px] bg-[#FFFFFF] rounded-[8px] box-border mx-auto py-[16px]"
    >
      <div
        class="flex items-center flex-1 justify-between px-[16px] box-border border-0 border-solid border-r-[1px] border-[#EEEEEE]"
        @click="toAskLawyer"
      >
        <div>
          <div class="text-[18px] font-bold text-[#333333]">
            免费咨询
          </div>
          <div class="text-[12px] text-[#999999] mt-[4px]">
            0元问律师
          </div>
        </div>
        <img
          alt=""
          class="w-[48px] h-[48px] block"
          src="../../rapid-consultation-confirm-order/mall/img/m0L5kbMU.png"
        >
      </div>
      <div
        class="flex items-center flex-1 justify-between px-[16px] box-border"
        @click="toConfirmOrderPlus(sceneType.sydb)"
      >
        <div>
          <div class="text-[18px] font-bold text-[#333333]">
            电话咨询
          </div>
          <div class="text-[12px] text-[#999999] mt-[4px]">
            1对1私密咨询
          </div>
        </div>
        <img
          alt=""
          class="w-[48px] h-[48px] block"
          src="../../rapid-consultation-confirm-order/mall/img/Y4L7heQf.png"
        >
      </div>
    </div>

    <div
      class="w-[351px] h-[72px] bg-[#FFFFFF] rounded-[8px] border-box flex items-center mt-[12px] mx-auto"
    >
      <div class="pr-[8px] border-0 border-solid border-r-[1px] border-[#EEEEEE] shrink-0">
        <img
          alt=""
          class="w-[32px] h-[32px] block ml-[12px] shrink-0"
          src="../../rapid-consultation-confirm-order/mall/img/yE6CMhLJ.png"
        >
      </div>
      <div class="flex items-center">
        <div
          v-for="(item, index) in tabList"
          :key="index"
          class="w-[100px] h-[42px] box-border flex items-center justify-center border-0 border-solid [&:not(:last-child)]:border-r-[1px] border-[#EEEEEE]"
        >
          <div>
            <div
              class="text-[15px] text-[#333333] flex justify-center items-end"
            >
              <div>
                {{ askLawyerData[item.prop] }}
              </div>
              <span class="text-[11px] text-[#999999] ml-[4px]">人</span>
            </div>
            <div class="text-[12px] text-[#666666] flex items-center mt-[4px]">
              <div
                :class="[
                  index === tabList.length - 1 ? 'bg-[#F78C3E]' : 'bg-[#22BF7E]'
                ]"
                class="w-[4px] h-[4px] rounded-full mr-[4px]"
              />
              <div>{{ item.title }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { todayConsultTrends } from "@/api/lawyer";
import { toAskLawyer } from "@/libs/turnPages";
import { toConfirmOrderPlus } from "@/libs/tools";
import { sceneType } from "@/libs/config";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";

export default {
  name: "EntranceAskLawyer",
  props: {
    /** 点击问律师埋点 */
    clickAskLawyerBuryPoint: {
      type: String,
      default: ""
    },
    /** 点击电话咨询埋点 */
    clickPhoneConsultBuryPoint: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      /** 咨询动态数据 */
      askLawyerData: {},
      /** tab数据 */
      tabList: [
        {
          title: "在线律师",
          prop: "onlineLawyerCount"
        },
        {
          title: "在线咨询",
          prop: "onlineConsultCount"
        },
        {
          title: "付费咨询",
          prop: "payConsultCount"
        }
      ]
    };
  },
  computed: {
    sceneType() {
      return sceneType;
    }
  },
  mounted() {
    this.getAskLawyerData();
  },
  methods: {
    toConfirmOrderPlus(scene) {
      buryPointChannelBasics({
        code: this.clickPhoneConsultBuryPoint,
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
        type: 1
      });

      toConfirmOrderPlus(scene);
    },
    toAskLawyer() {
      buryPointChannelBasics({
        code: this.clickAskLawyerBuryPoint,
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
        type: 1
      });

      toAskLawyer();
    },
    /** 获取咨询动态数据 */
    getAskLawyerData() {
      todayConsultTrends().then(res => {
        this.askLawyerData = res.data;
      });
    }
  }
};
</script>

<style lang="scss" scoped></style>
