import API_VERSION from "./apiVersion.json";
import { getCurrentAppConfig } from "./appConfig";

export const axiosBaseHeadersConfig = {
  // #ifndef  MP-HJLS
  ...API_VERSION.origin,
  ...getCurrentAppConfig(),
  // #endif
  // #ifdef  MP-HJLS
  ...API_VERSION.hjls,
  // #endif
  // #ifdef  MP-TOUTIAO
  osVersion: "11",
  // #endif
  // #ifdef MP-BAIDU
  osVersion: "12",
  // #endif
  // #ifdef  MP-ALIPAY
  osVersion: "18",
  // #endif
  platform: "applets"
};

/* 协议连接*/
export const protocols = {
  /* 隐私政策*/
  law_privacy_protocol:
    process.env.VUE_APP_ENV_PROTOCOL + "law_privacy_protocol.html",
  /* 用户协议*/
  law_user_protocol:
    process.env.VUE_APP_ENV_PROTOCOL + "law_user_protocol.html",
  /* 购买协议*/
  law_buy_protocol:
    process.env.VUE_APP_ENV_PROTOCOL + "law_buy_protocol.html?header=0"
};
/* 导航对应的code*/
export const navKeysCode = {
  /* 法律资讯*/
  LEGAL_CASE: "10001",
  /* 法律问答*/
  LEGAL_QUESTIONS_AND_ANSWERS: "10002",
  /* 法律课堂*/
  LAW_CLASS: "10003",
  /* 首页*/
  LEGAL_KNOWLEDGE: "10004",
  /* 法律视频 */
  LEGAL_VIDEO: "20038",
  /* 轻法维权 */
  LEGAL_SAFEGUARD: "10005",
  /* 刑法罪名库 */
  CHARGE_LIBRARY: "20051",
  /* 法律法规 */
  LAW_RULE: "20050",
  /* 案例库 */
  LAWYER_CASE: "20053"
};
/* 极速咨询订单类型*/
export const speedOrderType = {
  /* 待支付 */
  UNPAID: 1000,
  /* 已支付 */
  PAID: 1001,
  /* 交易关闭 */
  CLOSED: 1002,
  /* 已退款 */
  REFUNDED: 2002
};
/* 埋点行为*/
export const BEHAVIOR = {
  VI: "VI", // 曝光
  IN: "IN", // 激活
  CK: "CK" // 点击
};

/** 指标名称 */
export const INDICATOR_NAME = {
  /** 代理案件数 */
  caseTotal: "caseTotal",
  /** 常去法院统计 */
  courtName: "courtName",
  /** 案由分布统计 */
  caseReason: "caseReason",
  /** 最近案件数量统计 */
  recentCase: "recentCase",
  /** 标的额统计 */
  caseAmt: "caseAmt"
};

/** 问答案源场景值 */
function qaSource(isNew = false) {
  if (isNew) {
    return {
      /** 咨询-我的诉求服务 */
      gjzx: "x_web_gjzx",
      /** 咨询-我的诉求服务2 */
      gjzx2: "x_web_gjzx2",
      /** 咨询-我的提问服务 */
      ptzx: "x_web_ptzx",
      /** 咨询-我的提问服务2 */
      ptzx2: "x_web_ptzx2",
      /** 问律师问答结果服务 */
      wdjg: "x_web_wdjg",
      /** 问律师问答结果服务2 */
      wdjg2: "x_web_wdjg2",
      /** 问律师案源结果服务 */
      ayjg: "x_web_ayjg",
      /** 问律师案源结果服务2 */
      ayjg2: "x_web_ayjg2"
    };
  }

  return {
    /** 咨询-我的诉求服务 */
    gjzx: "web_gjzx",
    /** 咨询-我的诉求服务2 */
    gjzx2: "web_gjzx2",
    /** 咨询-我的提问服务 */
    ptzx: "web_ptzx",
    /** 咨询-我的提问服务2 */
    ptzx2: "web_ptzx2",
    /** 问律师问答结果服务 */
    wdjg: "web_wdjg",
    /** 问律师问答结果服务2 */
    wdjg2: "web_wdjg2",
    /** 问律师案源结果服务 */
    ayjg: "web_ayjg",
    /** 问律师案源结果服务2 */
    ayjg2: "web_ayjg2"
  };
}

/* 公共咨询引导入口*/
export const sceneType = {
  // 快速咨询
  kszx: "kszx",
  // 问答入口
  wdrk: "wdrk",
  // 我的问答
  wdwd: "wdwd",
  // 我的委托.
  wdwt: "wdwt",
  // 我的订单
  wddd: "wddd",
  // 找律师列表
  zlslb: "zlslb",
  // 我的律师
  wdls: "wdls",
  /* 首页顶部 首页电话咨询 */
  sydb: "sydb",
  /* 首页底部tab*/
  syzxan: "syzxan",
  /* app新人弹窗=*/
  xrtc: "xrtc",
  /* 法临首页快速咨询律师顶部卡片*/
  sykszxls: "sykszxls",
  /* 法临首页底部浮窗*/
  syxuzx: "syzxfc",
  /* 问答详情*/
  wdxqljzx: "wdxqljzx",
  /* 法临律师解答空白页*/
  lsjdkby: "lsjdkby",
  /* 发布问答的理解沟通*/
  fbwdjxls: "fbwdjxls",
  /* 我的问答*/
  wdtwysx: "wdtwysx",
  /* 首页图文咨询*/
  twzx: "twzx",
  /* 3.0.9小程序引导策略 29.9*/
  jjcltw: "jjcltw",
  /* 3.0.9小程序引导策略 49.9*/
  jjcldh: "jjcldh",
  /* 3.0.9小程序引导策略 9.9*/
  jjclwd: "jjclwd",
  /** 3.1.4问答悬赏 */
  wdxs: "wdxs",
  /** 3.1.6 降价策略 */
  jjcl5: "web_jjcl5",
  /** 问律师-律师卡片 */
  wls_lskp: "wls_lskp",
  /** 问律师-顶部弹窗 */
  wls_dbtc: "wls_dbtc",
  /** 电话咨询页降价策略商品 */
  dhzq_jj: "dhzq_jj",
  /** 问律师结果页降价商品 */
  wls_jj: "wls_jj",
  /** 咨询页问答和案源降价商品 */
  zz_jj: "zz_jj",
  /** 登录降价 */
  login_jj: "login_jj",
  /** 首页降价卡片 */
  web_jjcl5: "web_jjcl5",
  /** im再次咨询场景 */
  im_zczx: "im_zczx",
  /** 定向咨询 */
  dxzx: "web_dxzx",
  /* 高级合同服务*/
  web_gjhtfw: "web_gjhtfw",
  sjfw1: "sjfw1",
  sjfw2: "sjfw2",
  wlsjjfw: "web_wlsjjfw",
  // https://lanhuapp.com/web/#/item/project/product?pid=72abc1e2-40ca-4d75-9e42-83027d64c6de&image_id=1119c45d-9d7b-4947-b866-e069f956360f&tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=18f54e8f-eb5c-43e8-917e-a10b889c0046&docId=1119c45d-9d7b-4947-b866-e069f956360f&docType=axure&pageId=e7c9a4d383fb4b85befa065d4b7e0d35&parentId=7ce3bf52-923c-4d4f-bc31-b99c1a1337df
  // AI追问服务
  ai_zws: "ai_zws",
  /** 打官司定金 */
  dgsdj: "dgsdj",
  /** 打官司尾款 */
  dgswk: "dgswk",
  ...qaSource()
};

export const paralegalCard = {
  /** 写死的卡片 */
  CARD_ADV: "card_adv",
  /** 咨询选择 */
  CONSULTING_OPTIONS: "consulting_options",
  /** 快速咨询，律师一对一服务卡片 */
  QUICK_SERVER_ONE_TO_ONE: "quick_server_one_to_one",
  /** 保密文字 */
  CONFIDENTIAL_TEXT: "confidential_text",
  /** 等待文字卡片 */
  WAITING_FOR_CARD: "waiting_for_card",
  /** 引导文字 */
  INTRODUCTORY_TEXT: "introductory_text",
  /** 重新咨询 */
  RE_CONSULT: "re_consult",
  /** 为您推荐 */
  RECOMMENDED_FOR_YOU: "recommended_for_you",
  /** 地址选择 */
  CardAddressSelection: "card_address_selection"
};

/* 服务code*/
export const SERVERCODE = {
  /* 渠道落地页*/
  BASIC_CODE: "qdldy",
  /* 短信落地页*/
  SHORT_MESSAGE_CODE: "dxldy",
  /* 电话召回*/
  dxldyzf: "dxldyzf",
  qdldy: "qdldy",
  dxldy: "dxldy",
  /* wap渠道落地发回拦截*/
  qdldyfhlj: "qdldyfhlj",
  /* 电话召回返回攔截*/
  dxldyfhlj: "dxldyfhlj"
};

export const WS_MESSAGE_TYPE = {
  TEXT: 1,
  IMG: 2,
  VIDEO: 3,
  CUSTOM: 4,
  FILE: 5,
  SYSTEM: 6
};

export const newServiceCode = {
  /** 律师1v1 */
  LAWYERCONSUL1TO1: 300001,
  /** 律师咨询 */
  LAWYERCONSULTATION: 100003,
  /** 案件委托 */
  ENTRUSTEDSERVICE: 100002,
  /** 极速咨询 */
  SPEEDCONSULTATION: 100001
};

export const TRANSFORMATION_PATH = {
  // 'LANDPAGE': 1, //	落地页
  [newServiceCode["LAWYERCONSUL1TO1"]]: 2, //	找律师1v1
  [newServiceCode["LAWYERCONSULTATION"]]: 2, //	找律师
  [newServiceCode["SPEEDCONSULTATION"]]: 3, //	律师咨询
  [newServiceCode["ENTRUSTEDSERVICE"]]: 4 //	案件委托
};

// 消息类型 1:文本,2:图片，3：视频，4：自定义，5：文件，6：系统消息
export const askMessageType = {
  TEXT: 1,
  PICTURE: 2,
  VIDEO: 3,
  CUSTOMIZE: 4,
  FILE: 5,
  SYSTEM_INFORMATION: 6
};

// 角色 1：用户 2：律师
export const askMessageRole = {
  USER: 1,
  LAWYER: 2
};
/** 1.7.0新的我的服务已知code */
export const newServiceCodeArr = [100003, 100002, 100001, 300001];
/** 1.7.0新的订单类型 */
export const newOrderType = {
  /** 律师咨询（付费卡片购买的）*/
  LAWYERCONSULTATION: 1001,
  /** 案件委托*/
  ENTRUSTEDSERVICE: 1002,
  /** 快速咨询（9.9元的拦截优惠价格）*/
  SPEEDCONSULTATION: 1001
};
/* 极速咨询订单中退款状态类型*/
export const speedRefundType = {
  /* 未退款 */
  UNREFUNDED: 2000,
  /* 退款中 */
  REFUNDING: 2001,
  /* 退款成功 */
  REFUNDSUCESS: 2002,
  /* 退款失败 */
  REFUNDFAILED: 2003,
  /** 已撤销 */
  REVOKED: 2004
};

/* 全局转化路径*/
export const globalTransformationPath = {
  /* 默认*/
  DEFAULT: 0,
  /* 找律师*/
  FINDALAWYER: 2,
  /* 律师咨询*/
  LAWYERCONSULTATION: 3,
  /* 咨询案例*/
  CONSULTINGCASE: 5,
  // * 2.1.6 新增的转化路径
  /** 打官司 */
  SQUABBLE: 10,
  /** 首页-问律师（原一对一快速咨询） */
  QUICKCONSULTATION: 11,
  /** 首页问答留言咨询点击按钮 */
  HOME_Q_A_MESSAGE: 12,
  /** 问答列表首页中用户问详情按钮点击 */
  Q_A_LIST_HOME: 25,
  /** 首页付费特惠入口点击按钮 */
  HOME_PAID_SPECIALS: 15,
  /** 首页付费公共咨询弹窗页面 */
  HOME_PAID_PUBLIC: 14,
  /** 首页付费公共快速咨询点击按钮 */
  HOME_PAID_PUBLIC_EXPRESS: 16,
  /** 首页顶部固定付费公共快速咨询点击按钮 */
  HOME_TOP_FIXED: 17,
  /** 首页浮窗付费公共快速咨询点击按钮 */
  HOMEPAGE_FLOATING_WINDOW_PAYMENT: 18,
  /** 找律师中心位置固定付费公共快速咨询点击按钮 */
  FIND_A_LAWYER: 19,
  /** 我的订单底部固定付费公共快速咨询点击按钮 */
  BOTTOM_OF_MY_ORDER: 20,
  /** 我的委托页底部固定付费公共快速咨询点击按钮 */
  BOTTOM_OF_MY_DELEGATION_PAGE: 21,
  /** 我的问答页底部固定付费公共快速咨询点击按钮 */
  BOTTOM_OF_MY_ANSWER_PAGE: 28,
  /** 我的律师页底部固定付费公共快速咨询点击按钮 */
  BOTTOM_OF_MY_LAWYER_PAGE: 22,
  /** 发布问题页底部固定付费公共快速咨询点击按钮 */
  BOTTOM_OF_POST_QUESTION_PAGE: 23,
  /** 发布问答页提交问答按钮 */
  LAW_PAGE_ANSWER_CONSULT_INDEX_QUESTION_SUBMIT: 24,
  /** 首页咨询导航栏点击按钮 */
  HOME_CONSULTING_NAVIGATION: 29,
  /** 打官司基础数据提交按钮 */
  LAWSUIT_BASIC_DATA: 26,

  /* 1.2.8*/
  /*	找律师页打官司点击按钮*/
  LAW_PAGE_FIND_LAWYER_PROSECUTE_CLICK: 30,
  /*	问律师页面（原一对一快速咨询）*/
  LAW_PAGE_QUICKLY_CONSULT: 31,

  // seo 1.0.0
  LAW_PAGE_ANSWER_CLICK: 33,
  LAW_PAGE_ANSWER_CONSULT_CENTER_FIXED_QUICKLY_CONSULT_CLICK: 34,
  LAW_PAGE_ANSWER_DETAILS_QUICKLY_CONSULT_CLICK: 35,
  LAW_PAGE_ANSWER_INDEX_TO_CONSULT_BUTTON_CLICK: 45,
  ANSWER_DETAIL_BOTTOM_SUCTION_CLICK: 46,
  LAW_PAGE_ANSWER_CONSULT_PROSECUTE_BUTTON_CLICK: 47
};

/** 界面的一些路由 */
export const pageRouter = {
  /** 我要打官司界面 */
  MY_LITIGATE: "/my-litigate",
  /** 我要打官司案件详情 */
  CASE_DETAILS: "/pages/case-details/index",
  /** 完善案件资料界面 */
  CASE_DETAILS_PREFECT: "/case-details/prefect"
};

/* 消息类型*/
export const messageCenterEnum = {
  /* 互动消息*/
  INTERACTION: "INTERACTION",
  /* 系统消息*/
  SYSTEM: "SYSTEM",
  /* 服务消息*/
  SERVER: "SERVER"
};

/**
 * 服务助手类型
 */
export const serviceHelperTypeEnum = {
  /** C端围观成功 */
  ONLOOKERS: 10000,
  /** C端赞赏成功 */
  PRAISE: 11000,
  /** 咨询助手 */
  ADVISORY: 6000,
  /** 打官司助手 */
  LITIGATE: 9000,
  /** 公共咨询支付成功 */
  PAY: 13000
};
// 商品类型
export const PRODUCT_TYPES = {
  HISTORICAL_SERVICE: 0, // -其他服务(历史服务
  ONE_V_ONE_SERVICE: 1, // 1V1服务
  CASE_SOURCE_PAID_SERVICE: 2, // 2-案源付费服务,
  ONE_V_ONE_PUBLIC_CONSULTATION_SERVICE: 3, // 3-1v1公共咨询服务,
  APPRECIATE_THE_PRODUCT: 4, // 4-赞赏商品,
  ONLOOKERS: 5, // 5-围观商品
  ASK_APPRECIATE_THE_PRODUCT: 6, // 6-追问的赞赏商品
  /** 服务悬赏 */
  SERVICE_REWARD: 8
};

/**
 * 分享字典参数
 */
export const SHARE_DICTIONARY = {
  /** 律师详情页分享 */
  LAWYER_HOME: "LAWYER_HOME",
  /** 一问多答分享 */
  ASK_DETAILS: "ASK_DETAILS",
  /** 法律文章分享 */
  ESSAY_DETAIL: "ESSAY_DETAIL",
  /** 法律指南分享 */
  ASKDETAIL: "ASKDETAIL",
  /** 个人中心分享 */
  MINE: "MINE"
};

/** 搜索页面的类型 */
export const SEARCH_PAGE_TYPE = {
  /** 普通 */
  COMMON: "1",
  /** 合同 */
  CONTRACT: "2",
  /** 专场 */
  SPECIAL: "3",
};

/** 是否触发onShow */
export let IS_ON_SHOW = true;

export const setIsOnShow = isOnShow => {
  IS_ON_SHOW = isOnShow;
};

export const getIsOnShow = () => {
  return IS_ON_SHOW;
};

/** 当前平台名 */
export const getPlatformName = () => {
  // #ifdef  MP-HJLS
  return "好佳律师";
  // #endif

  return "法临";
};

/** 是否是开发环境 */
export const IS_DEV = process.env.VUE_APP_ENV_TEST === "true";

/** 是否是本地开发环境 */
export const IS_LOCAL_DEV = process.env.NODE_ENV === "development";
