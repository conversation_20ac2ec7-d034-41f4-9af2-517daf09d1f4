import {
  bindRelation,
  getLoginByDeviceId,
  getUserByDeviceId
} from "@/api/user";
import store from "@/store";
import { whetherToLogIn } from "@/libs/tools";
import { getCommonConfigKey } from "@/api";
import { getUserTokenStorage } from "@/libs/token";


/** 判断是否登录 */
export function verifyLogin() {
  return store.getters["user/getToken"] || getUserTokenStorage();
}

/** 绑定律师分享信息 */
export function bindLawyerShareInfo() {
  const lawyerShareCode = uni.getStorageSync("lawyerShareCode");

  // 如果又code并且进行了登录
  if(lawyerShareCode && verifyLogin()) {
    bindRelation({
      lawyerShareCode
    }).then(() => {
      console.log("绑定律师", lawyerShareCode);
    });
  }
}

async function loginSetData(token) {
  await store.dispatch("user/setToken", token);
  await Promise.all([
    store.dispatch("user/setUserInfo"),
    store.dispatch("im/userLogin")
  ]);
  store.commit("user/EXECUTE_LOGIN_CALLBACK");
  store.commit("popup-state/SET_DEVICE_LOGIN_POPUP_STATE", false);
  store.commit("user/SET_LOGIN_POPUP_SHOW", true);
}

/**
 * 通过设备id进行查询登录信息
 * 如果有返回，才能进行登录
 */
function getUser() {
  // #ifdef MP-WEIXIN || MP-ALIPAY
  const openid = store.getters["user/getOpenid"];
  if (!openid) {
    return Promise.reject("设备id不存在");
  }
  // #endif

  return getUserByDeviceId();
}

/**
 * 验证设备id是否存在
 * 存在则会返回信息
 */
export function checkDeviceIdValid() {
  return getUser().then(res => {
    const { data } = res;

    if (data.phone) {
      store.commit("user/SET_DEVICE_INFO", data);
      return Promise.resolve(res);
    } else {
      return Promise.reject(res);
    }
  });
}

function beforeLogin() {
  return checkDeviceIdValid().then(res => {
    const params = {
      id: res.data.id
    };
    const loginQuery = uni.getStorageSync("loginQuery");
    // 登陆时需要使用的参数
    if (loginQuery) Object.assign(params, loginQuery);
    const lawyerShareCode = uni.getStorageSync("lawyerShareCode");
    if (lawyerShareCode) params.lawyerShareCode = lawyerShareCode;

    return getLoginByDeviceId(params);
  });
}

/**
 * 通过设备id进行登录
 * 直接登录，不会有弹窗出现
 */
export function deviceLogin({ showToast = true } = {}) {
  if (showToast)
    uni.showLoading({
      title: "授权中"
    });

  return  beforeLogin()
    .then(async res => {
      await loginSetData(res.data.token);
      uni.hideLoading();
      if (showToast)
        uni.showToast({
          title: "授权成功",
          icon: "none"
        });
    })
    .catch(() => {
      // 未获取到设备id
      // 使用正常登录流程
      whetherToLogIn(store.getters["user/getLoginCallback"]);
      if (showToast) uni.hideLoading();
    });
}

/**
 * 通过设备id进行登录 打开登录弹窗
 * @param nextCallback
 */
export async function whetherDeviceToLogIn(nextCallback) {
  if (!store.getters["user/getToken"]) {
    try {
      const res = await getCommonConfigKey({
        paramName: "DEVICE_LOGIN"
      });

      if (Number(res.data.paramValue) === 1) {
        await checkDeviceIdValid();
        store.commit("popup-state/SET_DEVICE_LOGIN_POPUP_STATE", true);
        store.commit("user/SET_LOGIN_CALL_BACK", nextCallback);
      } else {
        whetherToLogIn(nextCallback);
      }
    } catch (e) {
      whetherToLogIn(nextCallback);
    }
  } else {
    nextCallback && nextCallback();
  }
}
