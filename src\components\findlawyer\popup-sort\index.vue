<template>
  <div>
    <app-popup
      :animation="false"
      :popupStyle="{ top: `${tabsTop}px` }"
      :safeAreaInsetBottom="false"
      :show="show"
      bgColor="transparent"
      mode="top"
      @close="close"
    >
      <div class="warp">
        <div
          v-for="i in sortList"
          :key="i.value"
          class="item flex flex-align-center"
          @click="handleSwitch(i)"
        >
          <p class="label flex-1">
            {{ i.text }}
          </p>
          <if f="hjls">
            <img
              v-if="active === i.value"
              alt=""
              class="active"
              src="../../../pages/index/imgs/city-check.png"
            >
          </if>
          <if t="hjls">
            <img
              v-if="active === i.value"
              alt=""
              class="active"
              src="../../../pages/index/imgs/check-green.png"
            >
          </if>
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";

export default {
  name: "PopupSort",
  components: { AppPopup },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    tabsTop: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      active: 1,
      /* 排名*/
      sortList: [
        {
          text: "综合排名",
          value: 1
        },
        {
          text: "执业年限排名",
          value: 3
        },
        {
          text: "响应速度排名",
          value: 5
        },
        {
          text: "服务好评星级排名",
          value: 4
        },
        {
          text: "服务好评数排名",
          value: 7
        },
        {
          text: "夜间常在律师排名",
          value: 8
        }
      ]
    };
  },
  methods: {
    close() {
      this.$emit("update:show", false);
    },
    handleSwitch(data) {
      this.active = data.value;
      this.close();
      this.$emit("handleSwitch", data);
    }
  }
};
</script>

<style lang="scss" scoped>
.warp {
  background: #f5f5f7;
  border-radius: 0px 0px 16px 16px;

  .item {
    padding: 0 16px;
    height: 44px;
    .label {
      font-size: 14px;
      font-weight: 400;
      color: #333333;
    }
    .active {
      width: 20px;
      height: 20px;
    }
  }
}
</style>
