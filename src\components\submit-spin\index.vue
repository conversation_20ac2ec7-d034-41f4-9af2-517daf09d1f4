<template>
  <div>
    <div v-if="spin">
      <submit-spin-item />
    </div>
    <slot v-else />
  </div>
</template>

<script>
import { setAsyncRequest } from "@/libs/paralegalTools";
import { scrollToBottom } from "@/pages/submit-question/js";
import SubmitSpinItem from "@/components/submit-spin/SubmitSpinItem.vue";

export default {
  name: "SubmitSpin",
  components: { SubmitSpinItem },
  props: {
    /** 是否为加载中状态 */
    spinning: {
      type: Boolean,
      default: false,
    },
    asyncName: {
      type: String,
      default: "",
    },
    value: {
      type: <PERSON>olean,
      default: false,
    },
  },
  data() {
    return {
      spin: true,
    };
  },
  created() {
    this.spin = this.spinning;

    if (this.asyncName) {
      setAsyncRequest({
        asyncName: this.asyncName,
        callback: (callback) => {
          this.$nextTick(() => {
            this.closeSpin();
          });

          const result = callback?.(this.asyncName);
          console.log("async-result", result);
          this.$emit("async-result", result);
        },
      });
    } else {
      setTimeout(() => {
        this.closeSpin();
      }, 1000);
    }
  },
  methods: {
    /** 关闭加载中动画 */
    closeSpin() {
      this.spin = false;
      scrollToBottom();
    },
  },
};
</script>