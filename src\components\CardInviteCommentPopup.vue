<template>
  <div>
    <app-popup
      :show="show"
      :closeOnClickOverlay="false"
      :zIndex="9100"
      showCancelButton
      mode="bottom"
      bgColor="transparent"
      @cancel="handleClose"
      @close="handleClose"
    >
      <div
        class="pt-[36px] pb-[18px] relative rounded-tl-[16px] rounded-tr-[16px] overflow-hidden bg-[#FFFFFF] [box-shadow:0px_-2px_10px_0px_rgba(0,0,0,0.08)]"
      >
        <img
          alt=""
          class="w-[68px] h-[24px] block absolute left-0 top-0"
          src="@/pages/lawyer-home/img/Frame2754(1).png"
        >
        <slot name="title">
          <div class="flex items-center justify-center">
            <div
              class="ml-[5px] font-bold text-[16px] text-[#333333] flex items-center"
            >
              获取多种解决方案
            </div>
          </div>
        </slot>
        <slot name="subtitle">
          <div
            class="text-[14px] text-[#333333] mt-[9px] w-[343px] mx-auto text-center"
          >
            问更多律师平台限时赠送二次咨询折扣
          </div>
        </slot>
        <div class="flex items-center mt-[33px] ml-[16px]">
          <img
            alt=""
            class="w-[13px] h-[16px]"
            src="@/pages/lawyer-im/components/lawyer-im/im-chatroom-type/card-invite-comment/img/Frame1321315579.png"
          >
          <div class="ml-[4px] text-[14px] text-[#9F6310]">
            严选律师·咨询无忧
          </div>
        </div>
        <div class="mt-[12px] flex items-center overflow-x-auto min-h-[143px]">
          <div
            v-for="item in lawyerList"
            :key="item.id"
            class="w-[138px] ml-[16px] p-[12px] rounded-[8px] border-[0.5px] border-solid border-[#E3E3E3] box-border"
            @click="toLawyerHome(item.id)"
          >
            <div class="flex items-center">
              <img
                :src="item.imgUrl"
                alt=""
                class="w-[32px] h-[32px] block rounded-full"
              >
              <div class="ml-[8px]">
                <div class="font-bold text-[14px] text-[#333333]">
                  {{ item.realName }}
                </div>
                <div class="text-[10px] text-[#666666]">
                  执业{{ item.workTime }}年
                </div>
              </div>
            </div>
            <div class="mt-[8px] flex items-center">
              <div class="flex items-center">
                <img
                  alt=""
                  class="w-[12px] h-[12px] block"
                  src="@/pages/lawyer-im/components/lawyer-im/im-chatroom-type/card-invite-comment/img/icon.png"
                >
                <div class="font-bold text-[11px] text-[#F78C3E] ml-[2px]">
                  {{ (item.score || 0).toFixed(1) }}
                </div>
              </div>
              <div class="text-[11px] text-[#666666] ml-[12px] flex-shrink-0">
                已服务<span class="font-bold text-[#333333]">{{
                  item.serverNum
                }}</span>人
              </div>
            </div>
            <div class="mt-[4px] text-[10px] text-[#666666]">
              擅长：{{ (item.workField || []).join("、") }}
            </div>
            <div
              class="mt-[8px] w-[106px] h-[23px] rounded-[68px] border-[0.5px] border-solid border-[#3887F5] text-[11px] text-[#3887F5] flex items-center justify-center mx-auto"
              @click.stop="handleConsult(item)"
            >
              立即咨询
            </div>
          </div>
        </div>
        <div
          v-if="isDirect"
          class="flex items-center justify-center mt-[39px]"
        >
          <div
            class="w-[144px] h-[44px] rounded-[12px] border-[1px] border-solid border-[#3887F5] font-bold text-[16px] text-[#3887F5] flex items-center justify-center box-border"
            @click="freeAskOtherLawyer"
          >
            免费问其他律师
          </div>
          <div
            class="w-[186px] h-[44px] bg-[#3887F5] rounded-[12px] opacity-[0.99] font-bold text-[16px] text-[#FFFFFF] flex items-center justify-center box-border ml-[13px] relative z-10"
            @click="handleConsultAgain"
          >
            <div
              class="absolute left-[25%] text-[13px] text-[#FFFFFF] -top-[15px] h-[20px] bg-[linear-gradient(_109deg,_#FF913E_0%,_#F54A3A_100%)] rounded-tl-[10px] rounded-br-[10px] rounded-tr-[10px] rounded-bl-[2px] px-[8px] py-[1px] box-border flex items-center"
            >
              <div class="shrink-0">
                限时优惠
              </div>
              <div class="font-bold">
                {{ countDownFormat }}
              </div>
            </div>
            <div>¥{{ discountService.servicePrice | amountFilter }}</div>
            <div class="ml-[6px]">
              再次咨询
            </div>
          </div>
        </div>
        <div
          v-else
          class="mt-[31px] flex items-center justify-center flex-col"
        >
          <div
            class="w-[295px] pl-[12px] h-[32px] flex items-center bg-[linear-gradient(_180deg,_#FFEDE5_0%,_rgba(255,237,229,0)_100%)] rounded-tl-[8px] rounded-br-none rounded-tr-[8px] rounded-bl-none"
          >
            <img
              alt=""
              class="w-[38px] h-[38px]"
              src="@/pages/lawyer-im/components/lawyer-im/im-chatroom-type/card-invite-comment/img/Frame2460.png"
            >
            <div class="text-[13px] ml-[15px] text-[#EB4738]">
              多次咨询限时特惠
            </div>
            <div class="font-bold text-[13px] ml-[8px] text-[#EB4738]">
              {{ countDownFormat }}
            </div>
          </div>
          <div
            class="w-[343px] h-[44px] bg-[linear-gradient(_90deg,_#FA700D_0%,_#F34747_100%)] rounded-[22px] border-[0px] border-solid [border-image:linear-gradient(90deg,_rgba(250,_112,_13,_1),_rgba(243,_71,_71,_1))_0_0] flex items-center justify-center"
            @click="handleConsultAgain"
          >
            <div class="font-bold text-[16px] text-[#FFFFFF]">
              ¥{{ discountService.servicePrice | amountFilter }}
            </div>
            <div class="font-bold text-[16px] text-[#FFFFFF] ml-[10px]">
              再次咨询
            </div>
          </div>
        </div>
        <u-safe-bottom />
      </div>
    </app-popup>
    <telephone-consultation-popup v-if="show" />
    <private-telephone-consultation
      v-model="privateChatConsultingServicesState"
      :data="lawyerInfo"
      :productInfo="privateChatConsultingServiceInfo"
      @handleClickPay="handleClickPrivatePay"
    />
    <retain-capital-popup
      v-if="show"
      v-model="retainCapitalPopup"
      :isCaseSource="true"
      :showSafeBottom="showSafeBottom"
    />
  </div>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { caseSourceV2Save, serviceManegeInfoCommon } from "@/api";
import { sceneType } from "@/libs/config";
import { lawyerListV3 } from "@/api/findlawyer";
import {
  defaultPublicConsultationPay,
  toLawyerFakeImBefore
} from "@/libs/tools";
import Store from "@/store";
import countDownMixin from "@/pages/rapid-consultation-confirm-order/my-consultation/js/countDownMixin";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import { buryPointChannelBasics } from "@/api/burypoint";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath";
import { toLawyerHome } from "@/libs/turnPages";
import { getLocation } from "@/libs/getLocation";
import PrivateTelephoneConsultation from "@/components/private-telephone-consultation/index.vue";
import { caseSourceV2SaveGeneral } from "@/api/lawyer";
import TelephoneConsultationPopup from "@/components/telephone-consultation-popup/index.vue";
import RetainCapitalPopup from "@/components/RetainCapitalPopup.vue";
import { caseSourceV2GetCaseOrZx } from "@/api/special";

export default {
  name: "CardInviteCommentPopup",
  components: {
    RetainCapitalPopup,
    TelephoneConsultationPopup,
    PrivateTelephoneConsultation,
    USafeBottom,
    AppPopup
  },
  mixins: [countDownMixin],
  props: {
    speciality: {
      type: Number,
      default: undefined
    },
    value: {
      type: Boolean,
      default: true
    },
    uvCode: {
      type: String,
      default: ""
    },
    /* 订单信息*/
    data: {
      type: Object,
      default: () => ({})
    },
    /** 历史留资信息 */
    historyInfo: {
      type: String,
      default: ""
    },
    /** 历史留资id */
    historyId: {
      type: [String, Number],
      default: ""
    },
    showSafeBottom: {
      type: Boolean,
      default: true
    },
    /** 是否包含直接留资入口 */
    isDirect: {
      type: Boolean,
      default: true
    },
    path: {
      type: Number,
      default: 5005
    }
  },
  data() {
    return {
      lawyerList: [],
      /** 降价服务 */
      discountService: {},
      privateChatConsultingServicesState: false,
      lawyerInfo: {},
      privateChatConsultingServiceInfo: {
        title: "1V1图文在线深度解答",
        data: {}
      },
      retainCapitalPopup: false
    };
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
  watch: {
    show: {
      handler(val) {
        if (val) {
          buryPointTransformationPath.add(5005);

          buryPointTransformationPath.add(this.path);

          buryPointChannelBasics({
            code: this.uvCode,
            type: 1,
            behavior: BURY_POINT_CHANNEL_TYPE.VI
          });

          this.getDiscountService();
          this.getTabData();
        }
      },
      immediate: true
    }
  },
  methods: {
    /** 获取降价服务 */
    getDiscountService() {
      serviceManegeInfoCommon(sceneType.im_zczx).then(({ data }) => {
        this.discountService = data;
      });
    },
    /** 获取数据 */
    async getTabData() {
      let params = {};

      try {
        const local = await getLocation();

        params = {
          proviceCode: local.provinceCode
        };
      } catch (e) {
        console.log(e);
      }

      lawyerListV3({
        /** 页码 */
        currentPage: 1,
        /** 每页条数 */
        pageSize: 5,
        specialityCode: this.speciality,
        ...params
      }).then(res => {
        this.lawyerList = res.data.records || [];
      });
    },
    handleConsult(data) {
      buryPointChannelBasics({
        code:
          "LAW_APPLET_AFTER_PAY_SECOND_CONSULT_POPUP_PAGE_CONSULT_NOW_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });

      toLawyerFakeImBefore({
        lawyerInfo: data,
        itemClassType: 1,
        graphicConsultationCallbacks: ({ serverInfo }) => {
          this.lawyerInfo = data;
          this.privateChatConsultingServiceInfo.data = serverInfo;
          this.privateChatConsultingServicesState = true;
        }
      });
    },
    /** 点击支付后触发的操作 */
    handleClickPrivatePay(service) {
      try {
        // 保存咨询信息
        caseSourceV2SaveGeneral({
          createFrom: 1,
          info: this.data.info,
          lawyerId: this.lawyerInfo.id,
          typeLabel: this.data.typeLabel,
          typeValue: this.data.typeValue,
          serverCode: service.serviceCode
        });
      } catch (e) {
        console.log(e);
      }
    },
    handleConsultAgain() {
      buryPointChannelBasics({
        code:
          "LAW_APPLET_AFTER_PAY_SECOND_CONSULT_POPUP_PAGE_CONSULT_AGAIN_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });

      const payFn = defaultPublicConsultationPay({
        serviceCode: this.discountService.serviceCode
      });

      Store.commit("payState/SET_PAY_FAIL_POPUP_PAY", payFn);
    },
    /** 点击关闭按钮 */
    handleClose() {
      if (this.isDirect) this.retainCapitalPopup = true;
      this.show = false;
    },
    async freeAskOtherLawyer() {
      try {
        buryPointTransformationPath.add(5037);

        buryPointChannelBasics({
          code:
            "LAW_APPLET_AFTER_PAY_SECOND_CONSULT_POPUP_PAGE_FREE_ASK_OTHER_LAWYER_CLICK",
          type: 1,
          behavior: BURY_POINT_CHANNEL_TYPE.VI
        });

        buryPointChannelBasics({
          code:
            "LAW_APPLET_AFTER_PAY_SECOND_CONSULT_POPUP_PAGE_CONSULT_OTHER_CLICK",
          type: 1,
          behavior: BURY_POINT_CHANNEL_TYPE.VI
        });

        // 先查询是否有留资信息
        const { data } = await caseSourceV2GetCaseOrZx();

        const type = Number(data.type);

        this.show = false;

        // 如果有留资信息
        if (type !== 0) {
          this.retainCapitalPopup = true;
          return;
        }

        // 如果没有留资信息，并且是默认文案
        if (this.historyInfo === "律师您好，我想向您咨询一个法律问题") {
          this.retainCapitalPopup = true;

          return;
        }

        console.log(this.data, "this.data");

        // 如果不是默认文案，则直接进入案源池
        await caseSourceV2Save({
          toCaseSourceServerV2Id: this.historyId,
          directToCaseSource: true
        });

        uni.showToast({
          title: "正在邀请律师，请等待",
          duration: 3500,
          icon: "none",
          mask: true
        });

        // 延迟打开弹窗
        setTimeout(() => {
          this.retainCapitalPopup = true;
        }, 4000);
      } catch (e) {
        console.log(e);
      }
    },
    /** 跳转律师主页 */
    toLawyerHome(id) {
      buryPointChannelBasics({
        code:
          "LAW_APPLET_AFTER_PAY_SECOND_CONSULT_POPUP_PAGE_LAWYER_CARD_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });

      toLawyerHome({
        id
      });
    }
  }
};
</script>
