<template>
  <scroll-view
    :scrollY="!visiBle"
    class="no-scroll"
  >
    <slot class="can-scroll" />
  </scroll-view>
</template>

<script>
export default {
  name: "MyLitigateStep",
  props: {
    visiBle: {
      type: Boolean,
      default: false
    }
  },
  methods: {
  },
};
</script>

<style lang="scss" scoped>
.no-scroll {
  height: 100vh;
  box-sizing: border-box;
  .can-scroll{
     overflow: '';
  }
}
</style>
