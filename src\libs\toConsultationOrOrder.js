import { dataDictionary } from "@/api/index";
import { toConsultation, toOrder } from "@/libs/turnPages.js";

/** 根据支付type，判断是跳转到付费咨询还是订单 */
export async function toConsultationOrOrder({ type }) {
  const data = await dataDictionary({
    groupCode: "SERVICE_APPLIED_RANGE"
  }).then(({ data }) => {
    return data.map(item => {
      try {
        return {
          ...item,
          supplementDesc: JSON.parse(item.supplementDesc)
        };
      } catch (e) {
        return {
          ...item,
          supplementDesc: {}
        };
      }
    });
  });
  
  const { supplementDesc = {} } = data.find(item => Number(item.value) === Number(type)) || {};
  
  if(Number(supplementDesc.im) === 1) {
    toConsultation({
      index: 1,
    });
  
    return;
  } 
  
  toOrder({
    index: 2
  });
}