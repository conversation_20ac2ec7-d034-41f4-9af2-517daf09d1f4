class Seo {
  /**
   * 设置页面信息
   * 此函数旨在为特定的运行环境提供页面信息配置
   * 仅在运行环境为MP-TOUTIAO时执行操作
   *
   * @param {Object} options - 包含页面信息的选项对象
   * @param {string} options.title - 页面文章标题，根据页面的主要内容设置，且需要每个页面尽量唯一。需要根据页面接口返回值进行设置。长度限制 70 字符，超出部分会被截断为省略号。
   * @param {string} options.keywords - 页面文章关键词，多个关键词之间使用英文逗号 “，” 隔开。
   * @param {string} options.description - 页面文章摘要、描述信息。长度限制 150 字符，超出部分会被截断为省略号。
   */
  setPageInfo(options){
    // #ifdef MP-TOUTIAO
    tt.setPageInfo(options);
    // #endif
  }
}

const seo = new Seo();

export default seo;
