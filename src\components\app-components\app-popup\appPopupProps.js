export default {
  props: {
    /** 点击遮罩是否关闭弹窗（注意：关闭事件需要自行处理，只会在开启closeOnClickOverlay后点击遮罩层执行close回调） */
    closeOnClickOverlay: {
      type: Boolean,
      default: true,
    },
    /** 是否显示遮罩 */
    overlay: {
      type: Boolean,
      default: true,
    },
    /** 设置圆角值 */
    round: {
      type: Number,
      default: 16,
    },
    /** 是否展示弹窗 */
    show: {
      type: Boolean,
      default: false,
    },
    /** 弹出方向 */
    mode: {
      type: String,
      default: "bottom",
      validator: (val) => ["bottom", "center", "top"].includes(val),
    },
    /** 是否显示取消按钮 */
    showCancelButton: {
      type: Boolean,
      default: false,
    },
    // 低亮度取消按钮，有点看不清那种
    lowCancelButton: {
      type: Boolean,
      default: false,
    },
    zIndex: {
      type: [Number, String],
      default: 1000,
    },
    bgColor: {
      type: String,
      default: "#ffffff",
    },
    popupStyle: {
      type: [Object, String],
      default: () => ({}),
    },
    /** 是否开启动画 */
    animation: {
      type: Boolean,
      default: true,
    },
    /** 是否监听键盘高度 */
    listenKeyboard: {
      type: Boolean,
      default: true,
    },
    /** 是否启用滑动折叠功能 */
    enableSwipeCollapse: {
      type: Boolean,
      default: false,
    },
    /** 收缩时距离顶部的距离（px） */
    collapseDistance: {
      type: Number,
      default: 100,
    },
    /** 弹窗初始是否为收缩状态（仅在enableSwipeCollapse为true时有效） */
    initialCollapsed: {
      type: Boolean,
      default: false,
    },
  },
};
