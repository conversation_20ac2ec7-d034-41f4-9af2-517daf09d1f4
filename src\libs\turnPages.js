/**
 * 跳转到律师引导付费界面
 * @param {string} lawyerId 律师id
 * @param {string} [type] 1:快速咨询 2:其它咨询，其它咨询可不传
 * @param serverCode 选择的服务码
 */
import { setReturnRouterStorage } from "@/libs/token.js";
import qs from "qs";
import { handleOrderPay } from "@/libs/pay";
import { refundTypeEnum } from "@/pages/apply-refund/js/refundInfo.js";
import { caseSourceV2GetCaseOrZx } from "@/api/special";
import store from "@/store";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import { whetherToLogIn } from "@/libs/tools";
import { SEARCH_PAGE_TYPE } from "@/libs/config";
import { privateChatSaveAppoint } from "@/api/user";
import { saveLatestLawyer } from "@/pages/lawyer-home";
import { paralegalHistory } from "@/libs/paralegalTools";
import { getNewEntrustmentInfo } from "@/pages/submit-question/lawsuit/js/index";

/**
 * 将 query对象转换为字符串
 * @param query
 * @returns {string}
 */
export function queryToString(query) {
  let str = "?";
  for (let key in query) {
    str += `${key}=${query[key]}&`;
  }
  return str.substring(0, str.length - 1);
}

export function formatQuery({ path, query = {} }) {
  // 过滤 query 中的空值
  const queryStr = Object.keys(query)
    .filter(key => {
      return query[key] !== undefined && query[key] !== null;
    })
    .map(key => {
      return `${key}=${query[key]}`;
    })
    .join("&");

  return `${path}${queryStr ? "?" + queryStr : ""}`;
}

/**
 * 将 uni 的页面跳转方法转化为类Vue的跳转方法
 */
export function turnPages(options, isReplace = false) {
  return new Promise((resolve, reject) => {
    const { path, query, animationType, animationDuration } = options;
    // 过滤 query 中的空值
    for (let key in query) {
      if (query[key] === undefined || query[key] === null) {
        delete query[key];
      }
    }

    const queryStr = queryToString(query);
    let navigateTo = uni.navigateTo;
    if (isReplace) {
      navigateTo = uni.redirectTo;
    }
    navigateTo({
      url: path + queryStr,
      success() {
        resolve();
      },
      fail(error) {
        console.log(error);
        reject();
      },
      animationType,
      animationDuration
    });
  });
}

/**
 * 跳转到Im界面
 * @param lawyerId 律师id
 * @param [caseSourceId] 案源id
 * @param conversationId im会话id
 * @param isReplace
 */
export function toImChatPage(
  { lawyerId, caseSourceId, conversationId },
  isReplace = false
) {
  // 律师id 和 im会话id 必须同时存在，才跳转到im界面
  if (lawyerId && conversationId) {
    turnPages(
      {
        path:
          "/pages/lawyer-im/appoint-consulting-assistant-im-chatroom_v2/index",
        query: {
          id: lawyerId,
          caseSourceId,
          conversationId
        }
      },
      isReplace
    );
  }
}

/** 获取当前页面路由 */
export function getCurrentPageRoute() {
  // 获取加载的页面
  // eslint-disable-next-line no-undef
  const pages = getCurrentPages();

  // 获取当前页面的对象
  const currentPage = pages[pages.length - 1] || {};

  // 当前页面url
  return {
    fullPath: currentPage.$page?.fullPath,
    query: qs.parse(currentPage.$page?.fullPath?.split("?")?.[1]),
    path: currentPage.$page?.route
  };
}

/**
 * 跳转到订单确定页面
 * @param {number} serviceCode
 * @param {string} [type] 现在已经不需要传递该值 (1:案源；2:1v1 3:公共咨询) 6 公共付费
 * @param {string} [lawyerId]
 * @param {string} [businessId] type===1传
 * @param {string} [orderId] 有orderId传
 * @param {string} [synHistoryId] 历史记录id
 * @param {string} [synWdId] 公共付费问答id
 * @param {string} [path] 订单确定页面返回的路径
 * @param {string} [synCaseSourceId] 公共付费案源id
 * @param {Function} [paySuccessCallback] 支付成功回调
 */
export function toConfirmOrder(
  {
    serviceCode,
    type,
    lawyerId,
    businessId,
    orderId,
    synHistoryId,
    synWdId,
    synCaseSourceId,
    paySuccessCallback
  },
  path
) {
  store.commit("payState/SET_PAY_PAGE_ROUTE", getCurrentPageRoute().fullPath);

  // loading
  uni.showLoading({
    mask: true,
    title: "订单创建中"
  });

  const route = getCurrentPageRoute();

  setReturnRouterStorage(path || route.fullPath);

  let query = {};

  if (type === "1" || type === "8") {
    query = {
      serviceCode,
      businessId,
      type,
      paySuccessCallback
    };
  } else {
    query = {
      serviceCode,
      type,
      lawyerId,
      businessId,
      orderId,
      synHistoryId,
      synWdId,
      synCaseSourceId,
      paySuccessCallback
    };
  }

  // turnPages({
  //   path: "/pages/order/index",
  //   query,
  // });
  handleOrderPay(query);
}

/* 律师详情页面跳转
 *isFree:是否免费咨询
 *data.isSpeedConsultation:是否是快速咨询(在传入的data里面添加此参数)
 */
export function toFindLawyerDetail(data) {
  /** data.lawyerType==2不跳转 咨询师 data.role:官方不跳转  */
  if (data.lawyerType === 2 || data.role === 3) {
    /* this.$emit('toChat', data)*/
  } else {
    // if (data.isVip) {
    toPayLawyerGuide({
      lawyerId: data.id,
      type: data.isSpeedConsultation ? 1 : 2
    });
    // }
  }
}

/** 跳转到问律师界面 */
export function toAskLawyer(query = {}) {
  caseSourceV2GetCaseOrZx()
    .then(res => {
      /** 0 无 1 案源 2 问答 */
      const type = Number(res.data?.type || 0);

      if (type !== 0) {
        toSubmitQuestion(query);
      } else {
        turnPages({
          path: "/pages/submit-question/return",
          query
        });
      }
    })
    .catch(() => {
      turnPages({
        path: "/pages/submit-question/return",
        query
      });
    });
}

export function toSubmitQuestion(query = {}) {
  if (paralegalHistory.isFakePrivate()) {
    return toSubmitQuestionPrivate(query);
  }

  return turnPages({
    path: "/pages/submit-question/index",
    query
  });
}

export function toSubmitQuestionPrivate(query = {}) {
  return turnPages({
    path: "/pages/submit-question/private",
    query
  });
}

export const QUESTION_RESULT_FIRST = "questionResultFirst";

/**
 * 跳转到问律师结果页
 * 如果是抖音小程序，则跳转到订阅页面
 */
export function toAskLawyerResult() {
  // 设置缓存
  uni.setStorageSync(QUESTION_RESULT_FIRST, 1);

  // #ifdef MP-TOUTIAO || MP-WEIXIN
  uni.redirectTo({
    url: "/pages/submit-question-result/index?redirect=1"
  });
  // #endif

  // #ifndef  MP-TOUTIAO || MP-WEIXIN
  uni.redirectTo({
    url: "/pages/submit-question-result/index"
  });
  // #endif
}

/**
 * 跳转到律师主页
 * @param {string} id 律师id
 */
export function toLawyerHome({ id }) {
  console.log(id);
  if (!id) {
    return;
  }

  turnPages({
    path: "/pages/lawyer-home/index",
    query: {
      id
    }
  });
}

/**
 * 跳转到律师主页-案件统计
 */
export function toLawyerHomeCaseStatistics({ id }) {
  turnPages({
    path: "/pages/lawyer-home/case/index",
    query: {
      id
    }
  });
}

/**
 * 跳转到律师主页-裁决文书
 */
export function toLawyerHomeJudgment({ id }) {
  turnPages({
    path: "/pages/lawyer-home/document/index",
    query: {
      id
    }
  });
}

/**
 * 跳转到律师主页-案例详情
 */
export function toLawyerHomeJuDocumentDetail({ id, lawyerId, state }) {
  /* 是不是个人上传案例 详情页*/
  const path = state
    ? "/pages/lawyer-home/document-detail-of-me/index"
    : "/pages/lawyer-home/document-detail/index";
  turnPages({
    path: path,
    query: {
      id,
      ...(lawyerId ? { lawyerId } : {})
    }
  });
}

/** 跳转到律师案件详情 */
export function toLawyerCaseDetail({ id, lawyerId }) {
  turnPages({
    path: "/pages/lawyer-home/wonderful-case/detail/index",
    query: {
      id,
      lawyerId
    }
  });
}

/**
 * 跳转到律师主页-评价
 */
export function toLawyerHomeComment({ id, serviceCode }) {
  turnPages({
    path: "/pages/lawyer-home/comment/index",
    query: {
      id,
      serviceCode
    }
  });
}

/**
 * 跳转到律师引导付费界面
 * @param {string} lawyerId 律师id
 * @param {string | number} [type] 1:快速咨询 2:其它咨询，其它咨询可不传
 * @param serverCode 选择的服务码
 * @param {string | number} [createSource] 案源或者咨询数据来源 0 默认 1问律师 2打官司 3新版本文章详情
 * @param {string | number} [createSourceBusinessId] 案源或者咨询数据来源业务id 为3时为文章id
 */
export function toPayLawyerGuide({
  lawyerId,
  type,
  serverCode,
  createSource,
  createSourceBusinessId
}) {
  if (!lawyerId) {
    return;
  }

  turnPages({
    path: "/pages/lawyer-home/pay-lawyer-guide/index",
    query: {
      lawyerId,
      type,
      serverCode,
      createSource,
      createSourceBusinessId
    }
  });
}

/** 跳转到律师服务页面 */
export function toLawyerService({ scene }) {
  turnPages({
    path: "/pages/lawyer-home/lawyer-services/index",
    query: {
      scene
    }
  });
}

/** 跳转到专场二级页面 */
export function toSpecialSecond({ type }) {
  turnPages({
    path: "/pages/rapid-consultation-confirm-order/special/index",
    query: {
      type
    }
  });
}

/** 跳转到设置页面 */
export function toSetting() {
  whetherToLogIn(() => {
    turnPages({
      path: "/pages/other/mysetting/index"
    });
  });
}

export function toConsultationPage({ index }) {
  switch (index) {
  case 0:
    return "/pages/rapid-consultation-confirm-order/my-consultation/question/index";
  case 1:
    return "/pages/rapid-consultation-confirm-order/my-consultation/paidconsult/index";
  case 2:
    return "/pages/rapid-consultation-confirm-order/my-consultation/case/index";
  default:
    break;
  }
}

/**
 * 跳转到咨询页面
 * @param index 0 问答 1 付费咨询 2 案源
 */
export function toConsultation({ index = 0 } = {}, isReplace = false) {
  whetherToLogIn(() => {
    uni.setStorageSync("my-consultation-index", index);

    uni.$emit("updateIndex", index);

    turnPages({
      path: toConsultationPage({ index })
    },
    isReplace);
  });
}

/** 跳转到意见书界面 */
export function toOpinionBook() {
  turnPages({
    path: "/pages/other/opinion-book/index"
  });
}

/** 跳转到关注公众号界面 */
export function toFollowPublic() {
  toOfficialAccountMini();
}

/** 跳转到退款界面 */
export function toRefund({ orderId, refundType = "newOrder" }) {
  if (!orderId) {
    return;
  }

  turnPages({
    path: "/pages/apply-refund/index",
    query: {
      orderId,
      refundType,
      type: refundTypeEnum.REFUND
    }
  });
}

/** 退款详情界面 */
export function toRefundDetail({ orderId, refundType = "newOrder" }) {
  if (!orderId) {
    return;
  }

  turnPages({
    path: "/pages/refund-detail/index",
    query: {
      orderId,
      refundType,
      type: refundTypeEnum.REFUND
    }
  });
}

/** 跳转到申请售后界面 */
export function toApplyAfterSale({ orderId }) {
  if (!orderId) {
    return;
  }

  turnPages({
    path: "/pages/apply-refund/after-sales/index",
    query: {
      orderId
    }
  });
}

/** 跳转到换律师界面 */
export function toChangeLawyer({ orderId, refundType = "newOrder" }) {
  if (!orderId) {
    return;
  }

  turnPages({
    path: "/pages/apply-refund/index",
    query: {
      orderId,
      refundType,
      type: refundTypeEnum.CHANGE_LAWYER
    }
  });
}

/** 跳转到换律师详情界面 */
export function toChangeLawyerDetail({ orderId, refundType = "newOrder" }) {
  if (!orderId) {
    return;
  }

  turnPages({
    path: "/pages/refund-detail/index",
    query: {
      orderId,
      refundType,
      type: refundTypeEnum.CHANGE_LAWYER
    }
  });
}

/** 跳转到订单界面 */
export function toOrder({ index }) {
  turnPages({
    path: "/pages/myorder/index",
    query: {
      index
    }
  });
}

/** 跳转到客服中心界面 */
export function toCustomerServiceCenter() {
  // #ifndef  MP-BAIDU || MP-ALIPAY
  turnPages({
    path: "/pages/other/customer/index"
  });
  // #endif
}

/** 跳转到我的提问界面 */
export function toMyQuestion({ id }, isReplace = false) {
  if (!id) return;

  turnPages(
    {
      path: "/pages/lawyer-home/my-question/index",
      query: {
        id
      }
    },
    isReplace
  );
}

/**
 * 跳转文章列表
 * @param [lawyerId]
 * @param [typeValue]
 */
export function toArticleList({ lawyerId, typeValue, isSearch } = {}) {
  if (lawyerId) {
    turnPages({
      path: "/pages/lawyer-home/essay-list/index",
      query: {
        lawyerId,
        typeValue
      }
    });

    return;
  }

  turnPages({
    path: "/pages/lawyer-home/essay-list/index",
    query: {
      typeValue
    }
  });
}

/* 跳转文章详情*/
export function toArticleDetail({ id }) {
  turnPages({
    path: "/pages/lawyer-home/essay-detail/index",
    query: {
      id
    }
  });
}

/** 跳转到问答详情 */
export function toQuestionDetail({ id }) {
  turnPages({
    path: "/pages/ask-details/index",
    query: {
      id
    }
  });
}

export function toQaPage() {
  turnPages({
    path: "/pages/downloadpage/index",
    query: {
      addressUrl: process.env.VUE_APP_ENV_APPH5 + "serverQa"
    }
  });
}

/** 打开第三方页面 */
export function toThirdPartyPage({ addressUrl, title }) {
  turnPages({
    path: "/pages/downloadpage/index",
    query: {
      addressUrl,
      title
    }
  });
}

/** 关注公众号 */
export function toFollowPublicPage() {
  buryPointChannelBasics({
    code: "LAW_APPLET_FOLLOW_OFFICIAL_ACCOUNT_PAGE",
    behavior: BURY_POINT_CHANNEL_TYPE.CK,
    type: 1
  });

  toOfficialAccountMini();
}

/** 跳转到通用专场界面 */
export function toSpecialSession({ typeValue = "26" }) {
  turnPages({
    path: "/pages/rapid-consultation-confirm-order/special-session/index",
    query: {
      typeValue
    }
  });
}

/** 跳转到自营律师下单页 */
export function toSelfSupportLawyerOrder() {
  turnPages({
    path: "/pages/rapid-consultation-confirm-order/fast/index"
  });
}

/**
 * 跳转到1v1律师假im
 * @param id
 * @param {string | number} [type] 1:快速咨询 2:其它咨询，其它咨询可不传
 * @param {string | number} [createSource] 案源或者咨询数据来源 0 默认 1问律师 2打官司 3新版本文章详情
 * @param {string | number} [createSourceBusinessId] 案源或者咨询数据来源业务id 为3时为文章id
 * @param typeValue
 * @param typeLabel
 */
export function toLawyerFake({
  id,
  type,
  createSource,
  createSourceBusinessId,
  typeValue,
  typeLabel
}) {
  if (!id) return;

  // 有则去 1v1假IM流程 页面
  turnPages({
    path: "/pages/submit-question/lawyer-fake/index",
    query: {
      id,
      type,
      createSource,
      createSourceBusinessId,
      typeValue,
      typeLabel
    }
  });
}

/**
 * 跳转到1v1律师假im
 * @param id
 * @param {string | number} [type] 1:快速咨询 2:其它咨询，其它咨询可不传
 * @param {string | number} [createSource] 案源或者咨询数据来源 0 默认 1问律师 2打官司 3新版本文章详情
 * @param {string | number} [createSourceBusinessId] 案源或者咨询数据来源业务id 为3时为文章id
 * @param typeValue
 */
export function toLawyerFakeIm({
  id,
  type,
  createSource,
  createSourceBusinessId,
  typeValue,
  typeLabel
}) {
  if (!id) return;
  saveLatestLawyer(id);

  caseSourceV2GetCaseOrZx().then(res => {
    /** 0 无 1 案源 2 问答 */
    const caseType = Number(res.data?.type || 0);

    // 咨询过并且没有付费
    if (caseType === 0) {
      // 判断是否有留资 如果没有则去 1v1问律师流程 页面
      turnPages({
        path: "/pages/submit-question/one-by-one/index",
        query: {
          id
        }
      });
    } else {
      // 有则去 1v1假IM流程 页面
      toLawyerFake({
        id,
        type,
        createSource,
        createSourceBusinessId,
        typeValue,
        typeLabel
      });
    }
  });
}

export function hotTopicPath() {
  return formatQuery({
    path: "/pages/rapid-consultation-confirm-order/mall/index"
  });
}

/** 跳转到热门专题页面 */
export function toHotTopic() {
  turnPages({
    path: hotTopicPath()
  });
}

export function findLawyerPath() {
  return formatQuery({
    path: "/pages/submit-question/findlawyer/index"
  });
}

/** 跳转到找律师界面 */
export function toFindLawyer() {
  turnPages({
    path: findLawyerPath()
  }, true);
}

/** 跳转到全部律师界面 */
export function toAllLawyer(options) {
  const { typeValue, ...other } = options || {};

  turnPages({
    path: "/pages/lawyer-home/find-lawyer/index",
    query: {
      typeValue,
      ...other
    }
  });
}

/** 跳转到全部解答界面 */
export function toAllAnswer({ typeValue }) {
  turnPages({
    path: "/pages/lawyer-home/all-answer/index",
    query: {
      typeValue
    }
  });
}

/** 跳转到法律指南界面 */
export function toLawGuide() {
  turnPages({
    path: "/pages/lawyer-home/ask/index"
  });
}

/** 跳转到结果页更多问答页面 */
export function toMoreAnswer() {
  turnPages({
    path: "/pages/ask-details/search/result/questions/index"
  });
}

/** 跳转到结果页更多文章页面 */
export function toMoreArticle() {
  turnPages({
    path: "/pages/ask-details/search/result/article/index"
  });
}

/** 跳转到分类律师列表 */
export function toLawyerTypeList({ value }) {
  turnPages({
    path: "/pages/lawyer-home/find-type-lawyer/index",
    query: {
      typeValue: value
    }
  });
}

/** 跳转到专家咨询列表 */
export function toExpertConsultationPage() {
  turnPages({
    path: "/pages/rapid-consultation-confirm-order/expert-consultation/index"
  });
}

/** 跳转到绑定公众号页面 */
export function toOfficialAccountMini() {
  turnPages({
    path: "/pages/other/official-account/index"
  });
}

/** 跳转到搜索页面 */
export function toSearchPage({ type = SEARCH_PAGE_TYPE.COMMON } = {}) {
  turnPages({
    path: "/pages/ask-details/search/index",
    query: {
      type
    }
  });
}

/** 跳转到搜索结果页面 */
export function toSearchResultPage(options = {}) {
  const { type } = options;

  buryPointChannelBasics({
    code: "LAW_APPLET_SEARCH_PAGE_SEARCH_SUMBIT_CLICK",
    type: 1,
    behavior: BURY_POINT_CHANNEL_TYPE.CK
  });

  if (type === SEARCH_PAGE_TYPE.CONTRACT) {
    turnPages({
      path:
        "/pages/rapid-consultation-confirm-order/contract-search-result/index",
      query: options
    });

    return;
  }

  if (type === SEARCH_PAGE_TYPE.SPECIAL) {
    turnPages({
      path: "/pages/ask-details/search/special/index",
      query: options
    });

    return;
  }

  turnPages({
    path: "/pages/ask-details/search/result/index",
    query: options
  });
}

/** 跳转到全部律师页 */
export function toAllLawyerPage() {
  turnPages({
    path: "/pages/lawyer-home/all-article/index"
  });
}

/* 跳转合同模板列表页面*/
export function toContractTemplatesPage() {
  turnPages({
    path: "/pages/rapid-consultation-confirm-order/contract-templates/index"
  });
}

/* 跳转合同模板详情页面*/
export function toContractTemplatesDetailPage({ id }) {
  whetherToLogIn(() => {
    turnPages({
      path:
        "/pages/rapid-consultation-confirm-order/contract-templates-detail/index",
      query: {
        id
      }
    });
  });
}

/* 跳转合同模板详情页面*/
export function toContractTemplatesDetailLawyerPage({ id }) {
  if (!id) return;

  turnPages({
    path:
      "/pages/rapid-consultation-confirm-order/contract-templates-detail-lawyer/index",
    query: {
      id
    }
  });
}

/**
 * 跳转到高级服务页面
 */
export function toHighLevelServicePage({ type }) {
  if (!type) return;

  turnPages({
    path: "/pages/rapid-consultation-confirm-order/high-level-service/index",
    query: {
      type
    }
  });
}

/**
 * 跳转到我的案例详情页面
 * @param {Object} param 包含id的对象
 * @param {string} param.id 案例的id
 */
export function toMyCase({ id }) {
  if (!id) return;

  turnPages({
    path: "/pages/lawyer-home/my-case/index",
    query: {
      id
    }
  });
}

/**
 * 跳转到商品详情页面
 */
export function toGoodsDetail({ serviceCode }) {
  turnPages({
    path: "/pages/lawyer-home/goods-detail/index",
    query: {
      serviceCode
    }
  });
}

/**
 * 跳转到确认订单页面
 */
export function toConfirmOrderPage({ serviceCode }) {
  turnPages({
    path: "/pages/lawyer-home/goods-detail/order/index",
    query: {
      serviceCode
    }
  });
}

/**
 * 跳转到案件委托页面
 */
export function toDelegationPage() {
  whetherToLogIn(() => {
    // 判断是否有留资
    caseSourceV2GetCaseOrZx().then(res => {
      const type = Number(res.data?.type || 0);

      if (type === 0) {
        turnPages({
          path: "/pages/lawyer-home/goods-detail/delegation/index"
        });

        return;
      }

      toDelegationResultPage();
    });
  });
}

/**
 * 跳转到案件委托结果页面
 */
export function toDelegationResultPage({ isReplace = false } = {}) {
  turnPages(
    {
      path: "/pages/lawyer-home/goods-detail/delegation-result/index"
    },
    isReplace
  );
}

/** 跳转到首页 */
export function toHome() {
  uni.switchTab({
    url: "/pages/index/index"
  });
}

/** 跳转到待支付页面 */
export function toBePaidPage({ serviceCode, lawyerId, orderId, businessId }) {
  turnPages({
    path: "/pages/submit-question/to-be-paid/index",
    query: {
      serviceCode,
      lawyerId,
      orderId,
      businessId
    }
  });
}

/** 跳转到1v1私聊页面 */
export function toOneToOneChatPage({ lawyerId }, isReplace = false) {
  // 判断是否登录
  if (!store.getters["user/getToken"]) {
    turnPages({
      path: "/pages/submit-question/fake-im/index",
      query: {
        lawyerId
      }
    });

    return;
  }

  privateChatSaveAppoint({
    lawyerId
  }).then(({ data }) => {
    toImChatPage(
      {
        lawyerId: data.lawyerId,
        conversationId: data.imSessionId,
        caseSourceId: data.caseSourceServerV2Id
      },
      isReplace
    );
  });
}

/** 跳转到消息页面 */
export function toListOfMessages() {
  turnPages({
    path: "/pages/list-of-messages/index"
  });
}

/** 跳转到律师排行页面 */
export function toLawyerRankPage() {
  turnPages({
    path: "/pages/lawyer-home/lawyer-ranking/index"
  });
}

/** 跳转到精选解答页面 */
export function toFeaturedAnswersPage() {
  turnPages({
    path: "/pages/rapid-consultation-confirm-order/featured-answers/index"
  });
}

/** 跳转到相似方案页面 */
export function toLikeFeaturedAnswersPage({ text }) {
  turnPages({
    path: "/pages/rapid-consultation-confirm-order/like-featured-answers/index",
    query: {
      text
    }
  });
}

/** 跳转到咨询结果页面 */
export function toAskResultPage() {
  turnPages(
    {
      path: "/pages/rapid-consultation-confirm-order/ask-result/index"
    },
    true
  );
}

export function fastAskPath() {
  // #ifdef MP-TOUTIAO
  return formatQuery({
    path: "/pages/rapid-consultation-confirm-order/fast-ask/index"
  });
  // #endif
  // #ifndef MP-TOUTIAO
  return formatQuery({
    path: "/pages/submit-question/ai/index"
  });
  // #endif
}

/** 跳转到新的极速问律师界面 */
export function toFastAskPage() {
  buryPointChannelBasics({
    code: "LAW_APPLET_BOTTOM_ASK_CLICK",
    type: 1,
    behavior: BURY_POINT_CHANNEL_TYPE.CK
  });
  // #ifdef MP-TOUTIAO
  caseSourceV2GetCaseOrZx()
    .then(res => {
      /** 0 无 1 案源 2 问答 */
      const type = Number(res.data?.type || 0);

      if (type !== 0) {
        toAskResultPage();
      } else {
        turnPages({
          path: fastAskPath()
        });
      }
    })
    .catch(() => {
      turnPages({
        path: fastAskPath()
      });
    });
  // #endif

  // #ifndef MP-TOUTIAO
  turnPages({
    path: fastAskPath()
  });
  // #endif
}

export function toCityOptions() {
  turnPages({
    path: "/pages/rapid-consultation-confirm-order/city-options/index"
  });
}

export function askDetailPage({ caseSourceServerId }) {
  return formatQuery({
    path: "/pages/askdetail/index",
    query: {
      caseSourceServerId
    }
  });
}

export function toAskDetail({ caseSourceServerId }) {
  turnPages({
    path: askDetailPage({
      caseSourceServerId
    })
  });
}

/* 跳转二维码页面*/
export function toCustomerQrCodePage({ code }) {
  turnPages({
    path: "/pages/toutiao-download/customer/index",
    query: {
      code
    }
  });
}

/** 跳转到律师解决方案页面 */
export function toLawyerSolutionPage() {
  turnPages({
    path: "/pages/submit-question/lawyer-solution/index"
  });
}

// 跳转律师费计算器
export function toLawyerFeeCalculatorPage() {
  turnPages({
    path: "/pages/submit-question/caculator/civil/index"
  });
}

// 跳转劳动纠纷计算器
export function toLaborDisputeCalculatorPage() {
  turnPages({
    path: "/pages/submit-question/caculator/labor-arbitration/index"
  });
}

// 跳转合同审查计算器
export function toContractReviewCalculatorPage() {
  turnPages({
    path: "/pages/submit-question/caculator/contract-review/index"
  });
}

/** 跳转到打官司成功页面 */
export function toLawsuitSuccessPage(isReplace = false) {
  turnPages({
    path: "/pages/submit-question/lawsuit/success/index"
  }, isReplace);
}

export function toLawsuitIndexPage() {
  turnPages({
    path: "/pages/submit-question/lawsuit/index"
  });
}

/** 跳转到打官司页面 */
export function toLawsuitPage() {
  getNewEntrustmentInfo().then(({ latestSuitCase }) => {
    const { caseStatus } = latestSuitCase || {};
    // 已结案说进入打官司的话是直接进入留资页1，可以重新留资
    if(caseStatus === 5){
      return Promise.reject();
    }
    toLawsuitSuccessPage();
  }).catch(() => {
    toLawsuitIndexPage();
  });
}

/** 跳转到支付尾款页面 */
export function toLawsuitPayPage({ id, isReplace = false }) {
  turnPages({
    path: "/pages/submit-question/lawsuit/pay/index",
    query: {
      id
    }
  }, isReplace);
}

/** 跳转到我发布的官司页面 */
export function lawsuitPublishedPath() {
  return formatQuery({
    path: "/pages/submit-question/lawsuit/published/index"
  });
}

/** 跳转到我发布的官司列表页面 */
export function toLawsuitPublishedPage() {
  turnPages({
    path: lawsuitPublishedPath()
  });
}

/** 跳转到我发布的官司详情页面 */
export function toLawsuitPublishedDetailPage({ id }) {
  turnPages({
    path: "/pages/submit-question/lawsuit/published/detail/index",
    query: {
      id
    }
  });
}

/** 跳转到订单详情页面 */
export function toOrderDetailPage({ id }) {
  turnPages({
    path: "/pages/myorder/order-detail/index",
    query: {
      id
    }
  });
}

/** 跳转到注销页面 */
export function toAccountDeactivationPage() {
  turnPages({
    path: "/pages/submit-question/account-deactivation/index"
  });
}

/** 跳转到注销成功页面 */
export function toAccountDeactivationSuccessPage() {
  turnPages({
    path: "/pages/submit-question/account-deactivation/success/index"
  });
}

/** 跳转到我的页面 */
export function toMyPage() {
  uni.switchTab({ url: "/pages/mine/index" });
}
