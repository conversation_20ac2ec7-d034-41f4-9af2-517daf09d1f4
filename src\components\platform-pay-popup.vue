<template>
  <!-- 返回拦截弹窗 -->
  <app-popup
    :closeOnClickOverlay="false"
    :safeAreaInsetBottom="true"
    :show="show"
    :zIndex="9998"
    mode="center"
  >
    <div class="popup-content">
      <img
        alt=""
        class="popup-content-close-button"
        src="@/pages/submit-question/components/submit-pay-popup/img/close.png"
        @click="close"
      >
      <pay-fail-top-swiper />
      <div class="tips">
        <p class="tips-top">
          当前为平台补贴价，请尽快支付
        </p>
        <p class="tips-bottom">
          已为您升级平台优选律师，快速解决您的问题
        </p>
      </div>
      <img
        alt=""
        class="pay-button"
        mode="widthFix"
        src="@/pages/submit-question/components/submit-pay-popup/img/pay-button.png"
        @click="pay"
      >
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import payFailMixins from "@/mixins/payFailMixins.js";
import PayFailTopSwiper from "@/components/pay-fail-top-swiper/index.vue";
import Store from "@/store/index.js";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE, POINT_CODE } from "@/enum/burypoint";

export default {
  name: "PlatformPayPopup",
  components: { PayFailTopSwiper, AppPopup },
  mixins: [payFailMixins],
  data() {
    return {
      show: false,
    };
  },
  methods: {
    /** 支付失败时回调 */
    payFailCallback() {
      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_CONSULT_PAGE_PUBILC_CONSULT_BACK_INTERCEPT_PAGE,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
      });

      this.show = true;
    },
    close() {
      this.show = false;

      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_CONSULT_PAGE_PUBILC_CONSULT_BACK_CLOSE_CLICK,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
      });

      this.$emit("close", false);
    },
    pay() {
      Store.commit("payState/PAY_FAIL_POPUP_PAY");

      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_CONSULT_PAGE_PUBILC_CONSULT_SUBMIT_PAY_CLICK,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
      });

      this.$emit("click");
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-content {
  width: 311px;
  height: 364px;
  position: relative;
  border-radius: 16px;
  opacity: 1;
  box-sizing: border-box;
  background-color: #fff;
  overflow: hidden;
  text-align: center;
}

.popup-content-close-button {
  position: absolute;
  width: 24px;
  height: 24px;
  right: 12px;
  top: 12px;
  z-index: 2;
}

.tips {
  &-top {
    font-size: 17px;
    font-weight: bold;
    color: #333333;
  }

  &-bottom {
    margin-top: 4px;
    font-size: 13px;
    font-weight: 400;
    color: #999999;
  }
}

.pay-button {
  margin-top: 15px;
  width: 287px;
  border-radius: 60px;
}
</style>
