import { WS_MESSAGE_TYPE } from "@/libs/config.js";
import { createRandomId } from "@/libs/tools.js";

export default (otherSideUserName, currentUserName) => {
  const current_user_name = currentUserName;
  return {
    /* 设置公共发送数据*/
    setPrivateCommon(data, username = current_user_name) {
      return {
        id: createRandomId(),
        msg: data.msg,
        from: username,
        ...data
      };
    },
    /* 设置公共自定义发送数据*/
    setPrivateCustom(data, username = current_user_name, otherData = {}) {
      return this.setPrivateCommon(
        {
          msgType: WS_MESSAGE_TYPE.CUSTOM,
          msg: JSON.stringify({
            customEvent: "server",
            customExts: data
          }),
          ...otherData
        },
        username
      );
    },
    /* 设置文字发送数据*/
    setPrivateText(msg, username = current_user_name, data = {}) {
      return this.setPrivateCommon(
        {
          msg,
          msgType: WS_MESSAGE_TYPE.TEXT,
          ...data
        },
        username
      );
    }
  };
};

/**
 * 律师助手界面的卡片
 * 假的im卡片
 * @Version 2.2.0
 */
const paralegalCard = {
  // 2.1.0 律师助手 写死的卡片
  CARD_ADV: "card_adv",
  FAKE_CARD_ADV: "fake_card_adv",
  THROW_CARD_ADV: "throw_card_adv",
  // 2.1.0 律师助手 咨询选择
  CONSULTING_OPTIONS: "consulting_options",
  /** 2.1.3 快速咨询，律师一对一服务卡片 */
  QUICK_SERVER_ONE_TO_ONE: "quick_server_one_to_one",
  /** 2.1.8 律师助手 保密文字 */
  CONFIDENTIAL_TEXT: "confidential_text",
  /** 2.2.0 律师助手 等待文字卡片 */
  WAITING_FOR_CARD: "waiting_for_card",
  /** 2.2.0 律师助手 引导文字 */
  INTRODUCTORY_TEXT: "introductory_text",
  /** 2.2.0 律师助手 重新咨询 */
  RE_CONSULT: "re_consult",
  /** 2.2.0 律师助手 为您推荐 */
  RECOMMENDED_FOR_YOU: "recommended_for_you",
  /** 2.2.1 律师助手 补充信息 */
  ADDITIONAL_INFORMATION: "additional_information",
  /** 2.2.1 律师助手 补充信息案件委托 */
  ADDITIONAL_INFORMATION_CARD: "additional_information_card",
  /** 2.2.1 律师助手 为您推荐 */
  RECOMMENDED_YOU: "recommended_you",
  /** 2.6.0 结果卡片 */
  CARD_RESULT: "card-result",
  /** 2.6.0 支付成功提示 */
  CARD_PAY_SUCCESSFUL: "card-pay-successful",
  /** 3.0.8 假律师im服务卡片 */
  CARD_LAWYER_FAKE_SERVICE_INFO: "card-lawyer-fake-service-info",
  /** 3.1.2 加位置选择卡片 */
  CARD_ADDRESS_INFO: "card-address-info",
  /** 微信过审图 */
  CARD_LAWYER_PIC: "CardLawyerPic",
  QUICK_ENTRY: "card-quick-entry",
  ANALYZING: "card-analyzing",
  GO_CUSTOMER_SERVICE: "card-go-customer-service",
  CARD_INTERVAL_SWIPER: "card-interval-swiper",
  CARD_INTERVAL_SWIPER_THROW: "card-interval-swiper-throw",
  SUBMIT_CARD_TIPS: "submit_card_tips",
  SUBMIT_CARD_QUEUE: "submit_card_queue",
  SUBMIT_CARD_COUNT_DOWN: "submit_card_count_down",
  SUBMIT_AI_TEXT: "submit_ai_text",
  SUBMIT_AI_CHAT: "submit_ai_chat",
  SUBMIT_AI_PUBLIC_CONSULTATION: "submit_ai_public_consultation",
  SUBMIT_AI_LEGAL_OPINION: "submit_ai_legal_opinion",
  SUBMIT_AI_LEGAL_OPINION_SUCCESS: "submit_ai_legal_opinion_success",
  /** 输入字数过少提示 */
  SUBMIT_MIN_LENGTH_TIP: "submit_min_length_tip"
};

/* im发送类型*/
export const webImSendType = {
  TEXT: "TEXT",
  IMAGE: "IMAGE",
  FILE: "FILE",
  CUSTOM: "CUSTOM",
  STYLE_CARD: "style_card",
  STYLE_TXT: "style_txt",
  SERVER_INFO: "server_info",
  SERVER_HTML: "server_html",
  CONSULTING_ASSISTANT_LOGIN: "consulting_assistant_login",
  CONSULTING_ASSISTANT_CARD: "consulting_asistant_card",
  STYLE_TXT_SUB_2: "style_txt_sub_2",
  TIP: "tip",
  // 律师自动跳转连接
  LAWYER_LINK: "lawyer_link",
  LAWYER_LINK_V2: "lawyer_link_v2",
  // 律师卡片
  LAWYER_INFO: "lawyer_info",
  // 律师卡片
  LAWYER_INFO_V2: "lawyer_info_V2",
  // 律师支付卡片
  LAWYER_PAY: "lawyer_pay",
  // 免费案源转单
  TRANSFORM_ORDER: "transfer_order",
  // 律师评价
  LAWYER_RATE: "lawyer_rate",
  // 律师服务状态
  SERVER_STATE_CARD: "style_card_invite",
  // 免费转急速
  FREE_TO_RAPID: "speed_order",
  // 混合咨询卡片
  CONSULTIN_GMIXED_CARD: "consultin_gmixed_card",
  // 混合咨询卡片
  CONSULTIN_GMIXED_CARD_V2: "consultin_gmixed_card_v2",
  // 案源结构卡片
  CONSULTIN_DESCRIBE_CARD: "consultin_describe_card",
  // 案源结构卡片
  CONSULTIN_DESCRIBE_FORM: "consultin_describe_FORM",
  CONSULTIN_DESCRIBE_FORM_V2: "consultin_describe_FORM_V2",
  // 匹配律师
  MATCHINGLAWYER: "matching_lawyer",
  // 匹配律师
  MATCHINGLAWYERV2: "matching_lawyer_v2",
  // 找律师
  CONSULTIN_FIND_LAWYER: "consultin_find_lawyer",
  CONTACT_INFO_USER: "contact_info_user",
  CONTACT_INFO_LAWYER: "contact_info_lawyer",
  STYLE_TXT_BG: "style_txt_bg",
  // 退款
  REFUND_TIP: "refund_tip",
  // 邀请评论
  INVITE_COMMENT: "invite_comment",
  // 邀请完成
  INVITE_COMPLETE: "invite_complete",
  // 律师自动回复付费卡片
  server_upgrade: "server_upgrade",
  // 自动回复
  LAWYER_AUTO_REPLY: "lawyer_auto_reply",
  /** 2.1.0 一对一服务 付费卡片 */
  SERVER_ONE_TO_ONE: "server_one_to_one",
  /** 2.2.0 im 支付成功 */
  SEVER_OF_PAY_SUCCESS: "sever_of_pay_success",
  /** 2.2.1 自己的追问卡片 */
  QUESTION_CARD: "server_QA_asked",
  SERVER_QA_FIRST_CHAT: "server_private_first_chat_qaMessege",
  /* 律师第一次发消息卡片*/
  ONE_ON_ONE_QA: "one-on-one_qa",
  /* 赞赏后提示话术*/
  SERVER_QA_ADDCOUNT: "server_QA_addCount",
  /* 律师已发起服务结束*/
  LAWYER_SERVICES_END: "lawyer_services_end",
  /* 服务实例*/
  SERVICE_INSTANCE: "service_instance",
  /** 协议书 */
  SERVER_LAWYER_IDEA: "server_lawyer_idea",
  /* 售后提示*/
  AFTER_SALE_TIP: "after_sale_tip",
  CARD_INTRODUCTORY_TEXT_NEW: "card_introductory_text_new",
  /* 合同模板*/
  SERVER_LAWYER_CONTRACT: "server_lawyer_contract",
  /* 私聊律师第一条回复*/
  SERVER_LAWYER_FIRST_REPLY: "server_private_first_chat",
  /**
   * 自定义回复消息
   * https://lanhuapp.com/web/#/item/project/product?tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&pid=15288297-293a-4995-a34c-4eb81610d13a&versionId=1141aef3-e1af-4946-84ef-cc64eb1f2195&docId=300ce858-bb44-4fc6-81c5-aa3c01b6bf57&docType=axure&pageId=4178193651364d31a6aa5368b9c4d1f0&image_id=300ce858-bb44-4fc6-81c5-aa3c01b6bf57&parentId=661f0bcc-e4f5-4aeb-85ab-a85f0b0bbcb9
   */
  IM_KEY_WORDS_SEND: "im_key_words_send",
  // 自定义 文本消息 案件委托
  LAWSUITCASE_CSBJ: "LAWSUITCASE_CSBJ",
  LAWSUITCASE_CSBJ_LAWYERINFO: "LAWSUITCASE_CSBJ_LAWYERINFO",
  // 指令消息
  LAWSUITCASE_GETPHONE: "LAWSUITCASE_GETPHONE",
  ...paralegalCard
};

/**
 * 判断是否是im中声明的消息类型
 */
export function isWebImSendType(item) {
  if(item.msgType !== WS_MESSAGE_TYPE.CUSTOM) return true;

  const type = item.msg?.customExts?.style;
  return Object.values(webImSendType).includes(type);
}


/* 发送状态*/
export const webImSendState = {
  loading: 1,
  success: 2,
  error: 3
};

