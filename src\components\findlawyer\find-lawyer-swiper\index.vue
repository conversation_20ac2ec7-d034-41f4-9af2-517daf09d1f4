<template>
  <div class="lawyer-box">
    <div class="lawyer-title flex flex-align-center flex-space-between">
      <img
        alt=""
        class="propaganda"
        src="../../../pages/submit-question/findlawyer/imgs/<EMAIL>"
      >
      <!--      <div><span class="text-theme-color">精选</span>·律师</div>
      <ul class="lawyer-subtitle flex flex-align-center">
        <li
          v-for="(item, index) in tipsList"
          :key="index"
          class="lawyer-subtitle-item flex flex-align-center"
        >
          <img
            alt=""
            class="lawyer-subtitle-item__icon"
            src="@/pages/submit-question/findlawyer/imgs/ok.png"
          >{{ item }}
        </li>
      </ul>-->
    </div>
    <div
      v-if="!isArrNull(bannerUrl)"
      class="lawyer-body"
    >
      <div class="swiper">
        <swiper
          :autoplay="true"
          :interval="3000"
          circular
          @change="change"
        >
          <swiper-item
            v-for="(item) in bannerUrl"
            :key="item.id"
          >
            <img
              :src="item.imageUrl"
              alt=""
              class="swiper-img"
              @click="jumpToLawyerHomepage(item.addressUrl)"
            >
          </swiper-item>
        </swiper>
        <div class="swiper-indicator flex flex-align-center flex-space-center">
          <div
            v-for="(item, index) in bannerUrl"
            :key="item.id"
            :class="{ 'swiper-indicator-item-active': index === currentIndex }"
            class="swiper-indicator-item"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { advertListPosition } from "@/api/lawyer.js";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath.js";
import {
  BURY_POINT_CHANNEL_TYPE,
  FROM_PAGE,
  globalTransformationPath,
  POINT_CODE,
} from "@/enum/burypoint.js";
import { buryPointChannelBasics } from "@/api/burypoint.js";
import { tr } from "@dcloudio/vue-cli-plugin-uni/packages/postcss/tags.js";
import { isArrNull } from "@/libs/basics-tools.js";

export default {
  name: "FindLawyerSwiper",
  data() {
    return {
      /** 展示图片 */
      bannerUrl: [],
      /** 当前所在滑块的 index */
      currentIndex: 0,
      tipsList: ["认证律师", "1万+律师入驻"],
    };
  },
  created() {
    this.getBannerData();
  },
  methods: {
    isArrNull,
    tr() {
      return tr;
    },
    /** 滑块切换时触发 */
    change(e) {
      this.currentIndex = e.detail.current;
    },
    /** 获取广告位数据 */
    getBannerData() {
      advertListPosition({ positionId: 145 }).then(({ data }) => {
        this.bannerUrl = data?.[0]?.advertContents || [];
      });
    },
    jumpToLawyerHomepage(addressUrl) {
      buryPointTransformationPath.addDataSources({
        fromPage: FROM_PAGE.ZLS_BANNER,
      });

      buryPointTransformationPath.new(
        globalTransformationPath.LAW_APPLET_FIND_LAWYER_TOP_BANNER_CLICK
      );

      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_FIND_LAWYER_TOP_BANNER_CLICK,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
      });

      uni.navigateTo({
        url: addressUrl,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.lawyer-box {
  //background-color: #fff;
  border-radius: 0 0 16px 16px;
}

.lawyer-title {
  /*font-size: 18px;
  font-weight: bold;
  color: #222222;
  padding: 8px 16px;*/
  .propaganda{
    width: 100%;
    height: 44px;
  }
}

.lawyer-subtitle {
  font-size: 10px;
  font-weight: 400;
  color: #999999;

  &-item {
    margin-right: 8px;

    &__icon {
      display: block;
      width: 12px;
      height: 12px;
      margin-right: 4px;
    }
  }
}

.lawyer-body {
  padding: 0 12px 0 12px;
}

.swiper {
  position: relative;
  height: 110px;
  border-radius: 12px;
  overflow: hidden;

  &-img {
    display: block;
    width: 351px;
    height: 110px;
    background: #ffffff;
    opacity: 1;
    border-radius: 12px;
  }

  &-indicator {
    position: absolute;
    bottom: 8px;
    width: 100%;
    z-index: 100;

    &-item {
      width: 12px;
      height: 3px;
      background: #ffffff;
      border-radius: 2px;
      opacity: 0.3;
      transition: width 0.3s;

      &:not(:last-child) {
        margin-right: 4px;
      }

      &-active {
        width: 20px;
        height: 3px;
        background: #ffffff;
        opacity: 1;
      }
    }
  }
}
</style>
