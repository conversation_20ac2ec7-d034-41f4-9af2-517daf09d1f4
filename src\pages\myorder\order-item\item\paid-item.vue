<template>
  <div class="order-item speed-order-item">
    <common-info :data="data" />
    <div :class="['order-bottom', 'flex', 'flex-align-center']">
      <div class="btns flex mt-[1px]">
        <!-- 退款中 -->
        <p
          v-if="refundBtnText"
          class="btn btn1 font14"
          @click.stop="toRefundDetail"
        >
          {{ refundBtnText }}
        </p>
        <p
          v-if="changeLawyerBtnText"
          class="btn btn1 font14"
          @click.stop="toChangeLawyerDetail"
        >
          {{ changeLawyerBtnText }}
        </p>
        <template v-if="!isServiceEndAfter">
          <!-- <p
            v-if="showRefundBtn"
            class="btn btn1 font14"
            @click.stop="toRefund"
          >
            退款
          </p>
          <p
            v-if="showChangeLawyerBtn"
            class="btn btn1 font14 btn--confirm"
            @click.stop="toChangeLawyer"
          >
            换律师
          </p> -->
        </template>
        <p
          class="btn btn1 font14"
          @click.stop="toApplyAfterSale"
        >
          订单详情
        </p>
        <p
          class="btn btn1 font14 btn--confirm"
          @click.stop="toWeChatCustomerService"
        >
          售后客服
        </p>
      </div>
    </div>
    <upgrade-platform-service-popup
      v-model="showRefundPopup"
      @cancel="clickPopupRight"
      @refund="clickPopupLeft"
    />
    <upgrade-one-by-one-service-popup
      v-model="show1V1RefundPopup"
      @refund="click1V1RefundPopupLeft"
      @upgrade="click1V1RefundPopupRight"
    />
    <app-modal
      :show="showChangeLawyerPopup"
      cancelText="我要换律师"
      confirmText="取消"
      content="当前律师尚未接单，选择换律师服务，平台将派选优质在线律师尽快为您服务"
      title="换律师确认"
      @cancel="clickChangeLawyerPopupLeft"
      @confirm="clickChangeLawyerPopupRight"
    />

    <!--    打官司退款-->
    <refund-in-a-lawsuit
      v-model="showRefundInALawsuitPopup"
      :data="data"
    />
    <refund-in-a-lawsuit-contact-customer-service
      v-model="showContactCustomerServicePopup"
    />
  </div>
</template>

<script>
import CommonInfo from "../commoninfo/index.vue";
import {
  toApplyAfterSale,
  toChangeLawyer,
  toChangeLawyerDetail,
  toOrderDetailPage,
  toRefund,
  toRefundDetail
} from "@/libs/turnPages";
import { speedRefundType } from "@/libs/config";
import AppModal from "@/components/app-components/app-modal/index.vue";
import dayjs from "dayjs";
import orderTimeMixin from "@/pages/myorder/order-item/mixins/orderTimeMixin.js";
import UpgradePlatformServicePopup from "@/pages/myorder/components/UpgradePlatformServicePopup.vue";
import UpgradeOneByOneServicePopup from "@/pages/myorder/components/UpgradeOneByOneServicePopup.vue";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import { toWeChatCustomerService } from "@/libs/tools";
import RefundInALawsuit from "@/pages/myorder/components/RefundInALawsuit.vue";
import RefundInALawsuitContactCustomerService
  from "@/pages/myorder/components/RefundInALawsuitContactCustomerService.vue";

/**
 a.律师咨询（付费卡片购买的）

 b.案件委托
 c.快速咨询（9.9元的拦截优惠价格）
 */
/** 未支付 */
export default {
  name: "PaidItem",
  components: {
    RefundInALawsuitContactCustomerService,
    RefundInALawsuit,
    UpgradeOneByOneServicePopup,
    UpgradePlatformServicePopup,
    AppModal,
    CommonInfo
  },
  mixins: [orderTimeMixin],
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      speedRefundType,
      // canRefund: false, // 是否有留资
      /** 是否显示退款拦截弹窗 */
      showRefundPopup: false,
      /** 换律师弹窗 */
      showChangeLawyerPopup: false,
      /** 1V1退款弹窗 */
      show1V1RefundPopup: false,
      //   打官司退款
      showRefundInALawsuitPopup: false,
      //   打官司退款 联系客服
      showContactCustomerServicePopup: false
    };
  },
  computed: {
    // 是不是案件委托定金
    isItACaseEntrustmentDeposit(){
      return this.data.serviceClassifyCode && this.data.serviceClassifyCode === "suit_case_deposit";
    },

    // 是不是案件委托尾款
    isItACaseEntrustmentTail() {
      return this.data.serviceClassifyCode && this.data.serviceClassifyCode === "suit_case_final_payment";
    },

    /**
     * 是否是1V1下单未开始服务时
     * https://lanhuapp.com/web/#/item/project/product?pid=e002fa14-2676-4241-9d89-27a5df5ac902&image_id=f3687295-9552-47eb-893f-13af917148ba&tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=9a3f73e2-2cf3-4211-9aba-4bca4f8cbd25&docId=f3687295-9552-47eb-893f-13af917148ba&docType=axure&pageId=d85bfaa8f212496f982bc8be230547d9&parentId=c033d937-0a04-4712-a39d-afdda95bac3b
     */
    is1V1Order() {
      // 退款状态判断，当没有律师接单时服务code不存在
      return !this.data.serverStatus && this.is1V1OrderType;
    },
    /**
     * 是不是法律意见书
     * 如果 type 为9，则表示是法律意见书
     * @returns {boolean}
     */
    isSpeedOrder() {
      return Number(this.data.type) === 9;
    },
    /** 
     * 是否是AI畅聊
     * 如果 type 为 10，则表示是AI畅聊
     * @returns {boolean}
     */
    isAIOrder() {
      return Number(this.data.type) === 10;
    },
    /** 是否是平台咨询未开始服务时 */
    isPlatformOrder() {
      return !this.data.serverStatus && !this.is1V1OrderType;
    },
    /** 是否是服务已开始—服务结束24H */
    isServiceEnd() {
      return (
        this.data.serverStatus === 1 ||
        (this.data.serverStatus === 3 && !this.serviceEndTime)
      );
    },
    /** 是否是服务结束24H后 */
    isServiceEndAfter() {
      // 如果是一问多答，且有律师回复，不显示售后按钮
      // https://lanhuapp.com/web/#/item/project/product?tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&pid=ea4b3468-e01c-4c90-bc35-95a5faed3609&versionId=88afe959-0d96-4b51-b3aa-73ce9e454170&docId=46ac898e-8f08-4e46-b3ac-5103008f8dc6&docType=axure&pageId=2d41c9524b7f4594a4ea2cde649977f9&image_id=46ac898e-8f08-4e46-b3ac-5103008f8dc6&parentId=b2d694be-4011-4bf8-88d9-308621f2016c
      if (this.isOneQuestion && this.hasLawyerReply) return true;

      return this.data.serverStatus === 3 && this.serviceEndTime;
    },
    /** 服务完成时间距现在时间是否超过24小时 */
    serviceEndTime() {
      const time = dayjs(this.data.serverCompleteTime);

      return dayjs().diff(time, "minutes") >= this.orderTime;
    },
    /** 是否是1V1下单 */
    is1V1OrderType() {
      return this.data.createFrom === 1;
    },
    /** 是否是悬赏 */
    isOneQuestion() {
      return this.data.type === 6;
    },
    /** 是否有律师回复 */
    hasLawyerReply() {
      return !!this.data.lawyerId;
    },
    /** 换律师按钮文案 */
    changeLawyerBtnText() {
      // 如果有退款状态，则换律师详情按钮不显示
      if (this.data.refundStatus) return "";

      switch (Number(this.data.changeLawyerCheckStatus)) {
      case 0:
        return "换律师审核中";
      case 1:
        return "售后详情";
      case 2:
        return "申请失败";
      case 9:
        return "售后详情";
      default:
        return "";
      }
    },
    /** 退款按钮文案 */
    refundBtnText() {
      if (this.data.refundStatus === speedRefundType.REFUNDING)
        return "退款审核中";

      if (this.data.refundStatus === speedRefundType.REFUNDFAILED)
        return "退款失败";

      return "";
    },
    /** 
     * 是否显示退款按钮
     */
    showRefundBtn() {
      if (this.isSpeedOrder || this.isAIOrder || this.isItACaseEntrustmentDeposit || this.isItACaseEntrustmentTail) {
        return !!(this.data.refundable && !this.data.refundId);
      }

      return (
        (this.is1V1Order || this.isPlatformOrder) &&
        !this.isServiceEnd &&
        this.data.refundable
      );
    },
    /** 是否显示换律师按钮 */
    showChangeLawyerBtn() {
      return this.is1V1Order && !this.isServiceEnd && this.data.canChangeLawyer;
    },
    /** 是否显示申请售后按钮 */
    showApplyAfterSaleBtn() {
      // 如果换律师审核中和退款审核中，不显示申请售后按钮，如果已经发起退款也不能再次申请售后
      if (
        this.data.changeLawyerCheckStatus === 0 ||
        this.data.refundStatus === speedRefundType.REFUNDING ||
        !!this.data.refundId
      )
        return false;

      return this.isServiceEnd;
    }
  },
  methods: {
    /** 
     * 去联系客服
     * https://lanhuapp.com/web/#/item/project/product?pid=9e589b80-b5b4-4bb8-8fbb-d8deb8d4ebc3&image_id=8b9a8cc9-8b78-4696-ad01-eebe92242e24&tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=cc5772ac-67c0-4455-b399-a7877d9c4883&docId=8b9a8cc9-8b78-4696-ad01-eebe92242e24&docType=axure&pageId=10a3323adf6d48fda3febdedc656cd89&parentId=d56984bd-39b1-4732-8766-7b6747568fb0
     */
    toWeChatCustomerService() {
      toWeChatCustomerService();
    },
    // 去退款详情
    toRefundDetail() {
      toRefundDetail({
        orderId: this.data.id
      });
    },
    /** 去换律师详情 */
    toChangeLawyerDetail() {
      toChangeLawyerDetail({
        orderId: this.data.id
      });
    },
    toRefund() {
      buryPointChannelBasics({
        code: "LAW_APPLET_MINE_PAGE_ORDER_RECORD_REFUND_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });
      

      // 打官司退款
      if(this.isItACaseEntrustmentDeposit){
        // 已经支付了尾款 或者委托已关闭 已结案 就不能退款
        if(this.data.suitCaseStatus === 4 || this.data.suitCaseStatus === 5 || this.data.suitCaseStatus === 6){
          this.showContactCustomerServicePopup = true;
          return;
        }
        this.showRefundInALawsuitPopup = true;
        return;
      }
      if(this.isItACaseEntrustmentTail){
        this.showContactCustomerServicePopup = true;
        return;
      }

      toRefund({
        orderId: this.data.id
      });
    },
    /** 换律师 */
    toChangeLawyer() {
      this.showChangeLawyerPopup = true;
    },
    /** 申请售后 */
    toApplyAfterSale() {
      toOrderDetailPage({
        id: this.data.id
      });
    },
    /** 点击弹窗左侧按钮 */
    clickPopupLeft() {
      buryPointChannelBasics({
        code:
          "LAW_APPLET_COMMON_CONSULT_DID_NOT_RECEIVE_ORDER_REFUND_POPUP_PAGE_REFUND_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      this.showRefundPopup = false;

      toRefund({
        orderId: this.data.id
      });
    },
    /** 点击弹窗右侧按钮 */
    clickPopupRight() {
      buryPointChannelBasics({
        code:
          "LAW_APPLET_COMMON_CONSULT_DID_NOT_RECEIVE_ORDER_REFUND_POPUP_PAGE_WAIT_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      this.showRefundPopup = false;
    },
    /** 换律师左侧按钮 */
    clickChangeLawyerPopupLeft() {
      this.showChangeLawyerPopup = false;

      toChangeLawyer({
        orderId: this.data.id
      });
    },
    /** 换律师右侧按钮 */
    clickChangeLawyerPopupRight() {
      this.showChangeLawyerPopup = false;
    },
    /** 1v1退款左侧按钮 */
    click1V1RefundPopupLeft() {
      buryPointChannelBasics({
        code:
          "LAW_APPLET_1V1_DID_NOT_RECEIVE_ORDER_REFUND_POPUP_PAGE_REFUND_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      this.show1V1RefundPopup = false;

      toRefund({
        orderId: this.data.id
      });
    },
    /** 1v1退款右侧按钮 */
    click1V1RefundPopupRight() {
      buryPointChannelBasics({
        code:
          "LAW_APPLET_1V1_DID_NOT_RECEIVE_ORDER_REFUND_POPUP_PAGE_UPGRADE_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      this.show1V1RefundPopup = false;

      toChangeLawyer({
        orderId: this.data.id
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./order-item.scss";
</style>
