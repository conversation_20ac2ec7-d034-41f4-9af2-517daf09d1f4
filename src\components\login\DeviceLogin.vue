<template>
  <app-popup
    :show="show"
    :zIndex="99999"
    mode="bottom"
  >
    <div class="p-[24px_24px_16px_24px]">
      <div class="flex items-center justify-between">
        <img
          alt=""
          class="w-[186px] h-[36px] block"
          src="@/components/login/imgs/x9RSh3.png"
        >
        <img
          alt=""
          class="w-[24px] h-[24px] block"
          src="@/components/login/imgs/pO9XJ6.png"
          @click="handleClose"
        >
      </div>
      <div
        class="font-bold text-[16px] text-[#333333] pb-[12px] border-0 border-b-[1px] border-solid border-[#EEEEEE] mt-[24px]"
      >
        使用您的手机号码
      </div>
      <div
        class="mt-[10px] border-0 border-b-[1px] border-solid border-[#EEEEEE]"
      >
        <div class="font-bold text-[16px] text-[#333333]">
          {{ getDeviceInfo.phone }}
        </div>
        <div class="text-[12px] text-[#999999] mt-[2px] mb-[10px]">
          绑定的手机号码
        </div>
      </div>
      <div class="flex items-center justify-end">
        <div
          class="text-[14px] text-[#3887F5] mt-[16px]"
          @click="otherBtnClick"
        >
          使用其他手机号授权
        </div>
      </div>
      <div
        class="mt-[24px] w-[327px] h-[44px] bg-[#3887F5] rounded-[68px] box-border font-bold text-[16px] text-[#FFFFFF] flex items-center justify-center"
        @click="btnHandleClick"
      >
        同意授权
      </div>
    </div>
    <u-safe-bottom />
  </app-popup>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { whetherToLogIn } from "@/libs/tools";
import { deviceLogin } from "@/libs/login";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";

export default {
  name: "DeviceLogin",
  components: { USafeBottom, AppPopup },
  computed: {
    show: {
      get() {
        return this.$store.getters["popup-state/getDeviceLoginPopupState"];
      },
      set(val) {
        this.$store.commit("popup-state/SET_DEVICE_LOGIN_POPUP_STATE", val);
      }
    },
    getDeviceInfo() {
      return this.$store.getters["user/getDeviceInfo"];
    }
  },
  watch: {
    show: {
      handler(val) {
        if (val) {
          buryPointChannelBasics({
            code: "LAW_APPLET_ASK_LAWYER_AUTH_POP_UP_PAGE",
            type: 1,
            behavior: BURY_POINT_CHANNEL_TYPE.VI
          });
        }
      },
      immediate: true
    }
  },
  beforeDestroy() {
    this.show = false;
  },
  methods: {
    handleClose() {
      this.show = false;
      deviceLogin({
        showToast: false
      });
    },
    btnHandleClick() {
      buryPointChannelBasics({
        code: "LAW_APPLET_ASK_LAWYER_AUTH_POP_UP_PAGE_CONFIRM_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });
      this.show = false;
      deviceLogin().then(() => {
        buryPointChannelBasics({
          code: "LAW_APPLET_ASK_LAWYER_AUTH_SUCCESS_TOAST_PAGE",
          type: 1,
          behavior: BURY_POINT_CHANNEL_TYPE.VI
        });
      });
    },
    otherBtnClick() {
      buryPointChannelBasics({
        code: "LAW_APPLET_ASK_LAWYER_AUTH_POP_UP_PAGE_OTHER_NUMBER_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI
      });
      this.show = false;
      whetherToLogIn(this.$store.getters["user/getLoginCallback"]);
    }
  }
};
</script>
