<template>
  <app-popup
    :closeOnClickOverlay="closeOnClickOverlay"
    :lowCancelButton="lowCancelButton"
    :overlay="overlay"
    :round="round"
    :show="show"
    :showCancelButton="showCancelButton"
    :zIndex="zIndex"
    mode="center"
    @cancel="$emit('cancelIcon')"
  >
    <div
      :style="{ borderRadius: `${round}px` }"
      class="modal-container"
    >
      <div class="modal-content">
        <slot>
          <div class="modal-title">
            {{ title }}
          </div>
          <div
            :style="[
              {
                textAlign: align
              }
            ]"
            class="modal-text"
          >
            {{ content }}
          </div>
        </slot>
      </div>
      <div class="modal-button">
        <div
          class="modal-button-left flex flex-vertically-centered"
          @click="handleCancel"
        >
          {{ cancelText }}
        </div>
        <div
          class="modal-button-right flex flex-vertically-centered"
          @click="handleConfirm"
        >
          {{ confirmText }}
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import appPopupProps from "@/components/app-components/app-popup/appPopupProps.js";

export default {
  name: "AppModal",
  components: { AppPopup },
  mixins: [appPopupProps],
  props: {
    cancelText: {
      type: String,
      default: "取消"
    },
    confirmText: {
      type: String,
      default: "确定"
    },
    title: {
      type: String,
      default: ""
    },
    content: {
      type: String,
      default: ""
    },
    /** 内容对齐方式 */
    align: {
      type: String,
      default: "left",
      validator: value => ["left", "center", "right"].includes(value)
    }
  },
  methods: {
    handleCancel() {
      this.$emit("cancel");
    },
    handleConfirm() {
      this.$emit("confirm");
    }
  }
};
</script>

<style lang="scss" scoped>
.modal-container {
  background-color: #fff;
}

.modal-content {
  padding: 24px;

  .modal-title {
    font-size: 16px;
    font-weight: bold;
    color: #333333;
    text-align: center;
  }

  .modal-text {
    padding-top: 12px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 24px;
  }
}

.modal-button {
  border-top: 0.5px solid #eee;
  width: 100%;
  height: 46px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  font-size: 16px;
  font-weight: 400;
  place-items: center;

  &-left {
    color: #999999;
    width: 100%;
    height: 100%;
    border-right: 0.5px solid #eee;
  }

  &-right {
    color: #3887f5;
    width: 100%;
    height: 100%;
  }
}
</style>
