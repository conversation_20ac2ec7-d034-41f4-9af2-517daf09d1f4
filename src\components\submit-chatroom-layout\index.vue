<template>
  <div
    :class="'im-chatroom-layout-' + theme"
    class="im-chatroom-layout"
    @click="$emit('click')"
  >
    <slot />
  </div>
</template>

<script>
// (value) => ['blue', 'white'].indexOf(value) > -1
export default {
  name: "SubmitChatroomLayout",
  props: {
    theme: {
      type: String,
      default: "",
    },
  },
};
</script>

<style lang="scss" scoped>
.have-avatar {
  .im-chatroom-layout  {
    max-width: 255px;
  }
}

.im-chatroom-layout {
  max-width: 255px;
  padding: 10px;
  border-radius: 4px;
  box-sizing: border-box;

  &.im-chatroom-layout-blue {
    background: #4a549e;

    p {
      color: #ffffff;
    }
  }

  &.im-chatroom-layout-white {
    background: #ffffff;
    border-radius: 0 4px 4px 4px;

    p {
      color: #333333;
    }
  }

  // 2.1.6 改成了淡蓝色，但是为了兼容，该类名没变
  &.im-chatroom-layout-yellow {
    //// #ifdef MP-HJLS
    background: #E5F3EE;
    // #endif
    // #ifndef MP-HJLS
    background: #E1EAFA;
    // #endif
    border-radius: 8px 0 8px 8px;

    p {
      color: #333333;
    }
  }

  ::v-deep {
    p {
      word-wrap: break-word;
    }
  }
}
</style>
