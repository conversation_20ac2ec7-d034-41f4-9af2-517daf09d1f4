<template>
  <login-layout>
    <div class="pb-[115px]">
      <img
        class="block w-full h-[112px]"
        src="@/pages/submit-question/caculator/imgs/<EMAIL>"
        alt=""
      >
      <div class="px-[16px] mt-[-26px]">
        <div class="bg-[#FFFFFF] scroll-contaier rounded-[8px] px-[16px]">
          <caculator-item-form
            ref="caculatorItemForm"
            :formData="formData"
            :rules="formRules"
          >
            <div
              v-if="!criminalFormShow"
              class="border-[0px] py-[22px] border-b-[1px] border-solid border-[#EEEEEE] last:border-b-0"
            >
              <caculator-item
                title="收费方式"
                question
                prop="chargeType"
                layout="column"
                @question="handleProtocolPopupShow"
              >
                <caculator-item-check-block
                  v-model="formData.chargeType"
                  :list="chargeTypeList"
                  placeholder="请选择"
                  @change="handleChange($event, 'chargeType')"
                />
              </caculator-item>
            </div>
          
            <div
              v-if="!riskFormShow"
              class="py-[22px] border-[0px] border-b-[1px] border-solid border-[#EEEEEE] last:border-b-0"
            >
              <caculator-item
                title="案件类型"
                prop="caseType"
                layout="column"
              >
                <caculator-item-check-block
                  v-model="formData.caseType"
                  :list="caseTypeList"
                  placeholder="请选择"
                  @change="handleChange($event, 'caseType')"
                />
              </caculator-item>
            </div>

            <div
              v-if="!riskFormShow"
              class="h-[68px] border-[0px] border-b-[1px] border-solid border-[#EEEEEE] last:border-b-0"
            >
              <caculator-item
                title="所在地区"
                prop="regionCode"
              >
                <caculator-item-popup-location
                  placeholder="请选择"
                  :value="formData.regionCode"
                  @setCity="setCity"
                />
              </caculator-item>
            </div>


            <div
              v-if="criminalFormShow&&formData.regionCode"
              class="py-[22px] border-[0px] border-b-[1px] border-solid border-[#EEEEEE] last:border-b-0"
            >
              <caculator-item
                title="案件阶段"
                prop="caseStage"
                layout="column"
              >
                <caculator-item-check-block
                  v-model="formData.caseStage"
                  :list="caseStageList"
                  placeholder="请选择"
                  @change="handleChange($event, 'caseStage')"
                />
              </caculator-item>
            </div>
          
            <div
              v-if="!criminalFormShow"
              class="h-[68px] border-[0px] border-b-[1px] border-solid border-[#EEEEEE] last:border-b-0"
            >
              <caculator-item
                title="标的金额"
                prop="amount"
              >
                <caculator-item-input
                  v-model="formData.amount"
                  unit="元"
                  type="digit"
                  maxlength="9"
                  placeholder="请输入"
                />
              </caculator-item>
            </div>
          </caculator-item-form>
        </div>
        <div class="mt-[12px] bg-[linear-gradient(_180deg,_#DAE8FF_0%,_#F0F8FF_50%,_#FFFFFF_100%)] rounded-[8px]">
          <div class="h-[134px] position-relative">
            <img
              class="background-image"
              src="@/pages/submit-question/caculator/imgs/<EMAIL>"
              alt=""
            >
            <p class="text-[16px] text-[#333333] flex items-center justify-center pt-[24px] pb-[4px]">
              预估律师费用
            </p>
            <div class="font-[600] text-[20px] text-[#3887F5] flex items-center justify-center pt-[4px]">
              <div
                v-if="showCalculateResult&&riskFormShow"
                class="font-[600] text-[12px] flex flex-col text-[#3887F5] pr-[4px]"
              >
                <p>不</p>
                <p>超</p>
                <p>过</p>
              </div>
              <div class="flex justify-center leading-[1] items-baseline">
                <span class="text-[16px]">¥</span> {{ showCalculateResult? calculateResult : 0 }}
              </div>
            </div>
          </div>
          <div
            v-if="showCalculateResult"
            class=" pb-[24px]"
          >
            <div class="flex items-center justify-center">
              <img
                class="w-[58px] h-[9px]"
                src="@/pages/submit-question/caculator/imgs/<EMAIL>"
                alt=""
              >
              <p class="px-[6px] text-[14px] text-[#666666]">
                {{ riskFormShow?labels.chargeType:labels.caseType }}收费标准
              </p>
              <img
                class="w-[58px] h-[9px]"
                src="@/pages/submit-question/caculator/imgs/<EMAIL>"
                alt=""
              >
            </div>
            <div class="pt-[12px] px-[16px] text-[12px] text-[#666666] whitespace-pre-wrap">
              <p
                v-for="(i,index) in rulesText"
                :key="index"
                class="pt-[4px]"
              >
                {{ i }}
              </p>
            </div>
            <div class="pt-[8px] px-[16px] text-[12px] text-[#999999]">
              注：此计算结果仅供参考，实际收费标准可能因地区、案件复杂程度等因素而异。建议咨询当地律师事务所获取准确报价。
            </div>
          </div>
        </div>
        <div class="flex items-center justify-center pt-[14px]">
          <caculator-item-check
            v-model="protocolChecked"
            :checkIcon="icons.check"
          >
            <p class="text-[12px] text-[#CCCCCC]">
              使用服务即表示同意律师提供免费法律指导
            </p>
          </caculator-item-check>
        </div>
        <u-safe-bottom />
      </div>

      <div class="fixed left-0 bottom-0 bg-[#FFFFFF]">
        <div class="w-[375px] h-[75px] flex justify-between items-center px-[16px] pt-[23px] pb-[8px] box-border">
          <div
            class="w-[144px] h-[44px] bg-[#EEEEEE] rounded-[12px] box-border flex items-center justify-center"
            @click="resetForm"
          >
            <div class="font-[500] text-[16px] text-[#666666]">
              重置
            </div>
          </div>
          <div class="w-[186px] h-[44px] ml-[13px]">
            <caculator-item-button
              :clickInterval="3000"
              @handleClick="handleCalculate"
            >
              计算
            </caculator-item-button>
          </div>
        </div>
        <u-safe-bottom />
      </div>
      <caculator-civil-popup
        v-model="payPopupShow"
        buriedType="attorneysFees"
      />
      <protocol-pop-up
        v-model="protocolPopupShow"
        height="210"
        protocolTitle="收费方式说明"
      >
        <div class="pt-[12px]">
          <p class="text-[14px] text-[#666666] leading-[20px]">
            <span class="font-[500] text-[#333333]">风险代理收费：</span> 指委托人先支付少量或不支付基础费用，由律师承担部分风险，待案件达到约定目标（如胜诉或实现债权）后，再按约定比例或金额支付律师费的方式。
          </p>
          <p class="text-[14px] pt-[24px] text-[#666666] leading-[20px]">
            <span class="font-[500] text-[#333333]">按标的额收费：</span> 指律师根据案件涉及的财产金额（即标的额）按一定比例收取服务费用的方式。这种收费方式通常适用于涉及财产关系的案件，比如合同纠纷、债权债务等。
          </p>
        </div>
      </protocol-pop-up>
    </div>
    <scroll-monitor
      targetClass="scroll-contaier"
      :offset="-8"
      @reach-top="handleReachTop"
      @leave-top="handleLeaveTop"
    />
  </login-layout>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import CaculatorItem from "@/pages/submit-question/components/caculator-item/index.vue";
import CaculatorItemInput from "@/pages/submit-question/components/caculator-item/input.vue";
import CaculatorItemCheck from "@/pages/submit-question/components/caculator-item/check.vue";
import CaculatorCivilPopup from "@/pages/submit-question/caculator/civil/popup.vue";
import ProtocolPopUp from "@/pages/submit-question/components/protocolPopUp/index.vue";
import { lawyerFeeCalculatorListData } from "@/api";
import CaculatorItemForm from "@/pages/submit-question/components/caculator-item/form.vue";
import { calculateLawyerFee, formatAmount, setNavigationBarTitle, whetherToLogIn } from "@/libs/tools.js";
import { isNull } from "@/libs/basics-tools";
import { saveCaseSource } from "@/pages/submit-question/caculator";
import CaculatorItemPopupLocation from "@/pages/submit-question/components/caculator-item/popup-location.vue";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import LoginLayout from "@/components/login/login-layout.vue";
import CaculatorItemButton from "@/pages/submit-question/components/caculator-item/button.vue";
import ScrollMonitor from "@/pages/submit-question/components/scroll-monitor/index.vue";
import timeConfig from "@/libs/timeConfig";
import CaculatorItemCheckBlock from "@/pages/submit-question/components/caculator-item/check-block.vue";
import { setNavigationBarAlipay } from "@/libs/tools";

const icons = {
  check: require("@/pages/submit-question/caculator/imgs/icon@2x_1.png")
};
export default {
  name: "CaculatorCivil",
  components: {
    CaculatorItemCheckBlock,
    ScrollMonitor,
    CaculatorItemButton,
    LoginLayout,
    CaculatorItemPopupLocation,
    USafeBottom,
    CaculatorItem,
    CaculatorItemInput,
    CaculatorItemCheck,
    CaculatorCivilPopup,
    ProtocolPopUp,
    CaculatorItemForm
  },
  data() {
    return {
      icons,
      // 协议勾选状态
      protocolChecked: true,
      // 支付弹窗状态
      payPopupShow: false,
      // 协议弹窗状态
      protocolPopupShow: false,
      // 收费方式list
      chargeTypeList: [{
        label: "按标的额比例收费",
        value: "1"
      }, {
        label: "风险代理收费",
        value: "2"
      }],
      // 案件类型list
      caseTypeList: [{
        label: "民事案件",
        value: "1"
      }, {
        label: "刑事案件",
        value: "2"
      }],
      // 案件阶段list
      caseStageList: [],
      // 表单数据
      formData: {
        // 收费方式
        chargeType: "",
        // 案件类型
        caseType: "",
        // 所在地区
        regionCode: "",
        // 标的金额
        amount: "",
        // 案件阶段
        caseStage: "",
        //   省份
        provinceCode: ""
      },
      labels: {
        chargeType: "",
        caseType: "",
        happenAddress: "",
        caseStage: "",
        amount: ""
      },
      // 表单规则 formData都是必填
      formRules: {
        chargeType: [{
          required: true,
          message: "请选择收费方式",
          trigger: "blur"
        }],
        caseType: [{
          required: true,
          message: "请选择案件类型",
          trigger: "blur"
        }],
        regionCode: [{
          required: true,
          message: "请选择所在地区",
          trigger: "blur"
        }],
        caseStage: [{
          required: true,
          message: "请选择案件阶段",
          trigger: "blur"
        }],
        amount: [{
          required: true,
          message: "请输入标的金额",
          trigger: "blur"
        }, {
          // 整数
          type: "integer",
          message: "标的金额请输入整数",
          trigger: "blur"
        }]
      },
      //   规制文案
      rulesText: [],
      //   计算结果
      calculateResult: null
    };
  },
  computed: {
    // 民事选择from的显示
    civilFormShow() {
      return this.formData.chargeType === "1" && this.formData.caseType === "1";
    },
    // 风险收费from的显示
    riskFormShow() {
      return this.formData.chargeType === "2";
    },
    // 刑事案件from的显示
    criminalFormShow() {
      return this.formData.chargeType === "1" && this.formData.caseType === "2";
    },
    // 是否展示计算结果
    showCalculateResult() {
      return !isNull(this.calculateResult);
    }
  },
  mounted() {
    setNavigationBarAlipay({
      frontColor: "#000000",
      backgroundColor: "#def2ff",
    });

    buryPointChannelBasics({
      code: "LAW_APPLET_ATTORNEYS_FEES_CALCULATOR_PAGE",
      type: 1,
      behavior: BURY_POINT_CHANNEL_TYPE.CK
    });
  },
  methods: {
    handleProtocolPopupShow() {
      this.protocolPopupShow = true;
    },
    // 选择事件
    handleChange(event, key) {
      this.labels[key] = event.label;
      if (key === "caseType") {
        // 如果是刑事案件 并且选择了城市  就调用配置 实时获取案件阶段
        this.getCriminalLawyerFeeCalculatorListData();
      }
      //   如果是请选择收费方式 就清空一次城市
      if (key === "chargeType") {
        this.formData.regionCode = "";
        this.formData.provinceCode = "";
        this.formData.caseType = "";
        this.labels.happenAddress = "";
      }
    },
    // 城市选择
    setCity({ city, label }) {
      this.formData.regionCode = String(city.code);
      this.formData.provinceCode = city.province;
      this.labels.happenAddress = label;
      // 如果是刑事案件 并且选择了城市  就调用配置 实时获取案件阶段
      this.getCriminalLawyerFeeCalculatorListData();
    },
    resetForm() {
      const data = this.$options.data();
      this.formData = data.formData;
      this.labels = data.labels;
      this.calculateResult = data.calculateResult;

      buryPointChannelBasics({
        code: "LAW_APPLET_ATTORNEYS_FEES_CALCULATOR_PAGE_RESET_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });
    },
    // 刑事案件的计算器列表数据
    getCriminalLawyerFeeCalculatorListData() {
      console.log(this.criminalFormShow, "this.criminalFormShow");
      if (this.criminalFormShow && this.formData.regionCode) {
        this.formData.caseStage = "";
        return this.getLawyerFeeCalculatorListData().then(res => {
          this.caseStageList = Object.freeze((res || []).map((item) => ({
            ...item,
            label: ((item.labelName || "").replace(/：+/g, ":").trim().split(":")[0] || ""),
            value: String(item.id)
          })));
        });
      }
    },
    // 获取律师费计算器列表数据
    getLawyerFeeCalculatorListData() {
      return lawyerFeeCalculatorListData(this.formData).then(res => {
        return res.data;
      });
    },
    // 计算
    handleCalculate() {

      buryPointChannelBasics({
        code: "LAW_APPLET_ATTORNEYS_FEES_CALCULATOR_PAGE_CALCULAT_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });
      whetherToLogIn(() => {
        if (!this.protocolChecked) {
          uni.showToast({
            title: "请先同意律师提供免费法律指导",
            icon: "none"
          });
          return;
        }
        this.$refs.caculatorItemForm.validate().then(() => {
          let info = "";
          // 民事案件 保存的信息 你好，我想咨询一下律师费，民事案件，标的额5000元，我在北京
          if (this.civilFormShow) {
            info = `你好，我想咨询一下律师费，${this.labels.caseType}，标的额${this.formData.amount}元，我在${this.labels.happenAddress}`;
          }
          // 刑事案件 保存的信息 你好，我想咨询一下律师费，刑事案件，现在处于一审阶段，我在北京
          if (this.criminalFormShow) {
            info = `你好，我想咨询一下律师费，${this.labels.caseType}，现在处于${this.labels.caseStage}，我在${this.labels.happenAddress}`;
          }
          // 风险代理收费 保存的信息 你好，我想咨询一下律师费，标的额XXXX元
          if (this.riskFormShow) {
            info = `你好，我想咨询一下律师费，标的额${this.formData.amount}元`;
          }
          this.calculateLawyerFee();
          // this.saveCaseSource(info);
        });
      });
    },
    // 保存案源
    async saveCaseSource(info) {
      saveCaseSource({
        info,
        label: "综合咨询",
        value: "26",
        data: (this.riskFormShow ? {} : {
          happenAddress: this.labels.happenAddress,
          regionCode: this.formData.regionCode
        })
      }).then(() => {
        timeConfig.toolDelay().then(time => {
          // 2s后 弹出支付弹窗
          setTimeout(() => {
            this.payPopupShow = true;
          }, time);
        });
      });
    },
    // 计算律师费
    calculateLawyerFee() {
      // 获取规则
      this.getLawyerFeeCalculatorListData().then(res => {
        // 规则.
        const rules = res || [];
        this.rulesText = rules.map((item, index) => `${index + 1}.${item.labelName}`);
        const calculateResult = calculateLawyerFee({
          amount: this.formData.amount,
          rules,
          ...(this.criminalFormShow ? { id: this.formData.caseStage } : {})
        });
        this.calculateResult = calculateResult.map(item => formatAmount(item)).join("~");
      });
    },
    handleReachTop() {
      setNavigationBarTitle({
        title: "计算器",
        backgroundColor: "#FFFFFF"
      });
    },
    handleLeaveTop() {
      setNavigationBarTitle({
        backgroundColor: "#DEF2FF"
      });
    },
  },

  // 必须在页面中添加 onPageScroll 处理函数
  onPageScroll(e) {
    // 触发全局事件，让ScrollMonitor组件能够接收到
    uni.$emit("onPageScroll", e.scrollTop);
  }
};
</script>
<style></style>