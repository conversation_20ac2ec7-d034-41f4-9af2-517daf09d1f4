<template>
  <div
    :class="{'button-border':border}"
    class="button-container"
  >
    <button
      v-if="needLogin&&!isLogin"
      :hover-stop-propagation="true"
      class="icon-login"
      type="default"
    >
      <slot />
    </button>
    <button
      v-else
      @click="handleClick"
    >
      <slot />
    </button>
  </div>
</template>

<script>
import { getUserTokenStorage } from "@/libs/token";
export default {
  name: "Index",
  props: {
    needLogin: {
      type: Boolean,
      default: false
    },
    /* 登录成功回调*/
    handleLoginSuccessCallback: {
      type: [undefined, Function],
      default: undefined
    },
    border: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    /* 是否登录*/
    isLogin() {
      return this.$store.getters["user/getToken"] && getUserTokenStorage();
    }
  },
  methods: {
    handleClick(){
      this.$emit("click");
    },
    /* 登录成功回调*/
    loginSuccess(){
      this.handleClick();
      this.$emit("loginSuccess");
    }
  }
};
</script>

<style lang="scss" scoped>
.button-container{
  height: 100%;
  width: 100%;
}
button{
  border: none;
  background: #F2AF30;
  border-radius: 22px;
  overflow: hidden;
  height: 100%;
  width: 100%;
  &:after{
    content: none;
  }
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  color: #FFFFFF;
}
.button-border{
  button{
    background: none;
    border-radius: 16px;
    border: 1px solid #F2AF30;
    color: #F2AF30;
  }
}
</style>
