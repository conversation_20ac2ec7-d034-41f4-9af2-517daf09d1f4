<template>
  <div>
    <!-- 返回拦截弹窗 -->
    <app-popup
      :closeOnClickOverlay="false"
      :safeAreaInsetBottom="true"
      :show="show"
      :zIndex="99999"
      lowCancelButton
      mode="center"
      @cancel="close"
    >
      <div
        class="position-relative p-[24px_26px_0_26px] w-[311px] h-[312px] box-border"
      >
        <img
          alt=""
          class="background-image"
          src="@/components/login/imgs/<EMAIL>"
        >
        <div class="font-bold text-[20px] text-[#333333] text-center">
          恭喜您，获得新人注册好礼
        </div>
        <div class="text-[15px] text-[#999999] mt-[6px] text-center">
          <span class="text-[#EB4738]">20元优惠劵</span>问律师直接抵扣
        </div>
        <img
          alt=""
          class="w-[260px] h-[83px] mt-[20px]"
          src="@/components/login/imgs/Group8222.png"
        >
        <div
          class="text-[12px] text-[#F34747] mt-[36px] flex items-center justify-center"
        >
          <div>优惠倒计时</div>
          <div
            class="flex items-center ml-[6px] font-bold text-[12px] text-[#F34747]"
          >
            <div
              class="w-[20px] h-[20px] bg-[#FFFFFF] rounded-[4px] border-[1px] border-solid border-[#CCCCCC] flex items-center justify-center"
            >
              {{ minutes }}
            </div>
            <div class="mx-[4px]">
              :
            </div>
            <div
              class="w-[20px] h-[20px] bg-[#FFFFFF] rounded-[4px] border-[1px] border-solid border-[#CCCCCC] flex items-center justify-center"
            >
              {{ seconds }}
            </div>
          </div>
        </div>
        <img
          alt=""
          class="w-[262px] mt-[10px]"
          mode="widthFix"
          src="@/components/login/imgs/button1.png"
          @click="toAskLawyer"
        >
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import countDownMixin from "@/pages/rapid-consultation-confirm-order/my-consultation/js/countDownMixin";
import { toAskLawyer } from "@/libs/turnPages";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";

export default {
  name: "LoginAwardPopup",
  components: { AppPopup },
  mixins: [countDownMixin],
  props: {
    value: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    /** 弹窗显示 */
    show: {
      get() {
        return this.$store.getters["popup-state/getCouponPopupState"];
      },
      set(val) {
        this.$store.commit("popup-state/SET_COUPON_POPUP_STATE", val);
      }
    }
  },
  watch: {
    show: {
      handler(val) {
        if (val) {
          buryPointChannelBasics({
            code: "LAW_APPLET_NEW_REGISTER_POP_UP_PAGE",
            type: 1,
            behavior: BURY_POINT_CHANNEL_TYPE.CK
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    close() {
      this.show = false;
    },
    toAskLawyer() {
      buryPointChannelBasics({
        code: "LAW_APPLET_NEW_REGISTER_POP_UP_PAGE_USE_NOW_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });
      toAskLawyer();
      this.close();
    }
  }
};
</script>
