import {
  requestCommon,
  requestCore,
  requestInfo,
  requestLawyer
} from "@/libs/axios";
import { toConfirmOrder, toImChatPage } from "@/libs/turnPages";
import { getParalegalDataId } from "@/libs/paralegalData";
import store from "@/store";

/* 法律问答详情获取咨询律师*/
export const asklawyerList = data =>
  requestCommon.post("/lawyer/recommend/getConsultLawyer", data);

/* 律师列表*/
export const lawyerList = data =>
  requestCommon.post("/lawyer/lawyer/lawyerList", data);

/* 律师详情*/
export const oneLawyer = data =>
  requestCommon.post("/lawyer/lawyer/oneLawyer", data);

/* 律师案例列表*/
export const lawyerLawCase = data =>
  requestCommon.post("/lawyer/lawyerLawCase/page", data);

/* 律师案例详情*/
export const lawyerLawCaseDetail = data =>
  requestCommon.post("/lawyer/lawyerLawCase/detail", data);

/* 案例详情*/
export const oneCase = data =>
  requestCommon.post("/lawyer/lawyer/oneCase", data);

/* 律师主页分页查询问答列表*/
export const getAnswerListByLowUserId = data =>
  requestInfo.post("/discuss/answer/answerListByLowUserId", data);

/* 查询律师的评价列表*/
export const getOrderDiscussPageByLawyerId = data =>
  requestLawyer.post("/orderDiscuss/pageByLawyerId", data);

/* 获取当前律师开启的服务项*/
export const getServiceItem = data =>
  requestLawyer.post("/server/getServiceItem", data);

/* 获取当前律师对应某个服务项的服务详情*/
export const getServiceDetail = data =>
  requestLawyer.post("/server/detail", data);

/* 创建业务订单*/
export const orderMakeOrder = data =>
  requestCommon.post("/order/makeOrder", data);

/* 支付*/
export const orderPay = data =>
  requestCommon.post("/order/v2/orderUser/pay", data);

/* 向微信主动查询订单状态*/
export const queryOrderStatus = data =>
  requestCommon.post(`order/queryOrderStatus/${data.businessOrderNo}`, data);

/* 快速咨询推荐律师列表信息 2.1.0*/
export const fastConsultRecommendLawyers = data =>
  requestInfo.post("/lawyer/fastConsult/recommendLawyers", data);

/** 根据位置获取广告(C端广告) */
export const advertListPosition = data =>
  requestCore.post("/advert/list/position", data);
/**
 * 获取当前用户与当前律师创建的相同服务的订单
 */
export const postGetExistOrderByCurrentUser = data =>
  requestCommon.post("/order/v2/orderUser/getExistOrderByCurrentUser", data);
/* 一对一案源保存*/
export const caseSourceV2SaveAppoint = data =>
  requestInfo.post("/caseSourceV2/saveAppoint", data, {
    loading: true
  });

/** 一对一案源保存通用 */
export const caseSourceV2SaveGeneral = params => {
  return new Promise(resolve => {
    caseSourceV2SaveAppoint(params).then(async ({ data }) => {
      const { errorCode, errorData } = data;

      const synHistoryId = getParalegalDataId();

      // 已经有付费且未完成一对一服务 需要提示并跳转到IM聊天窗口 返回格式见这个接口
      if (errorCode === 21101) {
        toImChatPage({
          conversationId: errorData?.imSessionId,
          lawyerId: errorData?.lawyerId,
          caseSourceId: errorData?.caseSourceV2Id
        });

        return;
      }

      // 已经有未付费一对一服务 跳转到订单列表付费
      if (errorCode === 21102) {
        uni.navigateTo({
          url: "/pages/myorder/index"
        });

        return;
      }

      // 当前用户尚有创建好的服务但是没有创建订单
      if (errorCode === 21103) {
        const payFunc = () => {
          toConfirmOrder({
            serviceCode: params.serverCode,
            type: "2",
            businessId: errorData?.id,
            lawyerId: params.lawyerId,
            synHistoryId
          });
        };

        payFunc();

        // 将付费方法存入vuex
        store.commit("payState/SET_PAY_FAIL_POPUP_PAY", payFunc);

        return resolve(payFunc);
      }

      const payFunc = () => {
        toConfirmOrder({
          serviceCode: params.serverCode,
          type: "2",
          businessId: data.caseSourceServerV2Id,
          lawyerId: params.lawyerId,
          synHistoryId
        });
      };

      payFunc();

      // 将付费方法存入vuex
      store.commit("payState/SET_PAY_FAIL_POPUP_PAY", payFunc);

      return resolve(payFunc);
    });
  });
};

/** 免费追问=》畅聊=》获取律师指定卡片一条付费信息 */
export const serviceManageBylawyer = data =>
  requestInfo.post("/serviceManage/byLawyer", data);

/* 律师主页用户购买服务滚动信息 2.1.8*/
export const fastConsultBuyLawyers = data =>
  requestInfo.post("/lawyer/fastConsult/lawyerBuyUsers", data);

/* 律师得分 2.1.8*/
export const scoreCountLawyers = data =>
  requestCommon.post("/lawyer/userEvaluate/scoreCount", data);

/* 2.1. 查询律师的评价列表*/
export const getEvaluatePage = data =>
  requestLawyer.post("/userEvaluate/evaluatePage", data);

/** 律师案件列表-大数据提供 */
export const bigdataLawyerCaseList = data =>
  requestLawyer.post("/bigdata/lawyer/case/list", data);

/** 根据商品code获取评价 */
export const getEvaluateByServiceCode = data =>
  requestInfo.post("/serviceManege/getEvaluateByServiceCode", data);

/** 根据商品code获取平均评价分数 */
export const getAvgEvaluateByServiceCode = data =>
  requestInfo.post("/serviceManege/getAvgEvaluateByServiceCode", data);

/** 律师案件详情-大数据提供 */
export const bigdataLawyerCaseDetail = data =>
  requestLawyer.post("/bigdata/lawyer/case/detail", data);

/** 统计指标查询-大数据提供 */
export const bigdataLawyerIndex = data =>
  requestLawyer.post("/bigdata/lawyerIndex", data);

export const lawyerCaseDetail = params =>
  requestCommon.post("/lawyer/bigdata/lawyer/case/detail", params); // 律师案件详情

/**
 * 小程序律师主页用户购买服务滚动信息
 * <AUTHOR>
 */
export const lawyerLawyerZx = data =>
  requestInfo.post("/lawyer/lawyerZx", data);

/** 师主页文章 律师主页 -> 文章列表 */
export const articleV2LawyerPage = data =>
  requestInfo.post("/articleV2/lawyer/page", data);

/** 文章详情 */
export const articleV2Detail = data =>
  requestInfo.post("/articleV2/detail", data);

/** 文章浏览量新增 */
export const articleV2DealPageView = data =>
  requestInfo.post(`/articleV2/dealPageView/${data.articleV2Id}`, data);

export const highQualityArticle = data =>
  requestInfo.post("/articleV2/highQualityArticle", data);
export const getInfoById = (data) =>
  requestCommon.post("/lawyer/lawyer/getInfoById", data);

/** 新版律师排名 */
export const lawyerNewRank = (data) =>
  requestCommon.post("/info/lawyer/newRank", data);

/** 今日咨询动态 */
export const todayConsultTrends = () =>
  requestCommon.post("/info/lawyer/todayConsultTrends");

/** 首页优选律师列表*/
export const mainPreferredLawyerHome = (data) => requestCommon.post("/info/lawyer/main/preferredLawyer", data);


/** 最近24小时咨询的服务 */
export const caseSourceServerV2RecentConsult = (data) => requestCommon.post("/info/caseSourceServerV2/recentConsult", data);

/** 全量落地页过滤词库判定拦截 */
export const perfectMatchStatementInfo = (data) => requestCommon.post("/info/caseSourceV2/perfectMatchStatementInfo", data);
