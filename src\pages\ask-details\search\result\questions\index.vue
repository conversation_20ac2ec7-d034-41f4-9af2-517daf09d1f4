<template>
  <div>
    <search-input
      v-model="searchInput"
      disabled
    />
    <div
      v-for="item in qaMessage2cPageSearchData.records"
      :key="item.id"
      class="bg-[#FFFFFF] rounded-[8px] overflow-hidden mt-[12px] mx-[12px]"
    >
      <lawyer-card :data="item" />
    </div>
    <u-safe-bottom />
  </div>
</template>

<script>
import searchData from "@/pages/ask-details/search/searchData";
import { qaMessage2cPageSearch } from "@/api/common";
import SearchInput from "@/components/SearchInput.vue";
import LawyerCard from "@/pages/index/component/lawyerCard.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "ResultQuestions",
  components: { USafeBottom, LawyerCard, SearchInput },
  data() {
    return {
      /** 问答数据 */
      qaMessage2cPageSearchData: {},
      searchInput: searchData.searchText || ""
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    /** 请求数据 */
    getData() {
      const keyword = searchData.searchText;

      qaMessage2cPageSearch({
        keyword,
        currentPage: 1,
        pageSize: 99
      }).then(res => {
        this.qaMessage2cPageSearchData = res.data;
      });
    }
  }
};
</script>
