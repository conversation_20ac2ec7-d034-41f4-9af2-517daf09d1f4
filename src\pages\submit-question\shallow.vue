<template>
  <login-layout>
    <submit-layout
      ref="imLayout"
      :currentData="currentData"
      :inputContainerDisable="inputContainerDisable"
      :inputContainerState="currentData.inputContainerState"
      :isBGarbageWords="isBGarbageWords"
      :lawyerInfo="getLawyerInfo"
      :logoState="false"
      :meInfo="getMeInfo"
      :moreFeaturesState="false"
      :msg="msg"
      :newsList="newsList"
      :placeholderText="placeholderText"
      :sendButtonDisabled="sendButtonDisabled"
      class="im-layout"
      hiddenHeader
      @consultingOptionsEmit="consultingOptionsEmit"
      @consultingOptionsTimeEnd="consultingOptionsTimeEnd"
      @selectCity="selectCity"
      @sendPrivateText="throttleSendPrivateText"
      @set-msg="setMsg"
      @history-select="historySelect"
      @re-consult="reConsult"
      @no-service-required="noServiceRequired"
      @additional-confirm="additionalConfirm"
    >
      <template #beforeHeader>
        <progress-rate :activeIndex="currentData.activeIndex" />
        <submit-fixed-pay-tip
          v-model="headerTip"
          :typeLabel="caseType"
        />
        <submit-fixed-guide-tip-new
          v-if="secondGuideVisible"
          :canShow="currentData.isLastStep"
        />
        <submit-discount-popup-new
          v-model="payCancelPopup"
          :currentData="currentData"
          cancelCode="LAW_APPLET_PAY_CONFIRM_PAY_PAGE_GIVE_UP_CLICK"
          payCode="LAW_APPLET_PAY_CONFIRM_PAY_PAGE_PAY_CLICK"
          uvCode="LAW_APPLET_PAY_CONFIRM_BACK_PAGE"
        />
        <!-- #ifndef MP-WEIXIN -->
        <!--        <lawyer-top-tips />-->
        <!-- #endif -->
      </template>
    </submit-layout>
  </login-layout>
</template>

<script>
import { buryPointChannelBasics } from "@/api/burypoint.js";
import { wordsCheckDoCheck } from "@/api/im";
import LoginLayout from "@/components/login/login-layout.vue";
import SubmitDiscountPopupNew from "@/components/submit-discount-popup-new/index.vue";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint.js";
import { paralegalTools } from "@/libs/paralegalTools";
import { webImSendType } from "@/libs/web-im-tools";
import { xssAttack } from "@/libs/xssAttack";
import payFailMixins from "@/mixins/payFailMixins.js";
import { clearLatestArticleTitle } from "@/pages/lawyer-home/index.js";
import ProgressRate from "@/pages/submit-question/components/progress-rate/progress-rate.vue";
import SubmitFixedGuideTipNew from "@/pages/submit-question/components/submit-fixed-guide-tip-new.vue";
import SubmitFixedPayTip from "@/pages/submit-question/components/submit-fixed-pay-tip.vue";
import SubmitLayout from "@/pages/submit-question/components/submit-layout.vue";
import { isLessThanEightThousand, selectAmountBuriedPoint, SUBMIT_PAGE_TYPE } from "@/pages/submit-question/js";
import historyMixin from "@/pages/submit-question/js/historyMixin.js";
import SubmitCommonMixin from "@/pages/submit-question/mixins/SubmitCommonMixin";
import submitSaveMixin from "@/pages/submit-question/mixins/submitSaveMixin.js";
import { optionsType } from "@/pages/submit-question/components/card-consulting-options/index.js";
import store from "@/store";
import { initSubmitQuestionData } from "@/pages/submit-question/js/index.js";
import { caseSourceV2GetInfo, caseSourceV2Save } from "@/api";
import { whetherDeviceToLogIn } from "@/libs/login";
import { getPhoneAddress } from "@/api/user";
import { getLocation } from "@/libs/getLocation";
import { getChannelServiceInfoExtra } from "@/libs/getPageInfo";

export default {
  name: "SubmitQuestionOther",
  components: {
    SubmitDiscountPopupNew,
    SubmitFixedGuideTipNew,
    SubmitFixedPayTip,
    LoginLayout,
    ProgressRate,
    SubmitLayout
  },
  mixins: [submitSaveMixin, historyMixin, payFailMixins, SubmitCommonMixin],
  data() {
    return {
      pageType: SUBMIT_PAGE_TYPE.shallowPage,
      /** 当前界面的数据 */
      currentData: {
        ...initSubmitQuestionData(),
        aiList: [],
        count: 0,
        catchList: [],
        /** AI 资讯是否结束 */
        isEnd: false
      },
      /** 定位信息确定获取到后的回调 */
      waitSaveCaseInfoCallback: null,
      pageConfig: {}
    };
  },
  watch: {
    currentData: {
      handler() {
        this.paralegalTools.saveHistory(this.currentData);
      },
      deep: true
    }
  },
  mounted() {
    buryPointChannelBasics({
      code: "LAW_APPLET_ASK_LAWYER_FAKE_IM_PAGE",
      behavior: BURY_POINT_CHANNEL_TYPE.CK,
      type: 1
    });

    this.getSysMinLength();
    this.getPageConfig();

    this.paralegalTools = paralegalTools(this.getLawyerName, this.getUsername, {
      catchList: this.currentData.catchList,
      notCatchComponentsNameList: [webImSendType.SUBMIT_AI_TEXT, webImSendType.WAITING_FOR_CARD, webImSendType.CARD_ADDRESS_INFO]
    });

    this.initPageData();

    store.commit("askALawyer/SET_PLACEHOLDER_HEIGHT", 0);
  },
  methods: {
    /**
     * 根据 count 下标获取配置值，如果当前下标没有值则递减查找
     * @param {Array|Object} config - 配置数组或对象
     * @returns {*} 找到的配置值
     */
    getConfigByIndex(config) {
      if (!config || typeof config !== "object") {
        return config;
      }

      // 如果是数组
      if (Array.isArray(config)) {
        let index = this.currentData.count;

        // 从当前 count 开始递减查找，直到找到有值的位置或到达 0
        while (index >= 0) {
          if (config[index] !== undefined && config[index] !== null) {
            return config[index];
          }
          index--;
        }

        // 如果都没找到，返回数组第一个元素或 undefined
        return config[0];
      }

      // 如果是对象，直接返回
      return config;
    },
    /**
     * 替换字符串中的占位符
     * @param {string} template - 原始字符串模板，如 "法临AI：{info}\n"
     * @param {string} replacement - 需要替换的内容
     * @returns {string} 替换后的字符串
     */
    replaceTemplate(template, replacement) {
      if (typeof template !== "string") {
        return template;
      }

      return template.replace(/\{info\}/g, replacement || "");
    },
    /** 获取页面配置信息 */
    getPageConfig() {
      getChannelServiceInfoExtra().then((data) => {
        console.log(data, "页面配置信息");
        this.pageConfig = data;
      });
    },
    async saveCaseAmount() {
      const res = await caseSourceV2GetInfo();

      // 如果 res.data 有值，则不生成案源
      if (res.data) {
        const { data: caseSourceInfo } = await caseSourceV2Save({
          info: this.currentData?.info
        });

        this.currentData.caseSourceInfo = caseSourceInfo;
        return;
      }

      const { data: caseSourceInfo } = await caseSourceV2Save({
        info: this.currentData?.info,
        typeLabel: this.currentData?.selectData?.[0]?.typeLabel,
        typeValue: this.currentData?.selectData?.[0]?.typeValue,
        amountGrade: this.currentData?.selectData?.[1]?.typeValue,
        happenAddress:
          (this.currentData?.cityInfo?.provinceName || "") +
          (this.currentData?.cityInfo?.cityName || ""),
        regionCode: this.currentData?.cityInfo?.cityCode,
        squareShow: 1,
        isDistributed: false
      });

      this.currentData.caseSourceInfo = caseSourceInfo;
    },
    /**
     * 发送消息到AI
     */
    sendMsgToAi(info) {
      this.currentData.aiList.push({
        role: "user",
        info: info
      });

      return new Promise((resolve) => {
        this.paralegalTools.promise(
          this.newsList,
          this.paralegalTools.addReceiveData({
            type: "custom",
            data: {
              style: webImSendType.SUBMIT_AI_TEXT
            },
            otherData: {
              infoList: this.currentData.aiList,
              api: "new"
            },
            callback: async ({ data }) => {
              this.paralegalTools.addReceiveData({
                type: "text",
                data: data.content
              });

              this.currentData.isEnd = data.is_end === 1;

              this.currentData.inputContainerState = !this.currentData.isEnd;
              resolve(data);
            }
          })
        );
      });
    },
    /*
     * 请求各种信息后进行入库
     * https://lanhuapp.com/web/#/item/project/product?pid=dd45032e-281c-49e6-a913-3381909d2fb9&teamId=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=3d732852-f9d3-4f4a-a25f-db6c4990b50d&docId=01294e5b-2bd1-48e2-9bb4-953a48de2d8a&docType=axure&pageId=bc0d24d7256e41a892b4a140e518392a&image_id=01294e5b-2bd1-48e2-9bb4-953a48de2d8a&parentId=ec8fc344-b372-466d-816a-52064bc75b09
     */
    async sendPrivateTextAfterAiReply() {
      this.currentData.inputContainerState = false;

      await this.selectConsultationType();

      /* 添加用户发送的消息 并发送出咨询类型选择*/
      this.newsList = [
        ...this.newsList,
        this.paralegalTools.addReceiveData({
          type: "custom",
          data: {
            style: webImSendType.CONSULTING_OPTIONS
          },
          otherData: {
            showTitle: true,
            overlay: true,
            layout: "horizontal",
            groupCode: "LAWYER_SPECIALITY",
            cancellable: false,
            name: optionsType.CONSULTATION_TYPE,
            textRender:
              this.pageType === SUBMIT_PAGE_TYPE.privatePage
                ? "请确认您所遇到的问题信息"
                : "请选择您的咨询内容的类型及涉案金额",
            titleText: "请选择咨询类型",
            clickToSwitch: true,
            typeValue: this.currentData.selectData?.[0]?.typeValue
          },
          avatarUrl: false
        })
      ];
    },
    /**
     * * 2 咨询类型选择回调
     * 现在不用完善信息了
     * @param selectData
     * @Version 2.1.8
     * @update https://lanhuapp.com/web/#/item/project/product?tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&pid=7bbbb963-63f2-450d-b338-7cd2aa07d7ee&versionId=0d71c86d-df55-4679-a292-d26a86b35bbd&docId=aafd281f-559b-4acf-a9ba-5a6f9801b936&docType=axure&pageId=8b8083289f084d7c907aadc4e39919aa&image_id=aafd281f-559b-4acf-a9ba-5a6f9801b936&parentId=735b223f-c556-4037-9e04-065825c83809
     */
    consultingOptionsEmit(selectData) {
      /** 选择案件金额后触发的事件 */
      const selectCaseAmount = async (data) => {
        selectAmountBuriedPoint();

        this.currentData.isLessThanEightThousand =
          await isLessThanEightThousand(data.typeValue);

        // ip地址位置
        const { data: location } = await getPhoneAddress();
        this.currentData.ipCityInfo = location;

        try {
          // 如果有定位
          const data = await getLocation();

          this.currentData.wechatCityInfo = data;

          // https://lanhuapp.com/web/#/item/project/product?pid=f282b0dd-b9d0-4574-9ad3-a1a5e69c40e5&image_id=172e4315-97f4-45f7-96bc-93c8f03dbd6b&tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=6a641d6b-a9c0-409e-9329-97f3eb78c967&docId=172e4315-97f4-45f7-96bc-93c8f03dbd6b&docType=axure&pageId=da86f1f9d0ee48b3b5234e8584311d0c&parentId=2ae5bdd3-ec3f-4404-bc91-ded38a3d9a3d
          this.currentData.cityInfo = data;
          this.waitSaveCaseInfoCallback?.();
        } catch (e) {
          // 如果没有定位
          await this.paralegalTools.delayAddData(this.newsList, [
            this.paralegalTools.addReceiveData({
              type: "custom",
              data: { style: webImSendType.CARD_ADDRESS_INFO }
            })
          ]);
        }
      };

      const { data, name } = selectData;

      if (name === optionsType.CASE_AMOUNT) {
        // 3.1.7 新流程，选择金额后才验证登陆
        whetherDeviceToLogIn(async () => {
          try {
            await this.getHistory();
          } catch (e) {
            this.$set(this.currentData.selectData, 1, data);
            await selectCaseAmount(data);
          }
        });
      }
    },
    /** 浅层马甲包的选择城市单独处理 */
    async selectCity(data) {
      console.log(data, "外层获取到了data的数据");
      this.currentData.cityInfo = data;

      this.newsList.pop();

      await this.paralegalTools.delayAddData(this.newsList, [
        this.paralegalTools.addSendData({
          type: "text",
          data: `<div>当前所在地：${this.currentData.cityInfo.cityName}</div>`
        })
      ]);

      this.waitSaveCaseInfoCallback?.();
    },
    async sendPrivateText() {
      this.$store.commit("askALawyer/CLEAR_TIMER");

      try {
        const msg = xssAttack(this.msg);

        // 判断输入的内容是否全部是空格
        if (msg.trim().length === 0) {
          uni.showToast({
            title: "检测到敏感词：内容不能只有数字和英文或者只有标点符号",
            duration: 2000,
            icon: "none"
          });

          return;
        }

        if (await this.isLessThanMinLength(msg)) return;

        const sendFn = async () => {
          await wordsCheckDoCheck({
            text: msg
          });

          clearLatestArticleTitle();

          // 用户发送的消息
          await this.paralegalTools.delayAddData(this.newsList, [
            this.paralegalTools.addSendData({
              type: "text",
              data: msg
            })
          ]);

          this.currentData.inputContainerState = false;

          this.waitSaveCaseInfoCallback = async () => {
            const requestData = await this.sendMsgToAi(this.msg);

            this.currentData.aiList.push({
              role: "assistant",
              info: requestData.content
            });

            const assistant = this.getConfigByIndex(this.pageConfig.aiProcessSplicing.assistant);
            const user = this.getConfigByIndex(this.pageConfig.aiProcessSplicing.user);

            this.currentData.count++;

            this.currentData.info += (this.replaceTemplate(user, this.msg) + (this.currentData.count >= this.pageConfig.aiMaxEndProcessNode ? "" : this.replaceTemplate(assistant, requestData.content)));

            this.msg = "";
            // 如果到了规定的入库节点，才进行保存信息
            if (this.currentData.count >= this.pageConfig.aiProcessNode && this.currentData.count <= this.pageConfig.aiMaxEndProcessNode) {
              await this.saveCaseAmount();
            }

            if (requestData.is_end === 1) {
              await this.paralegalTools.delayAddData(this.newsList, [
                // https://lanhuapp.com/web/#/item/project/product?pid=e61590e1-ddf5-49fc-ae13-8bce49c40c39&teamId=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=baf9ff1b-01e5-451a-86b4-97a2bb501506&docId=d4bf1024-ef66-4264-bf6d-9b133d97399f&docType=axure&pageId=1f0604fb8eaf4f2fa884efe38e11387c&image_id=d4bf1024-ef66-4264-bf6d-9b133d97399f&parentId=299fe10a-2c24-4494-854d-e47757423223&tid=43efda02-ce73-4682-acd1-afc75cbf6b0c
                this.paralegalTools.addReceiveData({
                  type: "custom",
                  data: {
                    style: webImSendType.WAITING_FOR_CARD
                  },
                  delay: 1000
                })
              ]);
            }
          };

          if (this.currentData.count === (this.pageConfig.aiProcessNode - 1)) {
            await this.sendPrivateTextAfterAiReply();
          } else {
            this.waitSaveCaseInfoCallback?.();
          }
        };

        whetherDeviceToLogIn(async () => {
          try {
            if (this.currentData.count === 0) {
              await this.getHistory();
            } else {
              await sendFn();
            }
          } catch (e) {
            await sendFn();
          }
        });
      } catch (e) {
        console.log(e);
      }
    }
  }
};
</script>
