<template>
  <app-popup
    :show="getShow"
    :zIndex="10002"
    class="popup"
    getContainer="body"
    lowCancelButton
    mode="center"
    @cancel="close"
  >
    <div>
      <div class="rounded-[16px] relative pb-[24px] bg-gradient-to-br from-[#F9FAFF] to-[#DDE7FF]">
        <div class="flex items-center justify-center">
          <img
            :src="data.imgUrl"
            alt=""
            class="w-[72px] h-[72px] mt-[-24px] rounded-[50%] border-4 border-[#FFFFFF] border-solid"
          >
        </div>
        <p class="font-bold text-[16px] text-[#333333] text-center pb-[8px] pt-[8px]">
          {{ data.realName }}律师
        </p>
        <p class="text-[13px] text-center text-[#666666]">
          {{ productInfo.title }}
        </p>

        <div class="pt-[16px] px-[24px] pb-[24px] relative">
          <div class="bg-[#FFFFFF] shadow-[0_0_12px_0_rgba(54,110,255,0.1)] rounded-[8px] box-border p-[16px] flex">
            <img
              :src="serverInfo.icon"
              alt=""
              class="w-[44px] h-[44px] mr-[12px] rounded-[8px]"
            >
            <div class="flex-1 flex flex-col justify-between">
              <p class="font-bold text-[16px] text-[#333333]">
                {{ serverInfo.serviceName }}
              </p>
              <p class="text-[12px] text-[#999999]">
                规格：{{ serverInfo.serviceNum || 1 }}{{ serverInfo.unitLabel }}
              </p>
            </div>
            <div class="font-bold text-[16px] text-[#333333]">
              ¥{{ serverInfo.servicePrice | amountFilter }}
            </div>
          </div>
        </div>
        <div
          class="w-[263px] h-[40px] mx-auto bg-[#3887F5] leading-[40px] text-center text-[15px] font-[400] text-[#FFFFFF] rounded-[20px]"
          @click="toOrder"
        >
          去支付
        </div>
      </div>
    </div>
  </app-popup>
</template>


<script>
import AppPopup from "@/components/app-components/app-popup";
import { buryPointChannelBasics } from "@/api/burypoint.js";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint.js";
import { isObjNull } from "@/libs/basics-tools.js";

export default {
  name: "PrivateTelephoneConsultation",
  components: {
    AppPopup
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({}),
      desc: "律师信息"
    },
    /* 商品信息*/
    productInfo: {
      type: Object,
      default: () => ({
        title: "",
        data: {},
        buryThePoint: {}
      }),
    },
  },
  computed: {
    getShow: {
      set(value) {
        this.$emit("input", value);
      },
      get() {
        return this.value;
      }
    },
    serverInfo(){
      return this.productInfo.data || {};
    },
  },
  watch: {
    getShow(){
      if(this.getShow){
        this.getBuryThePoint("page", (code) => {
          buryPointChannelBasics({
            code,
            type: 1,
            behavior: BURY_POINT_CHANNEL_TYPE.VI
          });
        });
      }
    }
  },
  methods: {
    /* 获取埋点*/
    getBuryThePoint(key, callback){
      if(this.productInfo.buryThePoint && !isObjNull(this.productInfo.buryThePoint) && this.productInfo.buryThePoint[key]){
        callback && callback(this.productInfo.buryThePoint[key]);
      }
    },
    close() {
      this.getBuryThePoint("close", (code) => {
        buryPointChannelBasics({
          code,
          type: 1,
          behavior: BURY_POINT_CHANNEL_TYPE.CK
        });
      });
      this.$emit("input", false);
    },
    toOrder(){
      this.getBuryThePoint("click", (code) => {
        buryPointChannelBasics({
          code,
          type: 1,
          behavior: BURY_POINT_CHANNEL_TYPE.CK
        });
      });
      this.$emit("handleClickPay", {
        serviceCode: this.serverInfo.serviceCode,
        type: "2",
      });
    }
  }
};
</script>