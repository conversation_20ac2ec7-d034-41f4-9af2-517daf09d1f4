import { setIsOnShow, speedOrderType } from "./config";
import store from "@/store/index.js";
import { buryPointChannelBasics } from "@/api/burypoint.js";
import { BURY_POINT_CHANNEL_TYPE, POINT_CODE } from "@/enum/burypoint.js";
import { isArrNull, isNull, isObjNull } from "@/libs/basics-tools.js";
import {
  checkAppLoginAndGetCode,
  getUserTokenStorage,
  setReturnRouterStorage,
  setUserTokenStorage,
} from "@/libs/token.js";
import {
  getCurrentPageRoute,
  toCustomerServiceCenter,
  toLawyerFakeIm,
  toLawyerHome,
} from "@/libs/turnPages.js";
import { dataDictionary, getCommonConfigKey } from "@/api";
import { formatTime } from "@/libs/filter.js";
import { handleOrderPay } from "@/libs/pay";
import dayjs from "dayjs";
import {
  serviceManegeGetLawyerAllServiceList,
  wordsCheckDoCheck,
} from "@/api/im";
import {
  douYinTradeOrderConfirmStartService,
  getLatestDouYinTradeOrder,
} from "@/api/order";
import { getParalegalDataId, saveParalegalData } from "@/libs/paralegalData";
import { getLocation } from "@/libs/getLocation.js";
import { lawyerListV3 } from "@/api/findlawyer.js";
import { bindWechatApplet } from "@/api/user";
import { serializationDictionary } from "@/api/index";

/**
 * 判断是否登录 需要登录的事件都需要走一步
 * @param nextCallback 登陆后触发的回调
 * @param options 以后可能会用到的参数
 * @returns {Promise<never>|Promise<boolean>}
 */
export function whetherToLogIn(nextCallback, options = {}) {
  console.log("store.getters[\"user/getToken\"]", store.getters["user/getToken"]);
  if (!store.getters["user/getToken"]) {
    store.commit("popup-state/SET_LOGIN_POPUP_STATE", true);
    store.commit("user/SET_LOGIN_CALL_BACK", nextCallback);
    buryPointChannelBasics({
      code: POINT_CODE.LAW_APPLET_LOGIN_POPUP,
      behavior: BURY_POINT_CHANNEL_TYPE.VI,
      type: 1,
    });
  } else {
    nextCallback && nextCallback();
  }
}

/** 过滤obj的空值 */
export function filterObjUndefined(obj) {
  if (typeof obj === "object") {
    return Object.keys(obj).reduce((prev, cur) => {
      if (obj[cur] !== undefined) {
        prev[cur] = obj[cur];
      }
      return prev;
    }, {});
  }
  return obj;
}

/* 手机号验证*/
export function validateMobile(value) {
  console.log(value);
  const reg = /^1\d{10}$/;
  if (!value) {
    return Promise.reject("请输入手机号码");
  } else if (!reg.test(value)) {
    return Promise.reject("请输入正确的手机号码");
  } else {
    return Promise.resolve();
  }
}

/* 手机验证码*/
export function validateCode(value) {
  const reg = /^\d{4}$/;

  if (!value) {
    return Promise.reject("请输入验证码");
  } else if (!reg.test(value)) {
    return Promise.reject("请输入正确的验证码");
  } else {
    return Promise.resolve();
  }
}

/**
 * @description:首字母大写
 * @author:djsong
 * @date:2021/6/16
 * @param:str 字符串
 * @return:str 字符串
 */
export const titleCase = (str) => {
  // 把字符串所有的字母变为小写，并根据空格转换成字符数组
  const arr = str.toLowerCase().split(" ");
  // 遍历字符数组
  for (let i = 0; i < arr.length; i++) {
    // 把第一个字符变为大写
    arr[i] = arr[i][0].toUpperCase() + arr[i].substring(1, arr[i].length);
  }
  // 加上空格，返回原模式的字符串
  return arr.join(" ");
};

/**
 * 节流函数 用于防止多次点击 在最开始的时候执行
 * @param func
 * @param timeFrame
 * @return {(function(...[*]): void)|*}
 */
export const throttle = (func, timeFrame) => {
  let lastTime = 0;
  return function (...args) {
    const now = new Date();
    if (now - lastTime >= timeFrame) {
      func(...args);
      lastTime = now;
    }
  };
};

// 防抖
export const debounce = (func, wait) => {
  let timeout;
  return function () {
    let context = this;
    let args = arguments;
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(context, args);
    }, wait);
  };
};

export const judgeClient = () => {
  const u = navigator.userAgent;
  const isAndroid = u.indexOf("Android") > -1 || u.indexOf("Adr") > -1; // 判断是否是 android终端
  const isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // 判断是否是 ios终端
  if (isAndroid === true) {
    return "Android";
  } else if (isIOS === true) {
    return "IOS";
  } else {
    return "Android";
  }
};

/* 判断是否安卓*/
export const isAndroidClient = () => {
  return judgeClient() === "Android";
};
/* 判断是否ios*/
export const isIOSClient = () => {
  return judgeClient() === "IOS";
};
/* 判断是否钉钉*/
export const isDingTalk = () => navigator.userAgent.indexOf("DingTalk") > -1;

// 判断当前是不是微信
export const isWeiXin = () => {
  const ua = window.navigator.userAgent.toLowerCase();
  return ua.match(/MicroMessenger/i) == "micromessenger";
};

/**
 * 时间格式化
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string") {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time);
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), "/");
      }
    }

    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    return value.toString().padStart(2, "0");
  });
  return time_str;
}

export const parseTimeToDay = (val) => {
  // console.log(val, "=======================");
  if (!val) return;
  const day = parseTime(val, "{d}");
  const nowDay = parseTime(new Date(), "{d}");
  if (Math.abs(day - nowDay) === 0) return "今天 " + parseTime(val, "{h}:{i}");
  if (Math.abs(day - nowDay) === 1) return "昨天 " + parseTime(val, "{h}:{i}");
  if (Math.abs(day - nowDay) >= 1) return parseTime(val, "{y}-{m}-{d} {h}:{i}");
  // const res = parseTime(val)
  // return res
};

// 极速我的订单状态值转换文字及颜色
export const formatSpeedOrderType = (val) => {
  let obj = {
    [speedOrderType.UNPAID]: { txt: "待支付", color: "#F78C3E" },
    [speedOrderType.PAID]: { txt: "已支付", color: "#999999" },
    [speedOrderType.CLOSED]: { txt: "已关闭", color: "#999999" },
    [speedOrderType.REFUNDED]: { txt: "已退款", color: "#999999" },
  };
  if (obj[val]) {
    return obj[val];
  } else {
    return val;
  }
};
/* 获取尺寸*/
export const findDimensions = () => {
  return new Promise((resolve, reject) => {
    uni.getSystemInfo({
      success: (res) => {
        resolve({
          height: res.windowHeight,
          width: res.windowWidth,
        });
      },
    });
  });
};

/**
 * 实现页面定位，让不同宽度的页面定位到相同的位置
 * 计算滚动距离，以375的宽度为基准
 */
export function calculateScrollDistance(top = 0) {
  // 获取屏幕宽度
  const screenWidth = uni.getSystemInfoSync().screenWidth;

  return (top / 375) * screenWidth;
}

// 极速我的订单已支付中退款状态值转换
export const formatRefundOrder = (val) => {
  let obj = {
    [speedRefundType.UNREFUNDED]: { txt: "退款", color: "#333333" },
    [speedRefundType.REFUNDING]: { txt: "退款中", color: "#999999" },
    [speedRefundType.REFUNDSUCESS]: { txt: "退款详情", color: "#333333" },
    [speedRefundType.REFUNDFAILED]: { txt: "退款失败", color: "#999999" },
  };
  if (obj[val]) {
    return obj[val];
  } else {
    return val;
  }
};
/* 对象转化成字符串*/
const stringify = (obj = {}) => {
  let str = "";
  Object.keys(obj).forEach(function (key) {
    str += key + "=" + obj[key] + "&";
  });
  return isNull(str) ? str : str.slice(0, str.length - 1);
};

/* 设置详情页面id*/
export const setDetailId = (path, params = {}) => {
  const toPath = path;
  if (isObjNull(params)) return toPath;
  else return toPath + "?" + stringify(params);
};

/**
 * 跳转到订单确定页面
 * @param {number} serviceCode
 * @param {string} [type] 现在已经不需要传递该值 (1:案源；2:1v1 3:公共咨询)
 * @param {string} [lawyerId]
 * @param {string} [businessId] type===1传
 * @param {string} [orderId] 有orderId传
 * @param {string} [synHistoryId] 历史记录id
 * @param {string} [synWdId] 公共付费问答id
 * @param {string} [path] 订单确定页面返回的路径
 * @param {string} [synCaseSourceId] 公共付费案源id
 * @param {Function} [paySuccessCallback] 支付成功回调
 * @param authChangeLawyer
 */
export function toConfirmOrder(
  {
    serviceCode,
    type,
    lawyerId,
    businessId,
    orderId,
    synHistoryId,
    synWdId,
    synCaseSourceId,
    authChangeLawyer,
    paySuccessCallback,
  },
  path
) {
  store.commit("payState/SET_PAY_PAGE_ROUTE", getCurrentPageRoute().fullPath);

  // loading
  uni.showLoading({
    mask: true,
    title: "订单创建中",
  });

  setReturnRouterStorage(path || getCurrentPageRoute().fullPath);

  let query = {};

  if (type === "1") {
    query = {
      serviceCode,
      businessId,
      type,
      paySuccessCallback,
    };
  } else {
    query = {
      serviceCode,
      type,
      lawyerId,
      businessId,
      orderId,
      synHistoryId,
      synWdId,
      synCaseSourceId,
      authChangeLawyer,
      paySuccessCallback,
    };
  }
  handleOrderPay(query);
  // uni.navigateTo({
  //   url: "/pages/order/index?" + stringify(query),
  // });
}

export const toConfirmOrderPlus = (scene, type) => {
  uni.navigateTo({
    url:
      "/pages/rapid-consultation-confirm-order/index?" +
      stringify({
        scene,
        type,
      }),
  });
};

/** 1.7.0获取系统配置的服务时间 案源付费默认有效时间(秒) 7天 */
export const getServiceCasePaySetTime = () => {
  return new Promise((resolve) => {
    getCommonConfigKey({ paramName: "case_source_v2_pay_valid_time" }).then(
      ({ data }) => {
        if (data) {
          resolve({
            data: formatTime(data.paramValue),
            value: data.paramValue,
          });
        } else {
          resolve({ data: "7天", value: 604800 });
        }
      }
    );
  });
};

/** 1.7.0获取系统配置的服务时间 案源默认有效时间(秒) 3天 */
export const getServiceCaseSetTime = () => {
  return new Promise((resolve) => {
    getCommonConfigKey({ paramName: "case_source_v2_valid_time" }).then(
      ({ data }) => {
        if (data) {
          resolve({
            data: formatTime(data.paramValue),
            value: data.paramValue,
          });
        } else {
          resolve({ data: "3天", value: 259200 });
        }
      }
    );
  });
};

/**
 * 计算时间与今天的差值，返回格式为 今天  或者  昨天 或者 日期
 * @param time moment支持的时间格式
 */
export const parseTimeDiffToday = (time) => {
  const date = dayjs(time);
  const todayDate = dayjs();
  /** 判断是今天 */
  if (date.isSame(todayDate, "day")) {
    return "今天" + date.format("HH:mm");
  }

  /** 判断是昨天 */
  if (date.isSame(todayDate.subtract(1, "day"), "day")) {
    return "昨天" + date.format("HH:mm");
  }

  return date.format("MM-DD HH:mm");
};

/** 判断是否订阅了消息 */
export function isSubscribeMessage() {
  return uni.getStorageSync("subscribe");
}

/** 设置订阅消息 */
export function setSubscribeMessage() {
  uni.setStorageSync("subscribe", "true");
}

/** 清除订阅消息 */
export function clearSubscribeMessage() {
  uni.removeStorageSync("subscribe");
}

export function requestSubscribeMessage({ wechatTmplIdList }) {
  return new Promise((resolve) => {
    console.log("触发订阅消息");

    const success = (res) => {
      // 订阅成功
      console.log("订阅成功", res);
      uni.showToast({
        title: "订阅成功",
        icon: "none",
      });
      uni.setStorageSync("subscribe", "true");
      resolve(res);
    };
    const fail = (error) => {
      // 订阅失败
      console.log("订阅失败, 错误详情: ", error);
      resolve(error);
    };

    const complete = (res) => {
      // 订阅完成
      console.log("订阅API调用完成: ", res);
      resolve(res);
    };

    let tmplIds = [];

    // #ifdef MP-TOUTIAO
    tmplIds = [
      "MSG1358066efdd86e72c8f44ea2f9b5040be879cf514399",
      "MSG135806654dd39f778e76c4fc79f68d99b7c305215634",
    ];
    // #endif

    // #ifdef MP-WEIXIN
    tmplIds = wechatTmplIdList || [
      "90kjnxC6uNd4GckLQkCcQWQt0ykdF5kHZhGIpbTlDso",
      "JQNQuiH_edgmspPvsS4E6sP6teq2HADRpMwBYoUy2ac",
      "gt0vzpdGDhUWNaQMnCbYyzsOs1ONl2K7RlALNUIxiUE",
    ];
    // #endif

    // #ifdef MP-HJLS
    tmplIds = [
      "Rg1Fi2vQYT6HrgDmeLL6ZLQMDzzmZU6NbHH_k67AoEM",
      "lbTTUwYVIUIKxfNon3CnpDHjLjBRiToqNWY657CQg0I",
      "13HhqPvq6qHebW8IrvdGl0E5i3DT63EFwzngfvYMTYg",
    ];
    // #endif

    if (isArrNull(tmplIds)) return resolve();
    // #ifdef MP-WEIXIN
    uni.requestSubscribeMessage({
      // 需要填入开放平台申请的模板id，支持最多3个同类型模板
      tmplIds,
      success,
      fail,
      complete,
    });
    // #endif
  });
}

export function shareAppMessage(path) {
  const channelId = uni.getStorageSync("shareChannelId");
  if (channelId) {
    path = path + (path.includes("?") ? "&" : "?") + `channelId=${channelId}`;
  }
  console.log("分享path：", path);
  return {
    title: "快速免费问律师，点此←",
    path,
    templateId: "560mli8bgrpkpoqpeo",
    success() {
      console.log(
        "转发发布器已调起，并不意味着用户转发成功，微头条不提供这个时机的回调"
      );
    },
    fail() {
      console.log("转发发布器调起失败");
    },
  };
}

/**
 * 微信小程序分享
 * @param {string} [title] 转发标题
 * @param {string} [path] 转发路径
 * @param {string | null} [imageUrl] 自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径。支持PNG及JPG。显示图片长宽比是 5:4。
 * @param {string | null} [scImgUrl] 支付宝分享图片 自定义社交图片链接，作为分享到支付宝好友时的主体图片。建议尺寸 376x330。
 * @param {string | null} [bgImgUrl] 支付宝分享图片 自定义分享预览大图，建议尺寸 750x825，支持：网络图片路径；不支持：base64。
 */
export function shareAppMessageNew({
  title,
  imageUrl,
  path,
  scImgUrl,
  bgImgUrl,
} = {}) {
  // 如果path为空，或者path长度小于等于0 则默认为当前页面的路径
  if (!path || (path && path.length <= 0))
    path = getCurrentPageRoute().fullPath;

  if (!imageUrl || (imageUrl && imageUrl.length <= 0)) imageUrl = null;

  if (!bgImgUrl || (bgImgUrl && bgImgUrl.length <= 0)) bgImgUrl = null;

  if (!scImgUrl || (scImgUrl && scImgUrl.length <= 0)) scImgUrl = null;

  const channelId = uni.getStorageSync("shareChannelId");

  let sharePath = path;

  if (channelId) {
    sharePath =
      sharePath +
      (sharePath.includes("?") ? "&" : "?") +
      `channelId=${channelId}`;
  }

  console.log("分享path：", sharePath);

  return {
    title,
    imageUrl,
    /* 兼容支付宝*/
    /* #ifdef  MP-ALIPAY*/
    bgImgUrl,
    scImgUrl,
    /* #endif*/
    path: sharePath,
  };
}

/**
 * 获取元素的数据
 * @param {string} selector
 * @return {Promise<NodeInfo>}
 */
export function getClientRect(selector) {
  return new Promise((resolve, reject) => {
    try {
      let query;
      // 如果this存在
      if (this) {
        query = uni.createSelectorQuery().in(this).select(selector);
      } else {
        query = uni.createSelectorQuery().select(selector);
      }

      query
        .boundingClientRect((res) => {
          return resolve(res);
        })
        .exec();
    } catch (e) {
      return reject(e);
    }
  });
}

/** 生成一个随机的id */
export function createRandomId() {
  return Math.random().toString(36).substr(2);
}

/**
 * 包装函数，为了解决接口同时调用的问题
 * @param {function} request 请求函数
 * @param {boolean} [isLogin] 是否需要登录
 * @param {number} [spacing] 请求间隔时间，单位为毫秒，默认5000毫秒
 * @return {function(*=): Promise<unknown>}
 */
export const wrapRequest = (request, { isLogin, spacing } = {}) => {
  /** 上次调用时间 */
  let lastTime = 0;
  /** 接口是否在请求中 */
  let isRequesting = false;
  /** 接口返回数据 */
  let responseData = {};

  /** 判断接口是否可以调用 */
  const canCall = () => {
    return lastTime === 0;
  };

  return function requestFunction(...data) {
    return new Promise((resolve, reject) => {
      // 如果要校验登陆
      if (isLogin) {
        if (!store.getters["user/getToken"]) return reject();
      }

      // 如果没有到调用时机，则直接返回上次的数据
      if (!canCall()) {
        // 如果还在请求中，则递归调用，直到请求完成
        if (isRequesting) {
          setTimeout(() => {
            requestFunction(...data)
              .then((res) => {
                return resolve(res);
              })
              .catch((err) => {
                return reject(err);
              });
          }, 100);

          // 这里不做任何 resolve reject 从而让递归一直进行下去
          return;
        }
        return resolve(responseData);
      }

      lastTime = spacing || 5000;
      isRequesting = true;
      // 如果到了调用时机，则调用接口
      request(...data)
        .then((res) => {
          lastTime = spacing || 5000;
          setTimeout(() => {
            // 5秒后才能再次调用
            lastTime = 0;
          }, spacing || 5000);
          // 保存数据
          responseData = res;
          return resolve(responseData);
        })
        .catch((err) => {
          // 如果接口调用错误，则0.5秒后就能再次调用
          setTimeout(() => {
            lastTime = 0;
          }, spacing || 500);
          return reject(err);
        })
        .finally(() => {
          isRequesting = false;
        });
    });
  };
};

/* 对象排序*/
export const objKeySort = (obj) => {
  const newkey = Object.keys(obj).sort();
  const newObj = {};
  for (let i = 0; i < newkey.length; i++) {
    newObj[newkey[i]] = obj[newkey[i]];
  }
  return newObj;
};

// 判断当前时间是否在设置的夜间时间内
export function timeIsNight(minuteArr) {
  const nightStart = minuteArr[0] || 0;
  const nightEnd = minuteArr[1] || 480;
  const currentTime = new Date();
  const startNightTime = new Date();
  startNightTime.setHours(nightStart / 60); // 设置夜间开始时间的小时
  startNightTime.setMinutes(nightStart % 60); // 设置夜间开始时间的分钟

  const endNightTime = new Date();
  endNightTime.setHours(nightEnd / 60); // 设置夜间结束时间的小时
  endNightTime.setMinutes(nightEnd % 60); // 设置夜间结束时间的分钟

  if (startNightTime <= endNightTime) {
    // 夜间时间段没有跨越午夜
    return currentTime >= startNightTime && currentTime <= endNightTime;
  } else {
    // 夜间时间段跨越午夜
    return currentTime >= startNightTime || currentTime <= endNightTime;
  }
}

/** 敏感词检测 */
export function checkSensitiveWord(str) {
  return new Promise(async (resolve, reject) => {
    // 判断输入的内容是否全部是空格
    if (str.trim().length === 0) {
      uni.showToast({
        title: "检测到敏感词：内容不能只有数字和英文或者只有标点符号",
        duration: 2000,
        icon: "none",
      });

      return reject();
    }

    try {
      await wordsCheckDoCheck({
        text: str,
      });

      resolve();
    } catch (e) {
      reject(e);
    }
  });
}

/* 判断有无图文咨询 或者电话咨询服务*/
// id 律师id
// itemClassType	否	Integer	选项分类 1 电话 2 图文
// 图文咨询的回调 graphicConsultationCallbacks
export const checkServiceType = ({
  lawyerInfo,
  graphicConsultationCallbacks,
  itemClassType,
  otherData,
}) => {
  return new Promise((resolve, reject) => {
    return serviceManegeGetLawyerAllServiceList({
      lawyerId: lawyerInfo.id,
      itemClassType,
    }).then(({ data }) => {
      if (!isNull(data) && !isArrNull(data)) {
        const serverInfo = data[0];
        /*
          1、点击【电话咨询】button在当前页调起电话咨询弹窗；
          2、点击【图文咨询】button进入此律师IM页；
          * */
        if (itemClassType === 1) {
          store.dispatch("lawyerHome/openPhoneConsultationConfig", {
            lawyerInfo: lawyerInfo,
            serverInfo: serverInfo,
          });
        } else if (itemClassType === 2) {
          if (graphicConsultationCallbacks) {
            graphicConsultationCallbacks({ lawyerInfo, serverInfo });
          } else {
            toLawyerFakeIm({ ...lawyerInfo, ...otherData });
          }
        } else {
          resolve(serverInfo);
        }
      } else {
        reject();
      }
    });
  });
};

/**
 * 跳转到假im页之前先做判断
 * https://lanhuapp.com/web/#/item/project/product?tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&pid=737e1692-0202-4f84-a97a-ed00ec3d1873&versionId=7707d878-ac3f-46ab-b65c-38540d1fceb6&docId=6727aa7e-7cad-43f8-855b-dc622f994057&docType=axure&pageId=d04ab40f2cf84ddf9650b9e072b97b99&image_id=6727aa7e-7cad-43f8-855b-dc622f994057&parentId=1e99af8f-160b-4674-be01-aab19f06d654
 */
export const toLawyerFakeImBeforeLogin = ({
  lawyerInfo,
  itemClassType,
  graphicConsultationCallbacks,
  ...otherData
}) => {
  return checkServiceType({
    lawyerInfo,
    graphicConsultationCallbacks,
    itemClassType,
    otherData,
  })
    .then(() => {})
    .catch(() => {
      // 如果没有电话咨询，则直接进入图文咨询
      if (itemClassType === 1) {
        toLawyerFakeImBeforeLogin({
          lawyerInfo,
          itemClassType: 2,
          graphicConsultationCallbacks,
          ...otherData,
        });
      } else if (itemClassType === 2) {
        // 如果没有图文咨询
        toLawyerHome(lawyerInfo);
      }
    });
};

export const toLawyerFakeImBefore = ({
  lawyerInfo,
  itemClassType,
  /* 图文咨询回调*/
  graphicConsultationCallbacks,
  ...otherData
}) => {
  whetherToLogIn(() => {
    toLawyerFakeImBeforeLogin({
      lawyerInfo,
      graphicConsultationCallbacks,
      itemClassType,
      ...otherData,
    });
  });
};

export const setNavigationBarTitle = ({
  frontColor = "#000000",
  title = "",
  backgroundColor = "#f5f5f7",
} = {}) => {
  // #ifdef MP-ALIPAY
  my.setNavigationBar({
    title,
    backgroundColor,
    frontColor,
  });

  return false;
  // #endif
  uni.setNavigationBarTitle({
    title,
  });

  uni.setNavigationBarColor({
    backgroundColor,
    frontColor,
  });
};

export const setNavigationBarAlipay = (config = {}) => {
  // #ifdef MP-ALIPAY
  setNavigationBarTitle(config);
  // #endif
};

/** 将px转换为rpx，主要是为了兼容支付宝小程序 */
export function pxToRpx(px) {
  if (!px) return 0;

  // #ifndef MP-ALIPAY
  return px * 2 + "rpx";
  // #endif

  // #ifdef MP-ALIPAY
  return px / 50 + "rem";
  // #endif
}

/* 判断链接后面的文件类型*/
export function getFileType(url) {
  // 先处理 URL 参数
  const urlWithoutParams = url.split("?")[0];
  // 获取文件扩展名
  const parts = urlWithoutParams.split(".");
  return parts[parts.length - 1].toLowerCase();
}

/* 请求接口判断分页*/
export function registerPagingApi(api, paginationData = {}) {
  let isLast = false;
  let requestState = false;
  let pageData = {
    pageSize: 6,
    currentPage: 1,
    ...paginationData,
  };

  const resetApi = () => {
    isLast = false;
    requestState = false;
    pageData = {
      pageSize: 10,
      currentPage: 1,
      ...paginationData,
    };
  };
  return (params = {}) => {
    if (params._reset) {
      resetApi();
      delete params._reset;
    }
    if (isLast || requestState) return Promise.resolve([]);
    requestState = true;
    return api({ ...pageData, ...params })
      .then((data) => {
        const { records = [] } = data.data || {};
        console.log("分页数据", data.data || {});
        /* 判断是不是最后一页*/
        if (records.length < pageData.pageSize || records.length === 0) {
          isLast = true;
        }
        pageData.currentPage++;
        return records || [];
      })
      .finally((data) => {
        requestState = false;
        return data;
      });
  };
}

export const openDocument = (url, fileType) => {
  uni.showLoading({
    title: "正在打开文档",
  });
  if (!fileType) {
    //   识别url后缀 获取文件类型
    fileType = getFileType(url);
  }
  if (fileType.toLowerCase() === "doc") fileType = "docx";
  return new Promise((resolve, reject) => {
    uni.downloadFile({
      url,
      /* 兼容支付宝fileType类型必传*/
      /* #ifdef  MP-ALIPAY*/
      fileType,
      /* #endif*/
      success: function (res) {
        var filePath = res.tempFilePath;
        uni.openDocument({
          filePath: filePath,
          fileType,
          showMenu: true,
          success: function () {
            uni.hideLoading();
            resolve();
          },
          fail: function (e) {
            console.log(e);
            reject();
            uni.showToast({
              title: "打开文档失败",
              icon: "none",
            });
          },
        });
      },
      fail: function (e) {
        console.log(e);
        reject();
        uni.showToast({
          title: "打开文档失败",
          icon: "none",
        });
      },
    });
  });
};

/* !背死支付宝小程序  只能放在页面上 又不敢改app里面的参数怕出问题 复制一份出来*/
export const loadAppData = (option) => {
  const query = option || {};
  const {
    channelId,
    clickId,
    clickid,
    situationId,
    lawyerShareCode,
    click_id,
    gdt_vid,
    qz_gdt,
    userBehaviorType,
  } = query;
  uni.removeStorageSync("situationId");
  uni.removeStorageSync("lawyerShareCode");
  uni.removeStorageSync("loginQuery");
  uni.removeStorageSync("buryPointQuery");

  if (situationId) {
    uni.setStorageSync("situationId", situationId);
    /* 模板消息点击人数*/
    buryPointChannelBasics({
      code: POINT_CODE.LAW_TEMPLATE_MESSAGE_CLICK,
      behavior: BURY_POINT_CHANNEL_TYPE.CK,
      type: 1,
    });
  }

  /** 创建广告回传参数对象 */
  function createLoginQuery(obj) {
    let loginQuery = {};

    for (let key in obj) {
      // 如果有值，就添加到loginQuery中
      if (obj[key]) {
        loginQuery[key] = obj[key];
      }
    }

    // 如果loginQuery为空，就返回null
    if (Object.keys(loginQuery).length !== 0) {
      return loginQuery;
    } else {
      return null;
    }
  }

  // * 广告回传参数 只需要在下面的对象中添加属性即可
  // 登陆那边会自动获取
  const loginQuery = createLoginQuery({
    clickId: clickId || clickid || click_id || gdt_vid || qz_gdt,
  });

  // * 埋点回传参数
  const buryPointQuery = createLoginQuery({
    userBehaviorType,
  });

  if (loginQuery) {
    uni.setStorageSync("loginQuery", loginQuery);
  }

  if (buryPointQuery) {
    uni.setStorageSync("buryPointQuery", buryPointQuery);
  }

  const cacheChannelId = uni.getStorageSync("channelId");

  // 一旦缓存过渠道id，就不再覆盖
  // https://lanhuapp.com/web/#/item/project/product?pid=3ccec1f0-25c5-49dd-95f0-edb181c32bf4&image_id=baf8a44c-b631-46c3-b216-5797c0b4de43&tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=036871ef-2f7c-4095-855c-d6f848ad103c&docId=baf8a44c-b631-46c3-b216-5797c0b4de43&docType=axure&pageId=8cb8128d74d74ce3a1a74420a5256fc8&parentId=87fe0705-2af7-496a-8ae0-c08430cb942c
  if (!cacheChannelId) {
    if (channelId) {
      uni.setStorageSync("channelId", channelId);
    }
  }

  if (lawyerShareCode) {
    uni.setStorageSync("lawyerShareCode", lawyerShareCode);
  }

  console.log("获取到 广告回传参数: ", loginQuery);
  console.log("获取到 channelId: ", channelId);
  console.log("获取到 lawyerShareCode: ", lawyerShareCode);
};

export async function bindWechatAppId() {
  // #ifdef MP-WEIXIN
  const code = await checkAppLoginAndGetCode();

  await bindWechatApplet({
    code,
  });
  // #endif
}

/** 如果从链接获取到token，则免登 */
export function getTokenLogin(token) {
  // 设置用户信息
  setUserTokenStorage(token);
  store.dispatch("user/setToken", token);
  // 免登埋点 需要在设置token后，方便获取用户信息
  buryPointChannelBasics({
    code: "LAW_APPLET_FROM_H5_NO_LOGIN_SUCCESS",
    behavior: BURY_POINT_CHANNEL_TYPE.CK,
    type: 1,
  });
  bindWechatAppId();

  // 刷新用户信息
  store.dispatch("user/setUserInfo");
  store.dispatch("im/userLogin");
}

/**
 * 获取广告回传参数 新流程需要使用
 * https://lanhuapp.com/web/#/item/project/product?pid=aff2057b-63e8-4432-bbe5-92da4cfecbe1&image_id=35a5d641-6c07-4f72-849e-d4801f03a41b&tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=6cdef7ce-4813-4f93-bafb-f021dc58212b&docId=35a5d641-6c07-4f72-849e-d4801f03a41b&docType=axure&pageId=b9529110ae924f418039c12540b17730&parentId=2a47e437-b9ef-45a1-99c0-85da5c4ba4c3
 */
export function getClickId() {
  const loginQuery = uni.getStorageSync("loginQuery");

  if (loginQuery) {
    return loginQuery.clickId;
  }

  return null;
}

/** B 转换为 KB */
export function bToKB(fileSize) {
  // 单位为B
  let num = Number(fileSize || 0);

  if (num === 0) {
    return 0;
  }

  // B 转换为 KB 如果大于1024KB 则转换为MB 保留一位小数
  num = num / 1024;

  if (num > 1024) {
    num = num / 1024;
    num = Number(num.toFixed(1)) + "MB";
  } else {
    num = Number(num.toFixed(1)) + "KB";
  }

  return num;
}

/**
 * 抖音订单开始接口
 * 抖音支付流程更改后，需要用户手动点击使用
 * 所以这个接口在各个页面跳转以及app运行的时候会进行调用
 */
export function ttConfirmFulfillment() {
  // #ifndef MP-TOUTIAO
  // 如果不是抖音，默认成功
  return Promise.resolve();
  // #endif

  // #ifdef MP-TOUTIAO
  return new Promise((resolve, reject) => {
    // 如果登录了才调用
    if (store.getters["user/getToken"] || getUserTokenStorage()) {
      console.log("抖音订单开始接口调用");
      getLatestDouYinTradeOrder().then(({ data }) => {
        if (!isObjNull(data)) {
          const { myOrderId, ...confirmData } = data;

          console.log(confirmData, "开始确认订单");
          tt.confirmFulfillment({
            ...confirmData,
            success: (res) => {
              console.log("confirmFulfillment success", res);
              // 调用成功后将标记传给后端
              douYinTradeOrderConfirmStartService({
                myOrderId,
              }).then((r) => {
                console.log(r);
                return resolve();
              });
            },
            fail: (res) => {
              console.log("confirmFulfillment fail", res);
              return reject(res);
            },
            complete: (res) => {
              // 这里是因为抖音的弹窗消失时会触发onShow事件，所以这里加一个标记，让onShow不能频繁触发，频繁触发会导致im报错
              setIsOnShow(false);
              console.log("confirmFulfillment complete", res);
              setTimeout(() => {
                setIsOnShow(true);
              }, 1000);
            },
          });
        } else {
          // 如果没有待确认的订单，直接返回成功
          return resolve();
        }
      });
    } else {
      return reject();
    }
  });
  // #endif
}

/** 下单公共咨询 */
export const defaultPublicConsultationPay = async ({ serviceCode }) => {
  const params = {
    info: "用户未输入问题描述",
    typeLabel: "综合咨询",
    typeValue: 26,
    type: 3,
  };

  await saveParalegalData(params);

  const payFn = () => {
    toConfirmOrder({
      serviceCode,
      synHistoryId: getParalegalDataId(),
    });
  };

  payFn();

  return payFn;
};

/**
 * @description 获取本地律师 找市->省->全国律师
 * @date 2024/4/11
 */
export const getLocalLawyer = (data = {}, options = {}) => {
  let cityParams = [];

  // 是否请求全部律师
  const { all } = options;

  const getLawyerList = (lawyerParams) => {
    return lawyerListV3(lawyerParams).then((res) => {
      return res || [];
    });
  };

  const loopLawyerList = () => {
    const cityInfo = (cityParams || []).shift() || {};

    console.log(cityInfo, cityParams, "cityParams");

    return getLawyerList({
      ...data,
      ...cityInfo,
    }).then((res) => {
      const list = res.data.records || [];

      // 判断需要的律师没有 进行下一个城市类型查询
      if (isArrNull(list) && !isNull(cityInfo)) {
        return loopLawyerList();
      }

      return res;
    });
  };

  if (isArrNull(cityParams)) {
    if (all) {
      return getLawyerList(data);
    }

    return getLocation()
      .then((local) => {
        cityParams = [
          {
            cityCode: local.cityCode,
          },
          {
            proviceCode: local.provinceCode,
          },
          {},
        ];

        return loopLawyerList();
      })
      .catch(() => {
        return getLawyerList(data);
      });
  }

  return loopLawyerList();
};

/** 以5分钟为一个间隔，读取当前时间，如果未到5分钟展示为XX:X5，超过五分钟则展示为XX:X0 */
export const getNextFiveMinuteInterval = (intervalNum = 5) => {
  /**/
  const date = new Date();
  /* 当前分钟*/
  const currentMinutes = date.getMinutes();
  /* 当前小时*/
  let currentHour = date.getHours();
  /* 间隔分钟数*/
  const intervalMinutes = intervalNum - (currentMinutes % 5);
  /* 下个分钟*/
  let nextMinute = 0;
  /* 处理边界值 比如8：55分输出9：00 分钟置零 小时++*/
  if (currentMinutes >= 55) {
    if (currentHour === 23) {
      currentHour = -1;
    }
    nextMinute = 0;
    currentHour++;
  } else {
    nextMinute = currentMinutes + intervalMinutes;
  }
  return `${currentHour < 10 ? "0" + currentHour : currentHour}:${
    nextMinute < 10 ? "0" + nextMinute : nextMinute
  }`;
};

/** 生成一个4000-5000的随机数 */
export const createRandomNum = () => {
  return Math.floor(Math.random() * 1000 + 4000);
};

export function hideTabBar() {
  uni.hideTabBar({
    animation: false,
    success() {
      console.log("调用成功");
    },
  });
}

/**
 * 根据对象和键获取值
 * 如果给定的键存在于对象中，则返回该键对应的值
 * 如果键不存在，则返回对象中的"default"键对应的值
 * 这样做是为了确保总是有一个默认值可以返回，避免返回undefined
 *
 * @param {Object} obj - 包含键值对的对象
 * @param {string} key - 需要获取值的键名
 * @returns {any} - 键对应的值或者"default"键对应的值
 */
export function getValueByObjKey(obj, key) {
  return obj[key] || obj["default"];
}

export function jsonParse(str) {
  if (typeof str !== "string") return str;

  try {
    return JSON.parse(str);
  } catch (e) {
    return str;
  }
}

export function pxToScreenPx(px) {
  return (px / 375) * (uni.getWindowInfo?.()?.windowWidth || 375);
}

// 给一个当前金额，利率  利率百分数
// 考虑精度问题最后是数字类型
export function getAmountRate({ currentAmount, rate }) {
  // 解决精度问题
  if (isNull(currentAmount) || isNull(rate)) return 0; // 使用toFixed保留两位小数，然后转换为数字类型
  const result = (currentAmount * (rate / 100)).toFixed(2);
  return parseFloat(result);
}

// 根据金额和规则计算律师费
// rules规则是一个数组，每个元素是一个对象，对象的属性是金额和利率 还有金额区间
// 金额是数字类型，利率是数字类型
// 金额区间是当前金额需要匹配的规制 最大区间没有就标识最大是无限
// 返回和getMinMaxRate一样的结果
export function calculateLawyerFee({ amount, rules, id }) {
  // 如果传入了id 就直接匹配id 对应的规制数据 返回当前数据的最大金额 最小金额
  if (!isNull(id)) {
    const rule = rules.find((rule) => rule.id === Number(id));
    if (!rule) return [0];
    return [rule.minAmount, rule.maxAmount];
  }
  if (isNull(amount)) return [0];
  const amountToNum = Number(amount);
  const rule = rules.find((rule) => {
    const { minAmount, maxAmount } = rule;
    if (!isNull(minAmount) && !isNull(maxAmount)) {
      return amountToNum >= minAmount && amountToNum <= maxAmount;
    }
    if (!isNull(minAmount)) {
      return amountToNum >= minAmount;
    }
    if (!isNull(maxAmount)) {
      return amountToNum <= maxAmount;
    }
  });
  if (!rule) return [0];
  const { minRate, maxRate } = rule;
  const min = getAmountRate({ currentAmount: amountToNum, rate: minRate });
  if (min < rule.minLawyerFee) return [rule.minLawyerFee];
  const max = getAmountRate({ currentAmount: amountToNum, rate: maxRate });
  if (minRate === maxRate) return [min];
  else return [min, max];
  // 根据金额和规则计算律师费
}

// 金额每3位加一个逗号 考虑小数点
export function formatAmount(amount) {
  return Number(amount).toLocaleString("en-US");
}

// 匹配字典里面的值
export function matchDictValue({
  key = "value",
  value,
  groupCode = "LAWYER_SPECIALITY",
} = {}) {
  if (isNull(groupCode)) return Promise.reject();
  return dataDictionary({ groupCode }).then(({ data }) => {
    if (isArrNull(data)) return Promise.reject();
    const result = data.find((item) => item[key] === value);
    if (!result) return Promise.reject();
    return Promise.resolve(result);
  });
}

/** 跳转到微信客服 */
export async function toWeChatCustomerService() {
  // #ifdef MP-WEIXIN
  uni.openCustomerServiceChat({
    extInfo: { url: "https://work.weixin.qq.com/kfid/kfcc80a9b01943892b0" },
    corpId: "ww4dda508a5b6ab170",
    complete(res) {
      console.log("openCustomerServiceChat complete", res);
    },
  });
  // #endif

  // #ifndef MP-WEIXIN
  // 拨打客服电话
  try {
    const { data } = await getCommonConfigKey({ paramName: "CUSTOM_USER_SERVICE_PHONE" });
    if (data && data.paramValue) {
      uni.makePhoneCall({
        phoneNumber: data.paramValue,
        success: (res) => {
          console.log("Customer service call initiated successfully", res);
        },
        fail: (err) => {
          console.error("Failed to make phone call", err);
          uni.showToast({
            title: "拨打电话失败",
            icon: "none",
            duration: 2000,
          });
        }
      });
    } else {
      console.error("Customer service phone number not found");
      uni.showToast({
        title: "客服电话号码未配置",
        icon: "none",
        duration: 2000,
      });
    }
  } catch (error) {
    console.error("Error fetching customer service phone number", error);
    uni.showToast({
      title: "获取客服电话失败",
      icon: "none",
      duration: 2000,
    });
  }
  // #endif
}

/**
 * 显示线索拦截弹窗
 * https://lanhuapp.com/web/#/item/project/product?pid=b9fd9e6c-c356-4cb9-8231-f7273a41b10d&image_id=70148422-ebb3-4e44-9428-1e1770fe9380&tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=f3679f86-2421-4ea4-91b3-d26a5c4f6769&docId=70148422-ebb3-4e44-9428-1e1770fe9380&docType=axure&pageId=8ebb3a3a4eea4c32bb09905458c9c35b&parentId=7169cfbe-e101-4cf0-aefb-54fbf4552c21
 * @param {Object} options 选项对象
 * @param {Function} [options.confirmCallback] 确认按钮点击后的回调方法
 * @param {Function} [options.cancelCallback] 取消按钮点击后的回调方法
 * @param {string} [options.info] 弹窗显示的信息内容
 */
export function showLeadInterceptPopup({
  confirmCallback,
  cancelCallback,
  info,
  key,
} = {}) {
  serializationDictionary(() =>
    dataDictionary({ groupCode: "QUICK_MESSAGE_POPUP_CONFIG" })
  )
    .then((data) => {
      return data.find((item) => item.value === key)?.supplementDesc || {};
    })
    .then((result) => {
      console.log("result", result);
      if (result.show) {
        store.dispatch("leadInterceptPopup/showPopup", {
          confirmCallback,
          cancelCallback,
          info,
          result,
        });
      } else {
        confirmCallback?.();
      }
    });
}
