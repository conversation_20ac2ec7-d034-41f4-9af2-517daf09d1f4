<template>
  <div>
    <!-- 返回拦截弹窗 -->
    <app-popup
      :closeOnClickOverlay="false"
      :safeAreaInsetBottom="true"
      :show="show"
      :zIndex="9990"
      mode="center"
      showCancelButton
      @cancel="close"
    >
      <div>
        <img
          class="w-[311px] h-[234px]"
          src="@/components/login/imgs/<EMAIL>"
          alt=""
          @click.stop="jump"
        >
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import { toConsultationOrOrder } from "@/libs/toConsultationOrOrder";

export default {
  name: "PaidPopup",
  components: { AppPopup },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    serviceInfo: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    /** 弹窗显示 */
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
  watch: {
    show() {
    }
  },
  methods: {
    close() {
      this.show = false;
    },
    jump() {
      this.show = false;
      toConsultationOrOrder({ type: this.serviceInfo.buyServiceType });
    }
  }
};
</script>
