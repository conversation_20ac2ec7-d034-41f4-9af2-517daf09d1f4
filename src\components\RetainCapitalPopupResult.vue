<template>
  <div>
    <app-popup
      :show="show"
      :zIndex="2002"
      mode="center"
      showCancelButton
      @cancel="handleCancel"
    >
      <div class="w-[311px] bg-[#FFFFFF] rounded-[16px] text-center box-border">
        <if t="hjls">
          <img
            alt=""
            class="w-[311px] h-[149px] block"
            src="@/pages/submit-question/findlawyer/imgs/Group8266.png"
          >
        </if>
        <if f="hjls">
          <img
            alt=""
            class="w-[311px] h-[149px] block"
            src="@/pages/submit-question/findlawyer/imgs/Group18266.png"
          >
        </if>
        <div class="p-[0_24px_24px_24px]">
          <div class="font-bold text-[16px] text-[#333333] mt-[3px]">
            已有3-5位律师收到您的咨询
          </div>
          <div class="text-[13px] text-[#999999]">
            预计三分钟内应答
          </div>

          <div class="text-[14px] text-[#333333] mt-[16px]">
            {{ text }}
          </div>
          <div
            class="w-[263px] h-[40px] bg-[#3887F5] rounded-[22px] font-bold text-[15px] text-[#FFFFFF] flex items-center justify-center mt-[16px]"
            @click="turnPage"
          >
            点击前往
          </div>
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import { toConsultation } from "@/libs/turnPages";
import { caseSourceV2GetCaseOrZx } from "@/api/special";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";

export default {
  name: "RetainCapitalPopupResult",
  components: {
    AppPopup
  },
  props: {
    value: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  data() {
    return {
      resultData: {}
    };
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    },
    isCase() {
      return Number(this.resultData.type) === 1;
    },
    text() {
      return this.isCase
        ? "您可前往我的-免费咨询查看其他律师回复"
        : "您可前往我的-一问多答查看其他律师回复";
    }
  },
  watch: {
    show: {
      handler(val) {
        if (val) {
          buryPointChannelBasics({
            code:
              "LAW_APPLET_FIND_LAWYER_STAY_BOTTOM_LEFT_INFO_POP_UP_LEFT_SUCCESS_PAGE",
            behavior: BURY_POINT_CHANNEL_TYPE.VI,
            type: 1
          });

          buryPointChannelBasics({
            code:
              "LAW_APPLET_AFTER_PAY_SECOND_CONSULT_POPUP_PAGE_LEAVE_INFO_SUCCESS",
            behavior: BURY_POINT_CHANNEL_TYPE.VI,
            type: 1
          });

          this.getDelegationInfo();
        }
      },
      immediate: true
    }
  },
  methods: {
    handleCancel() {
      this.show = false;
    },
    /** 获取留资信息 */
    getDelegationInfo() {
      caseSourceV2GetCaseOrZx().then(res => {
        this.resultData = res.data;
      });
    },
    /** 点击跳转 */
    turnPage() {
      this.isCase ? toConsultation({ index: 2 }) : toConsultation({ index: 0 });
    }
  }
};
</script>
