<template>
  <div class="wrap">
    <div class="main">
      <div class="pay-img">
        <img
          alt=""
          class="img"
          src="./imgs/success.png"
        >
      </div>
      <div class="info">
        <p class="p">
          支付成功
        </p>
      </div>
      <template v-if="[3].includes(orderInfo.buyServiceType)">
        <!--  抖音小程序  -->
        <div
          v-if="isToutiao"
          class="msg"
        >
          <div class="another-info">
            <span v-if="timeIsNight">
              当前为非工作时间段（{{
                `${_startTime}-${_endTime}`
              }}），值班律师较少。您的问题平台已加急派送，<span
                class="text-[#3887f5]"
              >律师电话</span>稍后将以<span class="text-[#3887f5]">短信</span>形式发送给您，请注意查收
            </span>
            <span v-else>
              支付成功，等待律师接单，<span class="text-[#3887f5]">律师电话</span>稍后将以<span class="text-[#3887f5]">短信</span>形式发送给您，请注意查收
            </span>
          </div>
        </div>
        <!-- 其它小程序-->
        <div
          v-else
          class="msg"
        >
          <p>
            您已成功购买一对一咨询服务，律师将尽快与您联<br>系，可前往
            <span class="text-[#3887f5] mx-[3px]">我的咨询</span> 查看律师接单进度
          </p>
        </div>
      </template>
      <div
        v-if="[4, 6].includes(orderInfo.buyServiceType)"
        class="msg"
      >
        <p>感谢您的认可，请继续深入追问</p>
      </div>
      <div
        v-if="[7].includes(orderInfo.buyServiceType)"
        class="msg"
      >
        <p>
          您的问题，已进入平台快速咨询通道，律师会在稍后几分钟为您解答，为了第一时间收到消息通知，建议您订阅小程序消息哦
        </p>
      </div>
      <div
        v-if="[8].includes(orderInfo.buyServiceType)"
        class="msg"
      >
        <p>律师已收到您的打赏，感谢您对律师和平台的支持</p>
      </div>
      <div
        v-if="[1].includes(orderInfo.buyServiceType)"
        class="msg"
      >
        <p>
          您已购买
          <span class="text-[#3887f5] mx-[3px]">{{ lawyerSessionData.lawyerName || "" }}</span>
          {{
            timeIsNight
              ? `律师1V1服务，当前为非工作时间段（${_startTime}-${_endTime}）`
              : "律师的服务"
          }}，<span class="text-[#3887f5]">律师电话</span>稍后将以<span
            class="text-[#3887f5]"
          >短信</span>形式发送给您，请注意查收
        </p>
      </div>
    </div>
    <!-- <div class="btn-container">
      <div
        v-if="!isPayOrder"
        class="btn"
        @click="jump"
      >
        <p class="download-text">
          {{ showData.msg || "" }}
        </p>
      </div>
    </div> -->
    <div
      v-if="isPayOrder"
      class="w-[327px] h-[44px] bg-[#3887F5] flex items-center justify-center rounded-[16px] mt-[16px] mx-auto"
      @click="handleJump"
    >
      <div class="text-[14px] text-[#FFFFFF]">
        {{ countDown }}秒后自动跳转
      </div>
    </div>
    <if f="hjls">
      <!-- #ifdef MP-WEIXIN -->
      <div
        v-if="![8].includes(orderInfo.buyServiceType)"
        class="fixed left-0 bottom-0 fixed-btn"
        @click.stop="toOfficial"
      >
        <img
          class="w-[375px] h-[226px]"
          src="@/pages/pay/imgs/<EMAIL>"
          alt=""
        >
        <img
          class="button-animation absolute z-[1] w-[351px] h-[64px] top-[146px] left-[12px]"
          src="@/pages/pay/imgs/<EMAIL>"
          alt=""
        >
        <u-safe-bottom />
      </div>
      <!-- #endif -->
      <retain-capital-popup-result v-model="resultPopup" />
    </if>
    <service-complaint-card />
    <!--    <div class="btn-container">-->
    <!--      <div class="btn my-order" @click="toMyOrder">-->
    <!--        <p class="download-text">我的订单查看</p>-->
    <!--      </div>-->
    <!--    </div>-->
  </div>
</template>

<script>
import { buryPointChannelBasics } from "@/api/burypoint.js";
import { BURY_POINT_CHANNEL_TYPE, POINT_CODE } from "@/enum/burypoint.js";
import { caseSourceOpen, getServerAndImInfo, servicePayStatus } from "@/api";
import {
  toConsultation,
  toImChatPage,
  toMyQuestion,
  toOfficialAccountMini,
} from "@/libs/turnPages.js";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath.js";
import { requestSubscribeMessage } from "@/libs/tools";
import nightTime from "@/mixins/nightTime";
import { getReturnRouterStorage } from "@/libs/token";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import RetainCapitalPopupResult from "@/components/RetainCapitalPopupResult.vue";
import { caseSourceV2GetCaseOrZx } from "@/api/special";
import ServiceComplaintCard from "@/pages/pay/ServiceComplaintCard/index.vue";
export default {
  components: { RetainCapitalPopupResult, USafeBottom, ServiceComplaintCard },
  mixins: [nightTime],
  data() {
    return {
      orderId: "",
      orderInfo: {},
      lawyerSessionData: {},
      isToutiao: false,
      resultPopup: false,
      countDown: 10,
      timer: null,
    };
  },
  onLoad(option) {
    this.queryOrder(option?.id);
    buryPointChannelBasics({
      code: "LAW_PAGE_ORDER_PAY_SUCCESS",
      behavior: BURY_POINT_CHANNEL_TYPE.VI,
      type: 1,
    });

    buryPointChannelBasics({
      code: POINT_CODE.LAW_APPLET_PAY_SUCCESS,
      behavior: BURY_POINT_CHANNEL_TYPE.VI,
      type: 1,
      extra: {
        // 3.0.9版本新增，溯源是哪个界面支付的
        lastPagePath: getReturnRouterStorage(),
      },
    }).finally(() => {
      buryPointTransformationPath.clear();
    });
  },
  onShow() {
    // #ifdef MP-TOUTIAO||MP-WEIXIN
    // 图文或电话咨询的展示在抖音小程序中有区别
    this.isToutiao = true;
    // #endif

    // 清除订阅消息状态
    uni.removeStorageSync("subscribe");
  },
  computed: {
    showData() {
      const { buyServiceType, businessId, createFrom } = this.orderInfo;
      let data = {};
      switch (Number(buyServiceType)) {
      case 1:
        data = {
          msg: "我知道了",
          turnPage: () => {
            /* 私聊直接跳转会话*/
            if (createFrom === 7) {
              this.toPage();
            } else {
              toConsultation({
                index: 1,
              });
            }
          },
        };
        break;
      case 2:
        data = {
          msg: "查看案件详情",
          path: `/pages/case-details/index?id=${businessId}`,
        };
        break;
      case 3:
        data = {
          msg: "查看我的咨询",
          turnPage: () => {
            toConsultation({
              index: 1,
            });
          },
        };
        break;
      case 4:
        data = { msg: "进入会话" };
        break;
      case 6:
        data = { msg: "进入会话" };
        break;
      case 7:
        data = {
          msg: "我知道了",
          turnPage: () => {
            toMyQuestion(
              {
                id: this.orderInfo.businessId,
              },
              true
            );
          },
        };
        break;
      case 8:
        data = { msg: "进入会话" };
        break;
      default:
        data = {
          msg: "查看案件详情",
          path: `/pages/case-details/index?id=${businessId}`,
        };
        break;
      }
      console.log(data);
      return data;
    },
    /** 是否是平台或者1v1付费订单 */
    isPayOrder() {
      return [1, 3].includes(this.orderInfo.buyServiceType);
    },
  },
  beforeDestroy() {
    if (this.timer) {
      console.log("clearInterval");
      clearInterval(this.timer);
      this.timer = null;
    }
  },
  methods: {
    /** 查询订单 获取数据 */
    async queryOrder(id) {
      const obj = await servicePayStatus({ orderId: id });
      this.orderInfo = obj?.data || {};
      console.log(obj);
      this.orderInfo.buyServiceType = Number(this.orderInfo.buyServiceType);
      // 获取会话信息
      if ([1, 4, 6, 8].includes(this.orderInfo.buyServiceType)) {
        this.getSession();
      }
      if (this.isPayOrder) {
        this.startCountDown();
      }
    },
    /* 获取律师和会话信息 */
    async getSession() {
      const { lawyerId, businessId, createFrom } = this.orderInfo;
      try {
        let params = {
          lawyerId,
          caseSourceServerV2Id: businessId,
          createFrom,
        };
        let res = await getServerAndImInfo(params);
        this.lawyerSessionData = res.data || {};
        console.log("lawyerSessionData:", this.lawyerSessionData);
      } catch (error) {
        console.log(error);
      }
    },
    /** 去我的订单列表 */
    toMyOrder() {
      uni.navigateTo({ url: "/pages/myorder/index" });
    },
    /** 动态地址的跳转 */
    jump() {
      if (this.isPayOrder) {
        buryPointChannelBasics({
          code: "LAW_PAGE_PAY_ORDER_SUCCESS_PAGE_LAWYER_ANSWER_CLICK",
          type: 1,
          behavior: BURY_POINT_CHANNEL_TYPE.CK,
        });
      }

      if (this.showData.turnPage) {
        this.showData.turnPage();
        return;
      }

      if (this.showData.path)
        return uni.navigateTo({ url: this.showData.path });

      this.toPage();
    },
    /* 会话 */
    toPage() {
      toImChatPage({
        lawyerId: this.lawyerSessionData.lawyerId,
        conversationId: this.lawyerSessionData.imSessionId,
        caseSourceId: this.lawyerSessionData.caseSourceServerV2Id,
      });
    },
    subscribe() {
      requestSubscribeMessage({
        wechatTmplIdList: [
          "gt0vzpdGDhUWNaQMnCbYyzsOs1ONl2K7RlALNUIxiUE",
          "JQNQuiH_edgmspPvsS4E6sP6teq2HADRpMwBYoUy2ac",
          "gt0vzpdGDhUWNaQMnCbYy0rOd4fjRKf9vi-GFDr06X8"
        ]
      }).then((res) => {
        this.jump();
      });
    },
    toOfficial() {
      buryPointChannelBasics({
        code: "LAW_PAGE_PAY_ORDER_SUCCESS_PAGE_PUBLIC_ACCOUNT_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
      });

      toOfficialAccountMini();
    },
    /** 点击重新留资 */
    toRetainCapital() {
      buryPointTransformationPath.add(5042);

      buryPointChannelBasics({
        code: "LAW_PAGE_PAY_ORDER_SUCCESS_PAGE_INVITE_LAWYER_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
      });

      caseSourceV2GetCaseOrZx().then((res) => {
        const type = Number((res.data || {}).type);

        if (type === 0) {
          // 当没有留资的时候才会调用
          caseSourceOpen().then(() => {
            uni.showToast({
              title: "正在邀请律师，请等待",
              duration: 3500,
              icon: "none",
              mask: true,
            });

            setTimeout(() => {
              caseSourceV2GetCaseOrZx().then((res) => {
                const type = Number((res.data || {}).type);

                if (type === 0) {
                  uni.showToast({
                    title: "没有有效咨询内容，前往快速咨询可让其他律师解答",
                    duration: 4000,
                    icon: "none",
                  });
                } else {
                  buryPointChannelBasics({
                    code: "LAW_PAGE_PAY_ORDER_SUCCESS_PAGE_LIVE_IN_SUCCESS",
                    type: 1,
                    behavior: BURY_POINT_CHANNEL_TYPE.CK,
                  });

                  this.resultPopup = true;
                }
              });
            }, 4000);
          });
        } else {
          uni.showToast({
            title: "已经为您邀请了其他律师了",
            icon: "none",
            duration: 2000,
          });
        }
      });
    },
    handleJump() {
      toConsultation({ index: 1 }, true);
    },
    startCountDown() {
      this.countDown = 10;
      this.timer = setInterval(() => {
        this.countDown--;
        if (this.countDown <= 0) {
          clearInterval(this.timer);
          this.handleJump();
        }
      }, 1000);
    },
  },
};
</script>

<style lang="scss" scoped>
.wrap {
  background: #ffffff !important;
  min-height: 100vh;
  //overflow: hidden;
}

.next_action {
  position: absolute;
  width: 10px;
  height: 44px;
  left: 131px;
}

.next {
  position: absolute;
  width: 10px;
  height: 44px;
  left: 239px;
}

.main {
  width: 319px;
  padding-top: 44px;
  margin-left: 28px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .pay-img {
    height: 120px;

    .img {
      width: 120px;
      height: 120px;
    }
  }

  .info {
    // flex: 1;
    padding-top: 12px;
    width: 255px;
    height: 22px;
    font-size: 18px;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: bold;
    color: #333333;
    line-height: 19px;

    .p {
      text-align: center;
    }
  }

  .msg {
    width: 319px;
    //height: 40px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 22px;
    text-align: center;
    margin-top: 16px;

    .another-info {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 22px;
    }
  }
}

.btn-container {
  width: 100%;
  display: flex;
  justify-content: center;
  //margin-bottom: 16px;
  flex-direction: column;
  align-items: center;

  .btn {
    //width: 160px;
    //height: 32px;
    padding: 7px 24px;
    border-radius: 68px 68px 68px 68px;
    border: 1px solid #eeeeee;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 24px;

    &.toutiao {
      margin-top: 40px;
    }

    .download-text {
      height: 20px;
      font-size: 14px;
      font-weight: 400;
      color: #3887f5;
      line-height: 20px;
    }

    &.my-order {
      background: #ffffff !important;
      border: 1px solid #cccccc;

      .download-text {
        color: #333333;
        font-weight: 400;
      }
    }
  }

  .btn-text {
    font-size: 12px;
    font-weight: 400;
    color: #3887f5;
    margin-top: 8px;
    text-align: center;
  }
}

.step {
  width: 288px;
  height: 70px;
  margin-left: 43px;
  margin-top: 160px;
  display: flex;
  justify-content: space-between;

  &-left {
    width: 65px;
    height: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .img {
      margin-bottom: 8px;
      width: 44px;
      height: 44px;
    }

    .span {
      // width: 61px;
      height: 18px;
      font-size: 13px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #3887f5;
      line-height: 15px;
    }
  }

  &-center {
    // width: 61px;
    height: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .img {
      margin-bottom: 8px;
      width: 44px;
      height: 44px;
    }

    .span {
      // width: 61px;
      height: 18px;
      font-size: 13px;
      font-weight: 400;
      color: #3887f5;
      line-height: 15px;
    }
  }

  &-right {
    width: 61px;
    height: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .img {
      margin-bottom: 8px;
      width: 44px;
      height: 44px;
    }

    .error-span {
      width: 66px;
      height: 18px;
      font-size: 13px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 15px;
    }
  }
}
.fixed-btn {
  transform: translateY(100%);
  animation: 1s animation1shape2 forwards;
}
/*滑动出来*/
@keyframes animation1shape2 {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}
</style>
