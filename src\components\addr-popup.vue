<template>
  <div />
  <!-- <van-popup
    :class="{ 'popup-order-pc': !isMobile() }"
    :close-on-click-overlay="false"
    :value="show"
    get-container="body"
    position="bottom"
    round
  >
    <div class="actions flex flex-space-between">
      <span class="cursor-pointer" @click="$emit('update:show', false)"
        >取消</span
      >
      <p class="title">案件地点</p>
      <span
        :style="{ color: activeColor }"
        class="cursor-pointer confirm"
        @click="Confirm"
        >确定</span
      >
    </div>
    <van-cascader
      v-model="cascaderValue"
      :active-color="activeColor"
      :closeable="false"
      :field-names="fieldNames"
      :options="options"
      @change="onChange"
      @close="$emit('update:show', false)"
      @finish="cascaderConfirm"
    />
  </van-popup> -->
</template>

<script>
// import { Cascader, Popup } from "vant";
import { currencyGetAddress, getArea } from "@/api";
import { isMobile } from "@/libs/tool";

export default {
  name: "AddrPopup",
  components: {
    // "van-popup": Popup,
    // "van-cascader": Cascader,
  },
  props: {
    activeColor: {
      type: String,
      default: "#F2AF30",
    },
    show: {
      type: Boolean,
      default: false,
    },
    AfieldNames: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      fieldNames: {
        text: "name",
        value: "code",
        children: "children",
      },
      selectedOptions: [],
      options: [],
      cascaderValue: "",
      selectedCodes: [],
    };
  },
  mounted() {
    this.getArea(1).then(({ data }) => {
      this.options = data;
    });
  },
  methods: {
    isMobile,
    getArea(type, code) {
      return getArea({ type, code });
    },
    onChange({ value, tabIndex }) {
      if (tabIndex === 1) return false;
      if (this.selectedCodes.indexOf(value) > -1) return false;
      const currentCityIndex = this.options.findIndex(
        (item) => item.code === value
      );
      const currentCity = this.options[currentCityIndex];
      this.selectedCodes.push(value);
      this.getArea(2, currentCity.code).then(({ data }) => {
        this.$set(this.options[currentCityIndex], "children", data);
      });
    },
    cascaderConfirm({ selectedOptions }) {
      this.selectedOptions = selectedOptions;
    },
    Confirm() {
      if (this.selectedOptions.length > 1) {
        const data = this.selectedOptions[this.selectedOptions.length - 1];
        let label = data.provinceName + data.name;
        let value = data.code;
        // 省和市 直辖市两个值相同 只显示一个
        if (data.code === data.province) {
          label = data.provinceName;
          value = data.code;
        }
        this.$emit("onAddrConfirm", {
          [this.AfieldNames.label || "label"]: label,
          [this.AfieldNames.value || "value"]: value,
        });
      }
      this.$emit("update:show", false);
    },
    turnOnTargeting() {
      currencyGetAddress().then(({ data }) => {
        this.selectedOptions = [
          {
            province: data.provinceCode,
            provinceName: data.provinceName,
            code: data.cityCode,
            name: data.cityName,
          },
          {
            province: data.provinceCode,
            provinceName: data.provinceName,
            code: data.cityCode,
            name: data.cityName,
          },
        ];
        this.Confirm();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-order-pc {
  width: 10.5rem;
  left: 50%;
  transform: translateX(-50%);
}
.actions {
  height: 48px;
  box-sizing: border-box;
  padding: 14px 16px;
  background: #ffffff;
  border-radius: 16px 16px 0 0;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #969799;
  line-height: 20px;
  margin-bottom: -48px;
  border-bottom: 1px solid #eeeeee;
  p {
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: bold;
    color: #323233;
    line-height: 22px;
  }
}
</style>
