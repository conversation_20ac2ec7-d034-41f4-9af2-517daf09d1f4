const API_VERSION = require("../src/libs/apiVersion.json");

/**
 * 获取小程序产品版本号
 * @param {string} platName
 * @returns {*|string}
 */
function getMiniVersion(platName) {
  const plat = platName.split("-");

  for (const item of plat) {
    if (API_VERSION[item]) {
      return API_VERSION[item].apiVersion;
    }
  }

  return API_VERSION.origin.apiVersion;
}

const getCmdOption = () => {
  const arr = process.argv.slice(2); // 获取命令行参数数组
  const r = arr.reduce((pre, item) => {
    // 使用reduce方法对参数数组进行处理
    if (item.indexOf("=") !== -1) {
      // 判断参数是否有等号
      return [...pre, item.split("=")]; // 将带有等号的参数进行分割并添加到结果数组中
    }
    return pre; // 否则返回原结果数组
  }, []);
  // 将结果数组转化为参数对象
  return Object.fromEntries(r); // 返回参数对象
};
module.exports = {
  getMiniVersion,
  getCmdOption,
};
