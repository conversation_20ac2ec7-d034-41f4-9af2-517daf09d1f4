
/** 获取录音权限 */
export const getRecord = () => {
  return new Promise((resolve, reject) => {
    uni.authorize({
      scope: "scope.record",
      success: (res) => {
        console.log("录音授权成功：", res);
        if (res.errMsg === "authorize:ok") {
          resolve(res);
        } else {
          reject(res);
        }
      },
      fail: (err) => {
        console.log("录音授权失败：", err);
        reject(err);
      }
    });
  });
};
