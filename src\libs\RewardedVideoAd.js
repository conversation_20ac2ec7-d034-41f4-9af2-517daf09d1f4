class RewardedVideoAd {
  constructor() {
    this._rewardedVideoAd = null;
    this.callbackQueue = [];
  }

  set rewardedVideoAd(ad) {
    if(!ad) {
      console.error("RewardedVideoAd instance is null");

      return;
    }

    this._rewardedVideoAd = ad;

    this.callbackQueue.forEach((item) => {
      this._rewardedVideoAd[item.type](item.callback);
    });
  }

  get hasAd() {
    return !!this._rewardedVideoAd;
  }

  /** 清除广告 */
  clear() {
    this._rewardedVideoAd = null;
  }

  onLoad(callback) {
    if (this._rewardedVideoAd) {
      this._rewardedVideoAd.onLoad(callback);
    } else {
      this.callbackQueue.push({
        type: "onLoad",
        callback
      });
    }
  }

  onError(callback) {
    if (this._rewardedVideoAd) {
      this._rewardedVideoAd.onError(callback);
    } else {
      this.callbackQueue.push({
        type: "onError",
        callback
      });
    }
  }

  onClose(callback) {
    if (this._rewardedVideoAd) {
      this._rewardedVideoAd.onClose(callback);
    } else {
      this.callbackQueue.push({
        type: "onClose",
        callback
      });
    }
  }

  show() {
    if (this._rewardedVideoAd) {
      this._rewardedVideoAd.show().catch(() => {
        // 重新读取一次广告
        this._rewardedVideoAd.load().then(() => {
          // 重新显示广告
          this._rewardedVideoAd.show();
        });
      });
    } else {
      console.error("RewardedVideoAd instance is null");
    }
  }
}

export default RewardedVideoAd;
