import { requestCore, requestInfo, requestLawyer, requestCommon } from "@/libs/axios";

/* 用户基本信息*/
export const getUserInfo = (data) => requestCore.post("/user/info", data);

/* 用户基本信息更新*/
export const setUserInfoAsync = (data) =>
  requestCore.post("/user/info/update", data);

/* 用户协议*/
export const getUserAgreement = (data) => requestCore.post("/user/agreement", data);

/* 关注列表 */
export const userFollow = (data) => requestInfo.post("/user/userFollow", data);

/* 添加关注 */
export const clickfollow = (data) => requestInfo.post("/user/clickfollow", data);

/* 取消关注 */
export const removeFollow = (data) =>
  requestInfo.post("/user/removeFollow", data);

/* 用户关注列表 */
export const otherUserFollow = (data) =>
  requestInfo.post("/user/otherUserFollow", data);

/* 用户关注状态 */
export const followUserStatus = (data) =>
  requestInfo.post("/user/followUserStatus", data);



/* 关注用户文章列表 */
export const followUserArticle = (data) =>
  requestInfo.post("/user/followUserArticle", data);

/* 关注用户首页文章 */
export const followHome = (data) =>
  requestInfo.post("/user/followHome", data);

/* 用户粉丝列表 */
export const userFansList = (data) =>
  requestInfo.post("/user/userFansList", data);
/* 收藏列表 */
export const listCollect = (data) => requestInfo.post("/user/listCollectPage", data);

/* 添加收藏 */
export const clickCollect = (data) =>
  requestInfo.post("/user/clickCollect", data);

/* 取消收藏 */
export const removeCollect = (data) =>
  requestInfo.post("/user/removeCollect", data);

/* 消息查询*/
// export const messageList = (data) => requestCore.post("/message/list", data)

/* 作者信息 */
export const authBase = (data) => requestInfo.post("/author/base", data);


/** 文章标签搜索 */
export const articleTag = (data) =>
  requestInfo.post("/article/common/tags", data);

/* 用户消息未读取 */
export const noReadMessage = (data) => requestCore.post("/message/noRead", data);

/* 用户消息列表 */
export const messageList = (data) => requestCore.post("/message/page", data);

export const messagePage = (data) => requestCore.post("/message/list", data);

/** 通过设备号获取用户信息 */
export const getUserByDeviceId = (data) => requestCore.post("/user/getUserByDeviceId", data);

/** 通过设备号获取用户登录信息 */
export const getLoginByDeviceId = (data) => requestCore.post("/user/getLoginByDeviceId", data);

/**
 * 用户所有消息已读
 * https://showdoc.imlaw.cn/web/#/5/196
 */
export const hasReadMessage = (data) => requestCore.post("/message/all/read", data);

/* 获取IM*/
export const imLoadUser = (data) => requestCore.post("/im/loadUser", data);

/* 用户咨询列表 */
export const listConsulation = (data) => requestInfo.post("/caseSource/page", data);

/* 用户咨询列表 详情 */
export const listConsulationDetail = (data) => requestInfo.post("/caseSource/detail/" + data.caseSourceId, data);

/* 获取咨询律师回复列表 */
export const pageCaseSourceReply = (data) => requestInfo.post("/caseSource/pageCaseSourceReply", data);

/* 获取咨询律师推荐 */
export const getConsultLawyer = (data) => requestLawyer.post("/recommend/getConsultLawyer", data);

/* 获取当前用户的渠道ID*/
export const getDyXcxChannel = () => requestCore.post("/user/getDyXcxChannel");

/* 登录后的提示弹框 */
export const loginPopup = () => requestInfo.post("/caseSourceV2/login/newPopup");

/** 登录后的提示弹框 */
export const newPopupV2 = () => requestInfo.post("/caseSourceV2/login/newPopupV2");

/* 用户操作行为信息*/
export const  userActionInfo = () => requestInfo.post("/user/action/info");

/** 查询登录用户的手机号归属地 */
export const getPhoneAddress = () => requestCore.post("/currency/getPhoneAddress");

/** 用户获取案源服务律师手机号 */
export const getLawyerServerCall = (data) => requestInfo.post("/caseSourceServerV2/getLawyerServerCall", data);

/** 用户获取案源服务律师手机号 */
export const getDataPlatform = () => requestInfo.post("/user/getDataPlatform" );

/** 获取每日数据 */
export const getDayData = () => requestInfo.post("/data/getDayData" );

/** 72小时内：用户问答或咨询信息查询 */
export const wdOrZxInfo = () => requestInfo.post("/user/wdOrZxInfo" );

/** 免费私聊im创建 */
export const privateChatSaveAppoint = (data) => requestInfo.post("/privateChat/saveAppoint", data );


/** 律师分享绑定用户 */
export const bindRelation = (data) => requestCore.post("/user/bindRelation", data );

/** 无感绑定微信小程序 */
export const bindWechatApplet = (data) => requestCore.post("/user/wechat/bindWechatApplet", data );

/** 非流式简单AI回复单条-案源留资细类划分 */
export const unStreamLeftInfoCategory = (data) =>
  requestCommon.post("/info/aiEngineAsk/unStream/unStreamLeftInfoCategory", data);

/** 非流式简单AI回复单条 */
export const unStreamSimpleAiChat = (data) =>
  requestCommon.post("/info/aiEngineAsk/unStream/unStreamSimpleAiChat", data);

/** 根据关键词返回对应的工具类型 */
export const utilKeyWordsMatch = (data) =>
  requestCommon.post("/info/calculator/utilKeyWordsMatch", data);

/** 豆包ai引擎调用法律咨询意见书（非流式调用）每天一次没有订单 */
export const unStreamAskLegalOpinionWithOutOrder = (data) =>
  requestCommon.post("/info/aiEngineAsk/db/unStream/unStreamAskLegalOpinionWithOutOrder", data);

/** 
 * 根据案件类型判断高价值用户
 * https://showdoc.imlaw.cn/web/#/5/3791
 */
export const caseSourceDetermineHighValueByCaseType = (data) =>
  requestCommon.post("/info/caseSource/determineHighValueByCaseType", data);


/** 
 * 用户注销发送验证码
 * https://showdoc.imlaw.cn/web/#/5/3909
 */
export const loginOutSendSmsCode = (data) =>
  requestCommon.post("/info/loginOut/loginOutSendSmsCode", data);


/** 
 * 用户注销反馈
 * https://showdoc.imlaw.cn/web/#/5/3910
 */
export const userLoginOutFeedback = (data) =>
  requestCommon.post("/info/loginOut/userLoginOutFeedback", data);

