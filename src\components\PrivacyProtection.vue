<template>
  <div class="relative h-[25px]">
    <img
      alt=""
      class="w-[32px] h-[32px] absolute block -left-[16px] -top-[4px]"
      src="../pages/lawyer-im/assets/privacy.png"
      @click="toggleRightText"
    >
    <div
      v-if="showRightText"
      class="h-[25px] bg-[#FFFFFF] flex items-center rounded-[4px] pr-[8px]"
    >
      <div class="text-[12px] text-[#3887F5] ml-[24px]">
        {{ privacyText }}
      </div>
      <img
        alt=""
        class="w-[12px] h-[12px] ml-[2px]"
        src="../pages/lawyer-im/assets/jf6ta8.png"
        @click="toggleRightText"
      >
    </div>
  </div>
</template>

<script>
export default {
  name: "PrivacyProtection",
  props: {
    privacyText: {
      type: String,
      default: ""
    }
  },
  data(){
    return {
      showRightText: true
    };
  },
  mounted() {
    setTimeout(() => {
      this.showRightText = false;
    }, 3000);
  },
  methods: {
    toggleRightText() {
      this.showRightText = !this.showRightText;
    }
  }
};
</script>
