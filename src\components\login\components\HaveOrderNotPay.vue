<template>
  <div>
    <!-- 返回拦截弹窗 -->
    <app-popup
      :closeOnClickOverlay="false"
      :safeAreaInsetBottom="true"
      :show="show"
      :zIndex="9998"
      mode="center"
      showCancelButton
      @cancel="close"
    >
      <div
        class="w-311px h-[429px] box-border position-relative border-box"
      >
        <img
          alt=""
          class="background-image"
          src="@/components/login/imgs/<EMAIL>"
        >
        <div class="absolute top-[51px] right-[24px] flex items-center">
          <div class="text-[12px] text-[#FFFFFF] [text-shadow:0px_4px_4px_rgba(0,0,0,0.16)] mr-[4px]">
            倒计时
          </div>
          <u-count-down
            v-if="show"
            :time="countDown"
            format="HH:mm:ss:SSS"
            autoStart
            millisecond
            @change="handleChange"
          >
            <div class="flex items-center font-bold text-[12px] text-[#EB4738]">
              <p class="flex items-center justify-center w-[24px] h-[20px] bg-[#FFFFFF] rounded-[4px]">
                {{ timeData.minutes>=10?timeData.minutes:'0'+timeData.minutes }}
              </p>
              <p class="mx-[4px]">
                :
              </p>
              <p class="flex items-center justify-center w-[24px] h-[20px] bg-[#FFFFFF] rounded-[4px]">
                {{ timeData.seconds>=10?timeData.seconds:'0'+timeData.seconds }}
              </p>
              <p class="mx-[4px]">
                :
              </p>
              <p class="flex items-center justify-center w-[24px] h-[20px] bg-[#FFFFFF] rounded-[4px]">
                {{ Math.floor(timeData.milliseconds / 10) }}
              </p>
            </div>
          </u-count-down>
        </div>
        <div class="flex items-center justify-center absolute top-[287px] left-[30px]">
          <div class="font-bold text-[16px] text-[#FF551F]">
            ¥{{ serviceInfo.servicePrice | amountFilter }}
          </div>
          <div class="text-[12px] text-[#999999] ml-[8px] line-through">
            ¥{{ serviceInfo.originalPrice | amountFilter }}
          </div>
          <div class="text-[12px] text-[#666666] ml-[30px]">
            立省{{ (serviceInfo.originalPrice - serviceInfo.servicePrice) | amountFilter }}元
          </div>
        </div>
        <div class="absolute bottom-[49px] left-[16px]">
          <light-animation>
            <img
              alt=""
              class="block w-[279px] h-[46px]"
              src="@/components/login/imgs/Frame3146.png"
              @click="toPay"
            >
          </light-animation>
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import { amountFilter } from "@/libs/filter";
import { toConfirmOrder } from "@/libs/turnPages";
import Store from "@/store";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import UCountDown from "@/uview-ui/components/u-count-down/u-count-down.vue";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath";
import LightAnimation from "@/components/LightAnimation.vue";

export default {
  name: "LoginHaveOrderNotPay",
  components: { UCountDown, AppPopup, LightAnimation },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    serviceInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      timeData: {}
    };
  },
  computed: {
    /** 弹窗显示 */
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    },
    discountPrice() {
      return amountFilter(
        this.serviceInfo.originalPrice - this.serviceInfo.servicePrice
      );
    },
    countDown(){
      return Number(this.serviceInfo?.remainPayTime || 0) * 1000;
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          buryPointTransformationPath.add(5060);

          buryPointChannelBasics({
            code: "LAW_APPLET_INDEX_PAGE_V2_POP_UP_CREATE_ORDER_NO_PAY_TO_PAY_PAGE",
            type: 1,
            behavior: BURY_POINT_CHANNEL_TYPE.VI
          });
        }
      },
      immediate: true
    },
    serviceInfo: {
      handler() {
      },
      immediate: true
    }
  },
  methods: {
    close() {
      buryPointChannelBasics({
        code: "LAW_APPLET_INDEX_PAGE_V2_POP_UP_CREATE_ORDER_NO_PAY_TO_PAY_PAGE_LOOK_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });
      this.show = false;
    },
    toPay() {
      buryPointChannelBasics({
        code:
          "LAW_APPLET_INDEX_PAGE_V2_POP_UP_CREATE_ORDER_NO_PAY_TO_PAY_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      const payFn = () => {
        toConfirmOrder({
          serviceCode: this.serviceInfo.serviceCode,
          businessId: this.serviceInfo.businessId,
          lawyerId: this.serviceInfo.lawyerId,
          orderId: this.serviceInfo.orderId
        });
      };

      payFn();

      // 将付费方法存入vuex
      Store.commit("payState/SET_PAY_FAIL_POPUP_PAY", payFn);
    },
    handleChange(e){
      this.timeData = e;
    }
  }
};
</script>

<style lang="scss" scoped></style>
