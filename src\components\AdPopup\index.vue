<template>
  <div>
    <!-- #ifdef MP-WEIXIN -->
    <!--    <ad-popup-wrapper-->
    <!--      :position="position"-->
    <!--      :showSafe="showSafe"-->
    <!--      :timeCode="timeCode"-->
    <!--      :toast="toast"-->
    <!--      :showHead="showHead"-->
    <!--      :needWidth="needWidth"-->
    <!--      :visitCount="visitCount"-->
    <!--      :pageFlag="pageFlag"-->
    <!--      :adsPosition="adsPosition"-->
    <!--    >-->
    <!--      <template #havenTSeenTheAds>-->
    <!--        <slot-->
    <!--          name="havenTSeenTheAds"-->
    <!--        />-->
    <!--      </template>-->
    <!--      <template #watchedTheVideo>-->
    <!--        <slot-->
    <!--          name="watchedTheVideo"-->
    <!--        />-->
    <!--      </template>-->
    <!--    </ad-popup-wrapper>-->
    <!-- #endif -->
  </div>
</template>

<script>
import props from "./props";

export default {
  name: "AdPopup",
  props
};
</script>
