<template>
  <div class="flex items-center py-[8px] px-[12px] bg-[#FFFFFF]">
    <div class="w-[303px] h-[36px] px-[16px] bg-[#F5F5F7] rounded-[50px] box-border flex items-center">
      <img
        alt=""
        class="w-[16px] h-[16px] block mr-[8px] shrink-0"
        src="../pages/ask-details/img/1.png"
      >
      <u--input
        v-model="info"
        :cursorSpacing="50"
        :disabled="disabled"
        :placeholder="placeholder"
        :showConfirmBar="false"
        autoHeight
        border="none"
        placeholderStyle="font-size: 15px;font-weight: 400;color: #cccccc;"
        shape="circle"
        @confirm="handleClick"
      />
    </div>
    <div
      class="text-[14px] text-[#333333] flex-shrink-0 ml-[12px]"
      @click="handleClick"
    >
      搜索
    </div>
  </div>
</template>

<script>

export default {
  name: "SearchInput",
  props: {
    value: {
      type: String,
      default: "",
    },
    placeholder: {
      type: String,
      default: "请输入关键词",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    info: {
      get(){
        return this.value;
      },
      set(val){
        this.$emit("input", val);
      }
    },
  },
  methods: {
    /** 点击搜索 */
    handleClick() {
      this.$emit("search", this.info);
    },
  },
};
</script>

<style lang="scss" scoped></style>
