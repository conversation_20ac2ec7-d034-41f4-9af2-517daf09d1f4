<template>
  <div>
    <if f="hjls">
      <div
        class="py-[16px]"
        @click="handleGoLawyerHome(data)"
      >
        <div class="flex items-center">
          <div class="position-relative flex flex-row">
            <img
              :class="[
                `lawyer-card-avatar-${theme}`,
                `lawyer-card-avatar-${theme}-${tabIndex}`
              ]"
              :src="data.imgUrl"
              alt=""
              class="lawyer-card-avatar border-box"
            >
            <img
              v-if="labelsImg"
              :src="labelsImg"
              alt=""
              class="lawyer-head-labels"
            >
          </div>
          <div class="flex-1 pl-[12px]">
            <div class="flex justify-between">
              <div class="flex items-center font-[500] text-[16px] text-[#333333]">
                <p class="max-w-[70px] line-clamp-1">
                  {{ data.realName || "" }}
                </p>
                <!-- 0：不是会员 1：是会员               -->
                <img
                  v-if="data.isVip === 1"
                  alt=""
                  class="w-[12px] h-[12px] pl-[3px]"
                  src="@/components/lawyer-card-layout/img/<EMAIL>"
                >
              </div>
              <div class="flex items-center text-[13px] text-[#666666]">
                <p class="max-w-[44px] line-clamp-1">
                  {{ data.workCityName || "" }}
                </p>
                <span class="text-[#EEEEEE] px-[8px]">|</span>
                <p>
                  <span class="font-[500] text-[13px] text-[#333333]">{{
                    data.workTime
                  }}</span>年
                </p>
                <span class="text-[#EEEEEE] px-[8px]">|</span>
                <p>
                  帮助<span class="font-[500] text-[13px] text-[#333333]">{{
                    data.serverNum
                  }}</span>人
                </p>
              </div>
            </div>
            <div class="text-[12px] pt-[12px] text-[#999999] line-clamp-1">
              {{ data.lawyerOffice || "-" }}
            </div>
          </div>
        </div>
        <div class="flex items-center pt-[12px]">
          <p class="flex-1 line-clamp-1 text-[12px] text-[#999999]">
            擅长：{{ workField || "-" }}
          </p>
          <img
            src="@/pages/index/imgs/Frame1321314793.png"
            alt=""
            class="w-[30px] h-[30px] block ml-[24px] mr-[16px]"
            @click.stop="toPhoneAdvisory"
          >
          <div
            class="w-[108px] h-[30px] bg-[linear-gradient(_116deg,_#71B5FF_0%,_#2676E4_100%)] rounded-[50px] text-[13px] text-[#FFFFFF] box-border flex items-center justify-center"
            @click.stop="toLawyerIm"
          >
            <img
              alt=""
              class="w-[16px] h-[16px]"
              src="@/pages/index/imgs/Frame@2x(1).png"
            >
            <div>在线咨询</div>
          </div>
        </div>
      </div>
    </if>
    <if t="hjls">
      <div
        class="w-[351px] bg-[#FFFFFF] rounded-[8px] box-border p-[16px_16px_16px_12px] flex"
        @click="freeConsultation"
      >
        <div>
          <img
            :src="data.imgUrl"
            alt=""
            class="w-[52px] h-[52px] rounded-[40px] block"
            @click.stop="handleGoLawyerHome(data)"
          >
          <div
            v-if="data.online === 1"
            class="flex items-center justify-center mt-[8px]"
          >
            <i class="w-[5px] h-[5px] bg-[#50B08C] rounded-full" />
            <div class="text-[12px] text-[#50B08C] ml-[2px]">
              在线
            </div>
          </div>
        </div>
        <div class="ml-[12px] flex-1">
          <div class="flex items-center justify-between">
            <div>
              <div class="flex items-center">
                <div class="font-bold text-[16px] text-[#333333]">
                  {{ data.realName }}
                </div>
                <img
                  alt=""
                  class="w-[14px] h-[14px] ml-[8px]"
                  src="@/pages/index/imgs/card-info-rate.png"
                >
                <div class="font-bold text-[13px] text-[#F5BE41] ml-[2px]">
                  {{ (data.score || 0).toFixed(1) }}
                </div>
              </div>
              <div
                class="text-[13px] text-[#666666] mt-[4px] w-[150px] text-ellipsis"
              >
                {{ data.lawyerOffice }}
              </div>
            </div>
            <div
              class="text-[13px] text-[#FFFFFF] w-[104px] h-[28px] bg-[#50B08C] rounded-[50px] flex items-center justify-center shrink-0"
            >
              <img
                alt=""
                class="w-[16px] h-[16px] mr-[4px] block"
                src="@/components/lawyer-card-layout/img/123.png"
              >
              <div>
                免费咨询
              </div>
            </div>
          </div>
          <div class="flex mt-[12px]">
            <div class="text-[13px] text-[#666666] flex items-center">
              <div>执业</div>
              <div class="ml-[2px] text-[13px] text-[#EE5B3E]">
                {{ data.workTime }}年
              </div>
            </div>
            <div class="ml-[8px] text-[13px] text-[#666666] flex items-center">
              <div>已解决</div>
              <div class="ml-[2px] text-[13px] text-[#EE5B3E]">
                {{ data.serverNum }}人
              </div>
              <div>的法律纠纷</div>
            </div>
          </div>
          <div class="flex items-center space-x-[5px]">
            <div
              v-for="(item, index) in workFields"
              :key="index"
              class="mt-[8px] w-[58px] h-[20px] box-border bg-[#F5F5F7] rounded-[4px] border-[1px] border-solid border-[#EEEEEE] text-[12px] text-[#666666] flex items-center justify-center"
            >
              {{ item }}
            </div>
          </div>
        </div>
      </div>
    </if>
  </div>
</template>
<script>
import { amountFilter, amountIsOmittedFilter, formatTimeTwoUnit } from "@/libs/filter.js";
import { toLawyerFakeImBefore } from "@/libs/tools.js";
import { buryPointChannelBasics } from "@/api/burypoint";

import { BURY_POINT_CHANNEL_TYPE, FROM_PAGE, POINT_CODE } from "@/enum/burypoint";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath";
import { toOneToOneChatPage } from "@/libs/turnPages";
import { HJLS_BURYPOINT } from "@/enum/hjls-burypoint.js";

export default {
  name: "LawyerCardLayout",
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    tabIndex: {
      type: Number,
      default: null
    },
    theme: {
      type: String,
      default: "blue",
      validator: function(value) {
        return ["golden", "blue"].indexOf(value) !== -1;
      }
    },
    /** 图文咨询点击埋点 */
    imageConsultPoint: {
      type: String,
      default: POINT_CODE.LAW_APPLET_LAWYER_LIST_IMAGE_CONSULT_CLICK
    },
    /** 电话咨询点击埋点 */
    phoneConsultPoint: {
      type: String,
      default: POINT_CODE.LAW_APPLET_LAWYER_LIST_PHONE_CONSULT_CLICK
    },
    fromPage: {
      type: String,
      default: FROM_PAGE.SY_DHZX
    },
    fromPage1: {
      type: String,
      default: FROM_PAGE.SY_TWZX
    },
    offlinePriceIndex: {
      type: Number,
      default: 0
    },
    /** 转化路径 */
    transformationPath: {
      type: Number,
      default: null
    },
    /** 是否显示假的回复时间 */
    isShowFictitiousTime: {
      type: Boolean,
      default: false
    },
    showLawyerTags: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    fictitiousTimeText() {
      // 1-10 随机数
      const num = Math.floor(Math.random() * 10 + 1);

      return `“${num}分钟前回复了用户”`;
    },
    lawyerTags() {
      return this.data?.lawyerTags?.split(",").slice(0, 3) || [];
    },
    workField() {
      return this.data?.workField?.slice(0, 3).join("、");
    },
    offlinePrice() {
      return [528, 368, 350][this.offlinePriceIndex];
    },
    workFields() {
      return this.data?.workField?.slice(0, 3);
    },
    /** 电话咨询服务 */
    phoneConsultService() {
      return this.data?.serviceManageV2Vos?.find?.(
        item => Number(item.typeFlag) === 1
      );
    },
    /** 直降=线下价格-电话咨询价格 */
    discountPrice() {
      return this.phoneConsultService?.servicePrice
        ? parseInt(
          this.offlinePrice -
              amountFilter(this.phoneConsultService?.servicePrice)
        )
        : "???";
    },
    /** 电话咨询价格 */
    phoneConsultPrice() {
      return this.phoneConsultService?.servicePrice
        ? amountFilter(this.phoneConsultService?.servicePrice)
        : "???";
    },
    labelsImg() {
      let imgUrl = null;
      switch (this.tabIndex) {
      case 1:
        imgUrl = require("@/components/lawyer-card-layout/img/Group8307.png");
        break;

      case 2:
        imgUrl = require("@/components/lawyer-card-layout/img/<EMAIL>");
        break;

      case 3:
        imgUrl = require("@/components/lawyer-card-layout/img/<EMAIL>");
        break;

      default:
        imgUrl = null;
        break;
      }

      return imgUrl;
    }
  },
  methods: {
    amountIsOmittedFilter,
    formatTimeTwoUnit,
    /* 图文咨询*/
    toLawyerIm() {
      buryPointTransformationPath.addDataSources({
        fromPage: this.fromPage1
      });

      buryPointChannelBasics({
        code: this.imageConsultPoint,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      this.hasService(2);
    },
    /* 电话咨询*/
    toPhoneAdvisory() {
      buryPointTransformationPath.addDataSources({
        fromPage: this.fromPage
      });

      if (this.transformationPath) {
        buryPointTransformationPath.add(this.transformationPath);
      }

      buryPointChannelBasics({
        code: this.phoneConsultPoint,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      this.hasService(1);
    },
    /* 判断有无图文咨询 或者电话咨询服务*/
    // itemClassType	否	Integer	选项分类 1 电话 2 图文
    hasService(itemClassType) {
      return toLawyerFakeImBefore({
        lawyerInfo: this.data,
        itemClassType
      });
    },
    handleGoLawyerHome() {
      this.$emit("handleClick", this.data);
    },
    /** 免费咨询 */
    freeConsultation() {
      buryPointChannelBasics({
        code: HJLS_BURYPOINT.HJ_APPLET_INDEX_FREE_CONSULT_CLICK,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });
      toOneToOneChatPage({
        lawyerId: this.data.id
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.lawyer-head-labels {
  position: absolute;
  bottom: -1px;
  right: -2px;
  font-size: 0;
  width: 26px;
  height: 15px;
  z-index: 1;
}

.lawyer-card {
  padding: 12px;
  background: #ffffff;
  opacity: 1;
  box-sizing: border-box;
  .vip-icon {
    width: 12px;
    height: 12px;
    padding-left: 3px;
  }

  &-bg {
    position: absolute;
    display: block;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
  }

  .online {
    font-size: 12px;
    font-weight: 400;
    color: #666666;

    .online-dot {
      margin-right: 2px;
      width: 5px;
      height: 5px;
      background: #07c160;
      border-radius: 50%;
      opacity: 1;
    }
  }

  &-avatar {
    flex-shrink: 0;
    display: block;
    width: 48px;
    height: 48px;
    background: #c4c4c4;
    opacity: 1;
    border-radius: 4px;

    &-golden {
      &-1 {
        border: 1px solid #e5b04e;
      }

      &-2 {
        border: 1px solid #b9c2e8;
      }

      &-3 {
        border: 1px solid #fdc79f;
      }
    }
    &-blue {
      &-1 {
        border: 1px solid #e5b04e;
      }

      &-3 {
        border: 1px solid #fdc79f;
      }
    }
  }

  &-name {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
  }

  &-work-city {
    margin-left: 12px;

    .city {
      font-size: 12px;
      max-width: 46px;
      font-weight: 400;
      color: #9f6310;
    }
  }

  .office {
    margin-top: 6px;
    font-size: 13px;
    font-weight: 400;
    color: #666666;
  }

  &-info {
    margin-top: 4px;
    font-size: 13px;
    font-weight: 400;
    color: #666666;

    &-rate {
      font-size: 13px;
      font-weight: bold;
      color: #f2af30;

      &-icon {
        display: inline-block;
        width: 14px;
        height: 14px;
        margin-right: 2px;
      }
    }
  }

  .lawyer-card-btns {
    padding-top: 12px;

    .btn-border,
    .btn-solid {
      height: 26px;
      line-height: 26px;
      border-radius: 50px 50px 50px 50px;
      font-size: 13px;
      font-weight: 400;
      color: #ffffff;
      text-align: center;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .btn-border {
      width: 84px;
      color: #9f6310;
      border: 1px solid #e4d5bc;
      background: #fcf8f0;
    }

    .btn-solid {
      width: 100px;
      margin-left: 12px;
      color: #ffffff;
      background: linear-gradient(109deg, #e4b14f 0%, #c68020 100%);

      &__icon {
        display: block;
        width: 16px;
        height: 16px;
      }
    }
  }

  &-blue {
    .lawyer-card-btns {
      .btn-border {
        color: #3887f5;
        background-color: #ffffff;
        border: 1px solid #3887f5;
      }

      .btn-solid {
        width: 100px;
        margin-left: 12px;
        color: #ffffff;
        background: #3887f5;
      }
    }
  }

  &-golden {
    padding: 12px 0;
  }
}

.lawyer-card-be-good-at {
  padding-top: 8px;
  font-size: 12px;
  font-weight: 400;
  max-width: 240px;
  color: #999999;
}
</style>
