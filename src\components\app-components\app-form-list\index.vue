<template>
  <div>
    <div
      v-for="(form, index) in formList"
      :key="index"
      class="form-list"
    >
      <!-- 输入框 -->
      <app-form-list-item
        v-if="form.type === 'input'"
        :borderBottom="borderBottom"
        :isRequired="form.isRequired"
        :labelPosition="getLabelPosition(form)"
        :title="form.title"
      >
        <div
          :style="[$u.addStyle(formInputMoreItemStyle)]"
          class="input-box flex flex-align-center"
        >
          <input
            :maxlength="form.maxLength"
            :placeholderStyle="placeholderStyle"
            :type="form.inputType || 'text'"
            :value="formData[form.key]"
            class="input-box-button"
            placeholder="请输入"
            placeholderClass="input-box-button--placeholder"
            @input="handleInput($event, form.key, form.title)"
          >
          <p
            v-if="form.unit"
            class="input-box-unit"
          >
            {{ form.unit }}
          </p>
        </div>
      </app-form-list-item>

      <!-- 单选 -->
      <app-form-list-item
        v-if="form.type === 'radio'"
        :borderBottom="borderBottom"
        :isRequired="form.isRequired"
        :labelPosition="getLabelPosition(form)"
        :title="form.title"
      >
        <div class="form-radio-more flex flex-align-center flex-space-between">
          <p
            v-for="(item, cIndex) in form.arr"
            :key="cIndex"
            :class="formData[form.key] === item.value && 'current'"
            :style="[
              $u.addStyle(formRadioMoreItemStyle),
              formData[form.key] === item.value &&
                $u.addStyle(formRadioMoreItemCurrentStyle)
            ]"
            class="form-radio-more-item"
            @click="
              changeFormData({
                key: form.key,
                labelValue: item.label,
                value: item.value,
                label: form.title
              })
            "
          >
            {{ item.label }}
          </p>
        </div>
      </app-form-list-item>
    </div>
  </div>
</template>

<script>
import { isArray, isNull, isString } from "@/libs/basics-tools";
import { debounce } from "@/libs/tools";
// import AddrPopup from "@/components/app-components/app-form-list/addr-popup.vue";
import AppFormListItem from "@/components/app-components/app-form-list/app-form-item-list.vue";
import { setValidate } from "@/components/app-components/app-form-list/index";

export default {
  name: "AppFormList",
  components: {
    AppFormListItem
    // AddrPopup,
  },
  mixins: [uni.$u.mixin],
  props: {
    formList: {
      type: Array,
      default: () => []
    },
    labelPosition: {
      type: String,
      default: "top",
      validator: value => ["top", "left"].indexOf(value) > -1
    },
    data: {
      type: Object,
      default: () => ({})
    },
    realTimeValidate: {
      type: [Boolean, undefined],
      default: undefined
    },
    /** radio样式 */
    formRadioMoreItemStyle: {
      type: [Object, String],
      default: () => ({})
    },
    /** radio选中样式 */
    formRadioMoreItemCurrentStyle: {
      type: [Object, String],
      default: () => ({})
    },
    /** 输入框样式 */
    formInputMoreItemStyle: {
      type: [Object, String],
      default: () => ({})
    },
    /** 输入框占位样式 */
    formInputPlaceholderMoreItemStyle: {
      type: [Object, String],
      default: () => ({})
    },
    /** 是否显示下边框 */
    borderBottom: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      /** 地址对应下标记 */
      addrKey: 0,
      /** picker对应下标记 */
      pickerKey: 0,
      formData: {},
      popupData: {},
      /** 表单中选择的每一项 */
      selectList: [],
      startValidate: debounce(() => {
        this.realTimeVerification();
      }, 500)
    };
  },
  computed: {
    /** 过滤数组中的空值 */
    filterSelectList() {
      return this.selectList.filter(item => !isNull(item.value));
    },
    placeholderStyle() {
      const style = {
        fontSize: "13px",
        fontWeight: 400,
        color: "#222222",
        ...this.formInputPlaceholderMoreItemStyle
      };

      return this.$u.addStyle(style, "string");
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler() {
        this.formData = {
          ...this.formData,
          ...JSON.parse(JSON.stringify(this.data || {}))
        };

        this.init();
      }
    },
    formData: {
      deep: true,
      handler() {
        console.log(this.formData, "this.formData");
        if (!isNull(this.realTimeValidate)) {
          this.startValidate();
        }
      }
    },
    formList: {
      handler(value) {
        // 如果长度发生了变化，再重新初始化
        if (value.length !== this.selectList.length) {
          // ! 将key存下来，这里是为了保证后面往数组里面添加数据的时候顺序和 formList 的顺序一致
          this.selectList = value.map(item => ({
            key: item.key
          }));
          this.init();
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    if (!isNull(this.realTimeValidate)) {
      this.startValidate();
    }

    setValidate(this.validate);
  },
  methods: {
    /** 初始化 */
    init() {
      console.log(this.formData, "this.formData111111111");
      console.log(this.formList, "this.formList111111111");
      // 遍历 this.data
      Object.entries(this.formData).forEach(([key, value]) => {
        const target = this.formList.find(item => String(item.key) === key);

        const targetLabelValue = target?.arr?.find?.(
          item => item.value === value
        )?.label;

        console.log(targetLabelValue, "targetLabelValue");

        this.changeFormData({
          label: target?.title,
          labelValue: targetLabelValue,
          value,
          key: Number(key)
        });
      });
    },
    /** 获取label的位置 */
    getLabelPosition(data) {
      return data.labelPosition || this.labelPosition;
    },
    changeFormData({ key, value, labelValue, label }) {
      console.log(
        key,
        value,
        labelValue,
        label,
        "key, value, labelValue, label"
      );
      this.$set(this.formData, key, value);
      this.addSelectList({
        key,
        label,
        labelValue: labelValue || value,
        value
      });
      this.$emit("updateData", this.formData);
      this.$emit("updateSelectList", this.filterSelectList);
      console.log(this.filterSelectList, "updateSelectList");
    },
    /** 由于小程序的问题，这里单独对输入框做处理 */
    handleInput(e, key, title) {
      this.changeFormData({
        key: key,
        value: e.target.value,
        label: title
      });
    },
    /** 往 selectList 添加项 */
    addSelectList(item) {
      this.selectList.forEach((select, index) => {
        if (select.key === item.key) {
          this.$set(this.selectList, index, item);
        }
      });
    },
    /* 必填项验证 */
    validate(regState = true) {
      return new Promise((resolve, reject) => {
        for (let formListItem of this.formList) {
          console.log(formListItem, "formListItem");
          if (
            formListItem.isRequired &&
            isNull(this.formData[formListItem.key])
          ) {
            reject();
            return false;
          }
          if (regState && formListItem.reg && isArray(formListItem.reg)) {
            for (let r of formListItem.reg) {
              try {
                if (isString(r)) {
                  this.regs(r, this.formData[formListItem.key], this.formData);
                } else {
                  r(this.formData[formListItem.key], this.formData);
                }
              } catch (e) {
                this.$toast(e.message || e.description);
                reject(e);
                return false;
              }
            }
          }
        }
        resolve(this.formData);
      });
    },
    /**
     * 实时验证
     */
    realTimeVerification() {
      this.validate(false)
        .then(() => {
          this.$emit("update:realTimeValidate", true);
        })
        .catch(() => {
          this.$emit("update:realTimeValidate", false);
        });
    },
    regs(reg, value) {
      if (reg === "number" && value) {
        if (!isNull(value) && !/^[0-9]*$/.test(value)) {
          throw new Error("请输入正确的数字");
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.form-radio-more {
  .current {
    border: 0;
    color: #ffffff;
    background: $theme-color;
  }

  &-item {
    flex-shrink: 0;
    width: 56px;
    height: 34px;
    // border: 1px solid #eeeeee;
    border-radius: 30px 30px 30px 30px;
    margin-top: 5px;
    box-sizing: border-box;
    background: #f5f5f7;
    color: #222222;
    font-weight: 400;
    font-size: 13px;
    display: flex;
    justify-content: center;
    align-items: center;

    &:nth-child(n) {
      margin-right: 10px;
    }

    &:last-child {
      margin-right: 0;
    }
  }
}

// 输入框的样式
.input-box {
  width: 124px;
  height: 34px;
  background: #f5f5f7;
  border-radius: 17px 17px 17px 17px;
  display: flex;
  padding: 0 6px 0 12px;
  box-sizing: border-box;
  justify-content: space-between;

  &-button {
    outline: none;
    background: none;
    border: 0;
    width: 60px;
    height: 100%;
    flex: 1;
  }

  &-unit {
    color: #cccccc;
    padding-right: 10px;
    padding-left: 10px;
    position: relative;

    &:after {
      content: "|";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
</style>
