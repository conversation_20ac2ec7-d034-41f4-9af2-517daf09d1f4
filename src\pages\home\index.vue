<template>
  <div class="position-relative min-h-screen bg-[#FFFFFF]">
    <img
      :src="homeConfig.bgUrl"
      alt=""
      class="w-full background-image"
      mode="widthFix"
    >
    <div class="absolute top-[316px] left-[12px]">
      <swiper
        :autoplay="true"
        circular
        class="w-[351px] h-[200px]"
        @change="change"
      >
        <swiper-item
          v-for="(item, index) in homeConfig.swiper"
          :key="index"
          class="w-full h-full"
        >
          <img
            :src="item.url"
            alt=""
            class="w-full h-full block"
            @click="toFindLawyer"
          >
        </swiper-item>
      </swiper>
      <div class="absolute w-full -bottom-[10px]">
        <app-indicator
          :list="homeConfig.swiper"
          :value="currentIndex"
        />
      </div>
    </div>
    <img
      :src="homeConfig.buttonUrl"
      alt=""
      class="absolute top-[538px] left-0 w-full"
      mode="widthFix"
      @click="toFindLawyer"
    >
  </div>
</template>

<script>

import { getCommonConfigKey } from "@/api/order";
import AppIndicator from "@/components/app-components/AppIndicator.vue";
import { toFindLawyer } from "@/libs/turnPages";

export default {
  name: "HomePage",
  components: { AppIndicator },
  data(){
    return {
      homeConfig: {
        bgUrl: "",
        buttonUrl: "",
        swiper: [{
          url: ""
        }]
      },
      currentIndex: 0
    };
  },
  mounted() {
    this.getBannerUrl();
  },
  methods: {
    toFindLawyer,
    getBannerUrl() {
      getCommonConfigKey({ paramName: "HJ_REVIEW_HOME" }).then(res => {
        try {
          this.homeConfig = JSON.parse(res.data.paramValue);
        } catch (e) {
          console.log(e);
        }
      });
    },
    change(e) {
      this.currentIndex = e.detail.current;
    },
  },
};
</script>
