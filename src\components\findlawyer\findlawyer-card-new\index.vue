<template>
  <div class="findlawyer-card-warp flex">
    <div
      v-for="data in lawyerList"
      :key="data.id"
      class="flex-shrink-0 card flex"
      @click="handleClick(data)"
    >
      <img
        :src="data.imgUrl"
        alt=""
        class="avatar"
      >
      <div class="mg-l-8">
        <div class="top flex flex-align-center">
          <div class="flex flex-align-center">
            <p class="name">
              {{ data.realName || "" }}
            </p>
            <div class="fraction flex flex-align-center">
              <img
                alt=""
                class="lawyer-card-info-rate-icon"
                src="../../../pages/index/imgs/card-info-rate.png"
              >
              {{ (data.score || 0).toFixed(1) }}
            </div>
          </div>
        </div>
        <div class="company text-ellipsis">
          {{ data.lawyerOffice || "" }}
        </div>
        <div class="btn">
          <p class="btn-text">
            去咨询
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { currencyGetAddressByName, lawyerListV3 } from "@/api/findlawyer.js";
import { toLawyerHome } from "@/libs/turnPages.js";
import { isArrNull } from "@/libs/basics-tools.js";

export default {
  name: "FindLawyerCardNew",
  props: {
    /** 位置 */
    optionCityName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      lawyerList: [],
    };
  },
  watch: {
    optionCityName: {
      immediate: true,
      handler() {
        if (this.optionCityName) {
          /* 通过城市名称获取城市信息*/
          currencyGetAddressByName({
            cityName: this.optionCityName,
          }).then((res) => {
            this.getLawyerList(res.data.cityCode);
          });
        }
      },
    },
  },
  methods: {
    getLawyerList(cityCode) {
      lawyerListV3({
        /** 页码 */
        currentPage: 1,
        /** 每页条数 */
        pageSize: 5,
        // 默认会员律师优先
        // sort: 6,
        cityCode,
        cityName: this.optionCityName,
      }).then((res) => {
        this.lawyerList = res.data.records || [];
        /* true 有本地推荐律师*/
        this.$emit(
          "locateTheLawyerToDisplayTheStatus",
          !isArrNull(this.lawyerList)
        );
      });
    },
    handleClick(data) {
      toLawyerHome(data);
    },
  },
};
</script>

<style lang="scss" scoped>
.findlawyer-card-warp {
  padding-top: 12px;
  overflow-y: auto;
  margin-right: -12px;

  .card {
    width: 150px;
    background: #FFFFFF;
    opacity: 1;
    box-sizing: border-box;

    & + .card {
      margin-left: 8px;
    }

    .avatar {
      display: block;
      width: 36px;
      height: 36px;
      border-radius: 6px;
      opacity: 1;
    }

    .name {
      font-size: 14px;
      font-weight: 400;
      color: #333333;
    }

    .fraction {
      font-size: 11px;
      font-weight: 500;
      color: #f2af30;

      .lawyer-card-info-rate-icon {
        margin-left: 4px;
        width: 12px;
        height: 12px;
      }
    }

    .company {
      padding-top: 2px;
      font-size: 11px;
      font-weight: 400;
      color: #666666;
    }

    .btn {
      padding-top: 6px;
      display: flex;

      .btn-text {
        padding: 2px 12px;
        text-align: center;
        border-radius: 68px;
        font-size: 12px;
        font-weight: 400;
        border: 0.5px solid #3887f5;
        color: #3887f5;
      }
    }
  }
}
</style>
