import { requestCore } from "@/libs/axios";
import { loginPoint } from "@/api/burypoint";
import { isObjNull } from "@/libs/basics-tools";

/* 发送短信验证码*/
export const sendSms = data =>
  requestCore.post("/common/message/sendSms", data);

/* 验证码登陆*/
export const userLogin = data => requestCore.post("/user/login", data);

/* 登出*/
export const userLoginOut = data => requestCore.post("/user/loginOut", data);

/* 微信授权*/
// export const userWechatAuth = (data) => requestCore.post("/user/login/wechatAuth", data)

/* 微信绑定*/
// export const  userWechatBind = (data) => requestCore.post("/user/wechatBind", data)

/* 微信解绑*/
// export const  userWechatBinduntie = (data) => requestCore.post("/user/wechatBind/untie", data)

export const wechatUserLogin = data =>
  requestCore.post("/user/wechat/login", data);

/* 百度登录*/
// export const  baiduUserLogin = (data) => requestCore.post("/user/ba/login", data)

/* 小程序授权后登录*/
export const commonLoginRequest = data => {
  const params = isObjNull(loginPoint.loginPointData)
    ? data
    :  {
      ...data,
      extra: {
        ...loginPoint.loginPointData
      }
    };

  /* 头条登录*/
  // #ifdef MP-TOUTIAO
  return requestCore.post("/user/douyin/login", params);
  // #endif

  /* 微信登录*/
  // #ifdef MP-WEIXIN
  return requestCore.post("/user/wechat/login", params);
  // #endif

  /* 百度登录*/
  // #ifdef MP-BAIDU
  return requestCore.post("/user/ba/login", params);
  // #endif

  /* 支付宝登录*/
  // #ifdef MP-ALIPAY
  return requestCore.post("/user/alipayApplet/login", params);
  // #endif
};
