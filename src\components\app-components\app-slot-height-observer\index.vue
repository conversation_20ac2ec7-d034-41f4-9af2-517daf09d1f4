<template>
  <div
    :id="observerId"
    class="app-slot-height-observer"
  >
    <slot />
  </div>
</template>

<script>
import { mapMutations } from "vuex";

export default {
  name: "AppSlotHeightObserver",
  props: {
    // 唯一标识符，用于区分不同的观察器实例
    observerId: {
      type: String,
      default: () =>
        `slot_observer_${Date.now()}_${Math.random()
          .toString(36)
          .substr(2, 9)}`,
    },
    // 是否存储到vuex
    storeToVuex: {
      type: Boolean,
      default: true,
    },
    // vuex中存储的key
    vuexKey: {
      type: String,
      default: "slotHeight",
    },
    // 高度变化的阈值，小于此值不触发更新
    threshold: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      currentHeight: 0,
      intersectionObserver: null,
      resizeObserver: null,
      checkTimer: null,
    };
  },
  computed: {
    // 响应式的高度值，外部可以通过这个获取
    slotHeight() {
      return this.currentHeight;
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initHeightObserver();
    });
  },
  beforeDestroy() {
    this.cleanup();
  },
  // 监听 slot 内容变化
  updated() {
    this.$nextTick(() => {
      this.measureHeight();
    });
  },
  methods: {
    ...mapMutations(["SET_SLOT_HEIGHT"]),

    // 初始化高度监听器
    initHeightObserver() {
      // 首次获取高度
      this.measureHeight();

      // 使用 Intersection Observer 监听元素变化
      this.setupIntersectionObserver();

      // 定时检查高度变化（作为备用方案）
      this.setupPeriodicCheck();
    },

    // 测量当前高度
    measureHeight() {
      const query = uni.createSelectorQuery().in(this);
      query
        .select(`#${this.observerId}`)
        .boundingClientRect((rect) => {
          if (rect && rect.height !== undefined) {
            this.updateHeight(rect.height);
          }
        })
        .exec();
    },

    // 设置 Intersection Observer
    setupIntersectionObserver() {
      if (this.intersectionObserver) {
        this.intersectionObserver.disconnect();
      }

      this.intersectionObserver = uni.createIntersectionObserver(this, {
        thresholds: [0, 0.1, 0.5, 1],
        observeAll: true,
      });

      this.intersectionObserver
        .relativeToViewport()
        .observe(`#${this.observerId}`, (res) => {
          // 当元素进入或离开视口时，重新测量高度
          if (res.intersectionRatio > 0) {
            this.measureHeight();
          }
        });
    },

    // 设置定时检查（作为备用方案）
    setupPeriodicCheck() {
      if (this.checkTimer) {
        clearInterval(this.checkTimer);
      }

      this.checkTimer = setInterval(() => {
        this.measureHeight();
      }, 500); // 每500ms检查一次
    },

    // 更新高度
    updateHeight(newHeight) {
      const heightDiff = Math.abs(newHeight - this.currentHeight);

      // 只有高度变化超过阈值时才更新
      if (heightDiff >= this.threshold) {
        const oldHeight = this.currentHeight;
        this.currentHeight = newHeight;

        // 存储到 vuex
        if (this.storeToVuex) {
          this.SET_SLOT_HEIGHT({
            key: this.vuexKey,
            height: newHeight,
            observerId: this.observerId,
          });
        }

        // 触发高度变化事件
        this.$emit("height-change", {
          height: newHeight,
          oldHeight: oldHeight,
          observerId: this.observerId,
        });

        // 触发自定义事件（全局）
        uni.$emit("slot-height-change", {
          height: newHeight,
          oldHeight: oldHeight,
          observerId: this.observerId,
        });
      }
    },

    // 手动刷新高度
    refreshHeight() {
      this.measureHeight();
    },

    // 获取当前高度
    getCurrentHeight() {
      return this.currentHeight;
    },

    // 清理资源
    cleanup() {
      if (this.intersectionObserver) {
        this.intersectionObserver.disconnect();
        this.intersectionObserver = null;
      }

      if (this.checkTimer) {
        clearInterval(this.checkTimer);
        this.checkTimer = null;
      }
    },
  },
};
</script>

<style scoped>
.app-slot-height-observer {
  width: 100%;
}
</style>
