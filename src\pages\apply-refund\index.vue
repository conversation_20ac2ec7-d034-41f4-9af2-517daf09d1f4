<template>
  <no-scroll :visiBle="reasonVisible">
    <div class="apply-refund">
      <div class="info-content">
        <common-info :data="orderDetail" />
      </div>
      <div class="apply-refund-wrap">
        <p class="title font16">
          申请原因<span class="font16 span">*</span>
        </p>
        <div
          class="reason-select flex flex-align-center flex-space-between"
          @click="() => (reasonVisible = !reasonVisible)"
        >
          <p
            v-if="reasonData.label"
            class="text font13 text-ellipsis"
          >
            {{ reasonData.label }}
          </p>
          <p
            v-else
            class="text placeholder font13"
          >
            请选择
          </p>
          <img
            alt=""
            class="arrow"
            src="./assets/arrow-down.png"
            srcset=""
          >
          <select-reason
            v-model="reasonVisible"
            :type="routerParams.type"
            @reasonConfirm="reasonConfirmFun"
          />
        </div>
        <!-- 补充说明和截图 -->
        <div class="add-box">
          <p class="title font16">
            补充说明<span class="font16 span">*</span>
          </p>
          <div class="content">
            <div class="textarea">
              <textarea
                v-model="refundReason"
                :show-count="false"
                class="font14"
                maxlength="50"
                placeholder="请输入您的问题，我们竭诚为您服务"
                placeholder-style="color: #CCC"
              />
              <p class="number font15">
                <span>{{ refundReason.length }}</span>/50
              </p>
            </div>
            <app-upload v-model="fileList1" />
          </div>
        </div>
        <div class="tips">
          <p class="tips-desc">
            温馨提示：
          </p>
          <p
            v-for="(item, index) in serviceDesc"
            :key="index"
            class="tips-desc"
          >
            {{ item }}
          </p>
        </div>
      </div>
      <div
        :class="{
          'to-submit': true,
          'flex-vertically-centered': true,
          'not-submit': !canSubmit
        }"
        @click="toSubmit"
      >
        <p class="text">
          确认提交
        </p>
      </div>
    </div>
  </no-scroll>
</template>

<script>
import CommonInfo from "./commonInfo/index.vue";
import SelectReason from "./select-reason/index.vue";
import NoScroll from "@/components/no-scroll/index.vue";
import {
  applyRefund,
  applyRefundByBusiness,
  applyRefundNew,
  userChangeLawyerApply
} from "@/api/order.js";

import AppUpload from "@/pages/apply-refund/app-upload/index.vue";
import {
  changeLawyerInfoText,
  refundInfoText,
  refundTypeEnum
} from "@/pages/apply-refund/js/refundInfo.js";
import { toChangeLawyerDetail, toRefundDetail } from "@/libs/turnPages.js";
import orderDetailMixin from "@/pages/apply-refund/mixins/orderDetailMixin.js";

export default {
  name: "ApplyRefund",
  components: {
    AppUpload,
    CommonInfo,
    SelectReason,
    NoScroll
  },
  mixins: [orderDetailMixin],
  data() {
    return {
      /** 路由参数 */
      routerParams: {},
      /** 选择理由弹窗 */
      reasonVisible: false,
      reasonData: {},
      refundReason: "",
      fileList1: []
    };
  },
  onLoad(options) {
    this.routerParams = options;

    if (this.routerParams.type === refundTypeEnum.REFUND) {
      uni.setNavigationBarTitle({
        title: "申请退款"
      });
    }

    if (this.routerParams.type === refundTypeEnum.CHANGE_LAWYER) {
      uni.setNavigationBarTitle({
        title: "申请换律师"
      });
    }

    this.getDetailByOrderId();
  },
  computed: {
    canSubmit() {
      return (
        this.fileList1.length &&
        this.refundReason &&
        !this.$basicsTools.isNull(this.reasonData.value)
      );
    },
    /** 退款界面 */
    refundType() {
      return this.routerParams.type === refundTypeEnum.REFUND;
    },
    /** 换律师界面 */
    changeLawyer() {
      return this.routerParams.type === refundTypeEnum.CHANGE_LAWYER;
    },
    /** 服务说明文案 */
    serviceDesc() {
      if (this.changeLawyer) {
        return changeLawyerInfoText.content;
      }

      if (this.refundType) {
        return refundInfoText.content;
      }

      return [];
    }
  },
  methods: {
    // 选择退款原因
    reasonConfirmFun(item) {
      this.reasonData = item;
    },
    validFun() {
      if (this.$basicsTools.isNull(this.reasonData.value)) {
        return this.$toast("请选择申请原因");
      } else if (!this.refundReason) {
        return this.$toast("请填写补充原因");
      } else if (/^[0-9]*$/.test(this.refundReason)) {
        return this.$toast("补充原因内容中不包含中文");
      } else if (this.fileList1.length < 1) {
        return this.$toast("请上传截图凭证哟");
      }
    },
    toSubmit() {
      this.validFun();
      if (!this.canSubmit) return false;

      if (this.refundType) this.submitRefund();

      if (this.changeLawyer) this.submitChangeLawyer();
    },
    /** 提交退款 */
    async submitRefund() {
      try {
        let pics = this.fileList1?.map(item => item.url).join(",");
        let params = {
          orderId: this.routerParams.orderId,
          refundReason: this.refundReason,
          refundLabelId: Number(this.reasonData.value),
          refundPic: pics
        };

        let refundType = this.routerParams.refundType || "historyOrder";
        let caseSourceId = this.routerParams.caseSourceId;
        let res = !this.$basicsTools.isNull(caseSourceId)
          ? await applyRefundByBusiness({
            businessId: caseSourceId,
            businessType: this.routerParams.type,
            refundReason: this.refundReason,
            refundLabelId: Number(this.reasonData.value),
            refundPic: pics
          })
          : refundType === "newOrder"
            ? await applyRefundNew(params)
            : await applyRefund(params);
        if (res) {
          toRefundDetail({
            orderId: res.data.orderId,
            refundType
          });
        }
      } catch (error) {
        console.log(error);
      }
    },
    /** 提交换律师 */
    submitChangeLawyer() {
      this.validFun();
      if (!this.canSubmit) return false;

      const pics = this.fileList1?.map(item => item.url).join(",");

      userChangeLawyerApply({
        orderId: this.routerParams.orderId,
        changeReasonLabelId: Number(this.reasonData.value),
        changeReasonLabel: this.reasonData.label,
        changeAddNote: this.refundReason,
        changePic: pics
      }).then(() => {
        toChangeLawyerDetail({
          orderId: this.routerParams.orderId
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.apply-refund {
  padding-bottom: 150px;

  .title {
    color: #333333;
    font-weight: bold;
    padding: 16px 0 12px 0;

    .span {
      color: #f44949;
      margin-left: 1px;
    }
  }

  .info-content {
    width: 375px;
    background: #ffffff;
    border-radius: 0 0 16px 16px;
    height: 78px;
    padding: 16px 12px;
    box-sizing: border-box;
  }

  &-wrap {
    background: #ffffff;
    border-radius: 16px;
    margin-top: 12px;
    padding: 0 16px;
    box-sizing: border-box;
    width: 375px;

    .reason-select {
      width: 343px;
      height: 42px;
      background: #f5f5f7;
      border-radius: 8px 8px 8px 8px;
      padding: 12px;
      box-sizing: border-box;

      .text {
        color: #333;
        width: 295px;
      }

      .placeholder {
        color: #999;
      }

      .arrow {
        width: 16px;
        height: 16px;
      }
    }

    .add-box {
      margin-top: 16px;

      .content {
        background: #f5f5f7;
        width: 343px;
        border-radius: 8px;
        padding: 8px 8px 0 8px;
        box-sizing: border-box;

        .textarea {
          background: #f5f5f7;
          height: 109px;

          textarea {
            resize: none;
            border: none;
            line-height: 24px;
            background: #f5f5f7;
            width: 327px;
            height: 80px;
            color: #222;
            box-sizing: border-box;

            &::placeholder {
              color: #ccc;
            }
          }

          .number {
            width: 100%;
            height: 25px;
            text-align: right;
            box-sizing: border-box;
            color: #999999;
          }
        }
      }
    }

    .tips {
      margin-top: 16px;
      padding-bottom: 24px;

      .tips-desc {
        font-size: 13px;
        line-height: 22px;
        font-weight: 400;
        color: #999999;

        &:nth-child(1) {
          margin-bottom: 3px;
        }
      }
    }
  }

  .to-submit {
    width: 375px;
    height: 78px;
    background: #ffffff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.12);
    border-radius: 16px 16px 0 0;
    position: fixed;
    bottom: 0;

    .text {
      width: 343px;
      height: 40px;
      border-radius: 100px 100px 100px 100px;
      background: #3887f5;
      font-size: 14px;
      color: #fff;
      line-height: 40px;
      text-align: center;
    }
  }

  .not-submit .text {
    background: #a0cffb;
  }
}
</style>
