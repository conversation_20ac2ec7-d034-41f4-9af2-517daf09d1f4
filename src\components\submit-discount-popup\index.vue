<template>
  <!-- 返回拦截弹窗 -->
  <app-popup
    :closeOnClickOverlay="false"
    :safeAreaInsetBottom="true"
    :show="show"
    :zIndex="9998"
  >
    <div class="popup-content">
      <submit-discount-title />
      <div class="service-card">
        <div class="flex flex-align-center flex-space-between">
          <div class="flex flex-align-center">
            <img
              :src="serviceInfo.icon"
              alt=""
              class="service-card__icon"
            >
            <div class="service-card__text">
              {{ serviceInfo.serviceName }}
            </div>
          </div>
          <div class="service-card__origin-price flex flex-align-center">
            <div>¥{{ serviceInfo.originalPrice | amountFilter }}</div>
            <img
              alt=""
              class="service-card__origin-price__icon"
              src="@/components/submit-discount-popup/img/<EMAIL>"
            >
          </div>
        </div>
        <div
          class="service-card__price flex flex-align-center flex-space-between"
        >
          <div>专业定位问题，针对性提供解决方案</div>
          <div class="service-card__price__count">
            ¥{{ serviceInfo.servicePrice | amountFilter }}
          </div>
        </div>
      </div>
      <div class="button flex flex-space-between">
        <div
          class="button__cancel"
          @click="close"
        >
          放弃优惠
        </div>
        <div
          class="button__pay"
          @click="toPay"
        >
          <img
            alt=""
            class="button__pay__icon"
            src="@/components/submit-discount-popup/img/<EMAIL>"
          >
          立即支付
        </div>
      </div>
      <u-safe-bottom />
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import SubmitDiscountTitle from "@/components/SubmitDiscountTitle.vue";
import DiscountPriceMixin from "@/pages/submit-question/mixins/DiscountPriceMixin";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import { sceneType } from "@/libs/config";

export default {
  name: "SubmitDiscountPopup",
  components: { SubmitDiscountTitle, USafeBottom, AppPopup },
  mixins: [DiscountPriceMixin],
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    /** 展示uv code */
    uvCode: {
      type: String,
      required: true,
    },
    /** 支付code */
    payCode: {
      type: String,
      required: true,
    },
    /** 取消code */
    cancelCode: {
      type: String,
      required: true,
    },
  },
  data(){
    return {
      serviceSceneType: sceneType.dhzq_jj,
    };
  },
  computed: {
    /** 弹窗显示 */
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          buryPointChannelBasics({
            code: this.uvCode,
            type: 1,
            behavior: BURY_POINT_CHANNEL_TYPE.VI,
          });
        }
      },
      immediate: true,
    },
  },
  methods: {
    close() {
      buryPointChannelBasics({
        code: this.cancelCode,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
      });

      this.show = false;

      this.$emit("close", false);
    },
    toPay(){
      buryPointChannelBasics({
        code: this.payCode,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
      });

      this.toConsult();
    }
  },
};
</script>

<style lang="scss" scoped>
.popup-content {
  border-radius: 16px 16px 0 0;
  background: #fff6f1;
  opacity: 1;
  padding: 24px 16px;
}

.service-card {
  margin-top: 16px;
  padding: 12px 16px;
  background: #ffffff;
  border-radius: 8px;
  opacity: 1;

  &__icon {
    display: block;
    width: 24px;
    height: 24px;
    margin-right: 6px;
    border-radius: 4px;
  }

  &__text {
    font-size: 18px;
    font-weight: bold;
    color: #333333;
  }

  &__origin-price {
    font-size: 15px;
    font-weight: 400;
    color: #999999;

    &__icon {
      display: block;
      width: 18px;
      height: 18px;
      margin-left: 3px;
    }
  }

  &__price {
    font-size: 13px;
    font-weight: 400;
    color: #999999;

    &__count {
      font-size: 20px;
      font-weight: bold;
      color: #f34747;
    }
  }
}

.button {
  &__pay,
  &__cancel {
    margin-top: 32px;
    width: 164px;
    height: 44px;
    background: #ffffff;
    border-radius: 68px;
    font-size: 16px;
    opacity: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__pay {
    position: relative;
    background: linear-gradient(90deg, #fa700d 0%, #f34747 100%);
    color: #ffffff;

    &__icon {
      position: absolute;
      top: -17px;
      right: 9px;
      width: 108px;
      height: 26px;
    }
  }

  &__cancel {
    background: #ffffff;
    color: #666666;
  }
}
</style>
