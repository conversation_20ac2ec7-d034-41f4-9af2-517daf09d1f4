<template>
  <div>
    <p class="case-type-title">
      案件类型
    </p>
    <!-- 这是选择的部分 -->
    <div class="select-box flex flex-align-center">
      <p>{{ typeLabel }}</p>
    </div>
  </div>
</template>

<script>
/**
 * 案件类型选择，暂时是一个假的选择框
 * <AUTHOR>
 * @Version 2.10.0
 * @Description 该版本改动信息
 * @Date 2022/4/26
 * @FilePath components\case-details\case-type-select.vue
 */
export default {
  name: "CaseTypeSelect",
  props: {
    typeLabel: {
      type: String,
      default: "婚姻家庭",
    },
  },
};
</script>

<style lang="scss" scoped>
.case-type {
  &-title {
    font-size: 16px;
    font-weight: bold;
    color: #333333;
    margin-bottom: 12px;
  }
}

.select-box {
  height: 38px;
  background: #f5f5f5;
  border-radius: 30px 30px 30px 30px;
  font-size: 15px;
  font-weight: 400;
  color: #999999;
  padding: 0 16px;
}
</style>
