<template>
  <div class="mb-[16px]">
    <if f="hjls">
      <img
        v-if="timeIsNight"
        class="block w-[343px] h-[159px] mb-[16px]"
        alt=""
        src="@/pages/submit-question/imgs/adv/5dapjr.png"
      >
      <div
        v-else
        class="position-relative mb-[16px]"
      >
        <div
          class="w-full h-[68px] bg-[#FFFFFF] [box-shadow:0px_0px_10px_0px_rgba(0,0,0,0.02)] rounded-tl-[12px] rounded-br-none rounded-tr-[12px] rounded-bl-none pl-[12px] pt-[6px] box-border"
        >
          <swiper
            :autoplay="true"
            :interval="3000"
            circular
            class="w-[327px] h-[36px]"
            vertical
          >
            <swiper-item
              v-for="item in cardUrl"
              :key="item.id"
            >
              <img
                :src="item.imageUrl"
                alt=""
                class="h-[36px]"
                mode="heightFix"
              >
            </swiper-item>
          </swiper>
        </div>
        <img
          alt=""
          class="w-full h-[146px] -mt-[20px] relative"
          src="@/pages/submit-question/imgs/adv/m2401j.png"
        >
      </div>
    </if>
    <if t="hjls">
      <div>
        <img
          alt=""
          class="w-[351px] h-[146px] mb-[16px]"
          src="@/pages/submit-question/imgs/adv/1231.png"
        >
      </div>
    </if>
  </div>
</template>

<script>
import nightTime from "@/mixins/nightTime";

export default {
  name: "CardAdv",
  mixins: [nightTime],
  props: {
    currentData: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    cardUrl() {
      return [
        {
          id: 1,
          imageUrl: require("@/pages/submit-question/imgs/adv/1.png"),
        },
        {
          id: 2,
          imageUrl: require("@/pages/submit-question/imgs/adv/2.png"),
        },
        {
          id: 3,
          imageUrl: require("@/pages/submit-question/imgs/adv/3.png"),
        },
      ];
    },
  },
};
</script>
