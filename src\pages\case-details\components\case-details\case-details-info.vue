<template>
  <div class="details-info">
    <div
      v-for="(item,index) in dataSource"
      :key="index"
      class="info-item"
    >
      <div class="info-item-title">
        {{ item.caseSourceV2Problem.problemInfo }}
      </div>
      <div class="info-item-content">
        {{ transformAnswer(item) }}
      </div>
    </div>
  </div>
</template>
<script>
/**
 * 案件详情信息，只能够进行查看
 *
 */
export default {
  name: "CaseDetailsInfo",
  props: {
    /** 需要展示的数据 */
    dataSource: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    /** 将后端返回来的答案转换成中文 */
    transformAnswer(dataItem) {
      const resultDetail = dataItem.caseSourceV2Problem?.resultDetails;
      const resultValue = Number(dataItem.caseSourceV2Result?.resultValue);
      return (
        resultDetail?.find((item) => item.answerValue === resultValue)
          ?.answerLabel || resultValue / 100
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.details-info {
  background: #F5F5F7;
  border-radius: 8px 8px 8px 8px;
  padding: 14px 16px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 28px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item-title {
  font-size: 14px;
  font-weight: 400;
  color: #666666;
}

.info-item-content {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
}
</style>

