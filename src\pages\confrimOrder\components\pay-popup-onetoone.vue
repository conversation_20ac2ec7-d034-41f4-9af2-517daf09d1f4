<template>
  <!-- <van-popup v-model="getShow" round position="bottom" close-on-popstate :close-on-click-overlay="true" class="popup-order pc-max-w pc-max-w-fixed-center"> -->
  <u-popup
    :show="getShow"
    @close="close"
    @open="open"
  >
    <view class="card-over">
      <h3>还差一点就可享受金牌律师1对1服务了<br>真的要放弃吗？</h3>
      <p class="title">
        放弃您将失去以下特权：
      </p>
      <view class="list flex flex-align-center">
        <view
          v-for="(item, index) in list"
          :key="'llist' + index"
          class="item flex-column-centered flex-1"
        >
          <img
            :src="item.img"
            alt=""
            srcset=""
          >
          <p class="font12">
            {{ item.desc1 }}<br>{{ item.desc2 }}
          </p>
        </view>
      </view>
      <view class="button flex flex-space-between">
        <p
          class="btn font16"
          @click="handle"
        >
          稍后支付
        </p>
        <p
          class="btn font16"
          @click="onClick"
        >
          继续支付
        </p>
      </view>
    </view>
  </u-popup>

  <!-- </van-popup> -->
</template>

<script>
// import { Popup } from 'vant'
import { getReturnRouterStorage } from "@/libs/token";
import UPopup from "@/uview-ui/components/u-popup/u-popup.vue";

export default {
  name: "Index",
  components: {
    UPopup
    // 'van-popup': Popup
  },
  props: {
    callBack: {
      type: Function,
      default: () => true
    },
    value: {
      type: Boolean,
      default: false
    }
    // serviceTypeCode: {
    //   type: [String, Number],
    //   default: ''
    // }
  },
  data() {
    return {
      list: [
        {
          img: require("@/assets/confirm-order/1v1zsfw.png"),
          desc1: "1对1",
          desc2: "专属服务"
        },
        {
          img: require("@/assets/confirm-order/jplsjsxy.png"),
          desc1: "金牌律师",
          desc2: "极速响应"
        },
        {
          img: require("@/assets/confirm-order/xsyhfl.png"),
          desc1: "限时",
          desc2: "优惠福利"
        }
      ]
    };
  },
  computed: {
    getShow: {
      set(value) {
        this.$emit("input", value);
      },
      get() {
        return this.value;
      }
    }
  },
  watch: {
    // getShow(val){
    //   /* 服务弹窗code获取*/
    //   val && this.initInfo()
    // }
  },
  methods: {
    onClick() {
      this.$emit("input", false);
    },
    handle() {
      this.$router.replace(getReturnRouterStorage());
    }
  }
};
</script>

<style lang="scss" scoped>
.popup-order {
  border-radius: 20px 20px 0 0;
}
.header {
  width: 101%;
  height: 108px;
  margin-left: -0.5px;
}
.card-over {
  overflow-x: hidden;
  padding: 24px 16px;
  h3 {
    font-size: 18px;
    color: #111111;
  }
  .title {
    font-size: 14px;
    color: #999999;
    margin: 16px 0 24px 0;
  }
  .list {
    .item {
      img {
        width: 54px;
        height: 54px;
        margin-bottom: 8px;
        border-radius: 50%;
      }
      p {
        color: #333333;
        text-align: center;
      }
    }
  }
  .button {
    margin-top: 28px;
    .btn {
      width: 164px;
      height: 44px;
      border-radius: 68px 68px 68px 68px;
      background: linear-gradient(90deg, #fa700d 0%, #eb4738 100%);
      color: #fff;
      text-align: center;
      line-height: 44px;
      &:nth-child(1) {
        color: #333333;
        background: #f5f5f7;
        border: 1px solid #f5f5f7;
      }
    }
  }
}
</style>
