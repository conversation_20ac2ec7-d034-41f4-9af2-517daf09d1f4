// utils/MiniProgramBridge.js

class MiniProgramBridge {
  constructor() {
    this.pageInstance = null;
    this.webViewSrcKey = "h5Url"; // Page.data 中用于 web-view src 的 key
    this.baseH5Url = "";
    this.handlers = {}; // 存储 H5 action 的处理器 { action: handlerFunc }
    this.hashId = 0; // 用于确保 hash 每次都变化
  }

  /**
   * 初始化 Bridge
   * @param {object} pageInstance - 调用页面的 Page 实例 (this)
   * @param {string} webViewSrcKey - Page data 中 web-view src 使用的 key (默认 'h5Url')
   * @param {string} baseH5Url - H5 页面的基础 URL (不含 hash 和 query)
   */
  init(pageInstance, webViewSrcKey = "h5Url", baseH5Url) {
    if (!pageInstance) {
      console.error("[MiniProgramBridge] Invalid pageInstance provided.");
      return;
    }
    if (!baseH5Url) {
      console.error(
        "[MiniProgramBridge] baseH5Url is required for sending messages."
      );
      // Try to derive from current src if possible, but explicit is better
      const currentUrl = pageInstance[webViewSrcKey];
      if (currentUrl && typeof currentUrl === "string") {
        this.baseH5Url = currentUrl.split("#")[0].split("?")[0];
        console.warn(
          `[MiniProgramBridge] baseH5Url not provided, derived as: ${this.baseH5Url}. Provide it explicitly for reliability.`
        );
      } else {
        return; // Cannot proceed without base URL
      }
    } else {
      this.baseH5Url = baseH5Url;
    }
    this.pageInstance = pageInstance;
    this.webViewSrcKey = webViewSrcKey;
    this.handlers = {};
    this.hashId = 0;
    console.log("[MiniProgramBridge] Initialized.");
  }

  /**
   * 注册处理 H5 消息的 Handler
   * @param {string} action - H5 发送消息中的 action 字段
   * @param {function} handler - 处理函数，接收参数 (payload, message) => {}
   *                           payload 是消息的 payload 字段
   *                           message 是完整的消息对象
   */
  registerHandler(action, handler) {
    if (typeof handler !== "function") {
      console.error(
        `[MiniProgramBridge] Handler for action "${action}" must be a function.`
      );
      return;
    }
    this.handlers[action] = handler;
    console.log(`[MiniProgramBridge] Handler registered for action: ${action}`);
  }

  /**
   * 处理来自 H5 的 postMessage 事件
   * 需要在 Page 的 bindmessage 事件回调中调用此方法
   * @param {object} event - 小程序 bindmessage 事件对象
   */
  handleMessageEvent(event) {
    if (!event || !event.detail || !event.detail.data) {
      console.warn(
        "[MiniProgramBridge] Received invalid message event:",
        event
      );
      return;
    }

    const messages = event.detail.data;
    // postMessage 可能会合并短时间内发送的多条消息为一个数组
    const messageList = Array.isArray(messages) ? messages : [messages];

    messageList.forEach((message) => {
      console.log("[MiniProgramBridge] Received message from H5:", message);
      if (message && typeof message === "object" && message.action) {
        const handler = this.handlers[message.action];
        if (handler) {
          try {
            // 提供一个响应函数给 handler，方便直接回复
            const respond = (responsePayload, success = true) => {
              if (message.type === "request" && message.requestId) {
                this.sendResponse(message.requestId, responsePayload, success);
              } else {
                console.warn(
                  `[MiniProgramBridge] Cannot respond. Original message was not a request or missing requestId. Action: ${message.action}`
                );
              }
            };
            handler(message.payload, respond, message); // 传递 payload 和 respond 函数
          } catch (error) {
            console.error(
              `[MiniProgramBridge] Error executing handler for action "${message.action}":`,
              error
            );
            // Optionally send an error response back
            if (message.type === "request" && message.requestId) {
              this.sendResponse(
                message.requestId,
                { error: "Handler execution failed", details: error.message },
                false
              );
            }
          }
        } else {
          console.warn(
            `[MiniProgramBridge] No handler registered for action: ${message.action}`
          );
          // Optionally send a 'not implemented' response back
          if (message.type === "request" && message.requestId) {
            this.sendResponse(
              message.requestId,
              { error: `Action "${message.action}" not handled` },
              false
            );
          }
        }
      } else {
        console.warn(
          "[MiniProgramBridge] Received message without action or invalid format:",
          message
        );
      }
    });
  }

  /**
   * 向 H5 发送消息 (通过更新 web-view src 的 hash)
   * @param {string} action - 消息的操作标识
   * @param {object} payload - 携带的数据
   * @param {string} type - 消息类型 ('event', 'request', 'response')
   * @param {string|null} requestId - 消息 ID (通常在 response 或 request 时使用)
   */
  sendMessage(action, payload, type = "event", requestId = null) {
    if (!this.pageInstance || !this.baseH5Url) {
      console.error(
        "[MiniProgramBridge] Bridge not initialized or baseH5Url missing. Cannot send message."
      );
      return;
    }

    const message = {
      type: type,
      action: action,
      payload: payload,
      requestId: requestId,
      timestamp: Date.now(),
      hashId: ++this.hashId, // 确保 hash 变化
    };

    try {
      const encodedMessage = encodeURIComponent(JSON.stringify(message));
      // 注意 URL 长度限制，过大的 payload 可能导致失败
      if (encodedMessage.length > 1800) {
        // 设定一个保守的阈值 (实际限制可能更高或更低)
        console.warn(
          `[MiniProgramBridge] Message payload for action "${action}" might be too large for URL hash. Consider alternative methods if issues arise.`
        );
      }
      const newUrl = `${this.baseH5Url}#data=${encodedMessage}`;

      console.log(
        `[MiniProgramBridge] Sending message to H5 via hash update. Action: ${action}, New URL: ${newUrl.substring(
          0,
          100
        )}...`
      );

      // 更新页面 data 中的 web-view src
      this.pageInstance[this.webViewSrcKey] = newUrl;
    } catch (error) {
      console.error(
        `[MiniProgramBridge] Failed to stringify or encode message for action "${action}":`,
        error
      );
    }
  }

  /**
   * 发送一个响应消息给 H5，通常用于回复 H5 的 'request' 类型消息
   * @param {string} originalRequestId - H5 请求消息的 requestId
   * @param {object} payload - 响应的数据
   * @param {boolean} success - 指示操作是否成功 (可选，建议 payload 中包含状态)
   */
  sendResponse(originalRequestId, payload, success = true) {
    // 可以在 payload 中标准化地包含 success 状态
    const responsePayload = {
      success: success,
      data: success ? payload : undefined,
      error: !success ? payload : undefined,
    };
    this.sendMessage(
      "response",
      responsePayload,
      "response",
      originalRequestId
    );
  }

  /**
   * 发送一个事件通知给 H5
   * @param {string} action - 事件名称
   * @param {object} payload - 事件数据
   */
  sendEvent(action, payload) {
    this.sendMessage(action, payload, "event");
  }
}

// 单例模式导出，确保整个小程序只有一个 Bridge 实例
const h5Bridge = new MiniProgramBridge();

export default h5Bridge;
