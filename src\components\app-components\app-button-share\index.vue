<template>
  <div class="w-full h-full">
    <button
      open-type="share"
      @click="share"
    >
      <slot />
    </button>
  </div>
</template>

<script>
import { isNull } from "@/libs/basics-tools.js";
import { buryPointChannelBasics } from "@/api/burypoint.js";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint.js";

export default {
  name: "AppButtonShare",
  props: {
    /* 埋点*/
    buryThePoint: {
      type: String,
      default: ""
    },
  },
  methods: {
    share(){
      if(!isNull(this.buryThePoint)){
        buryPointChannelBasics({
          code: this.buryThePoint,
          type: 1,
          behavior: BURY_POINT_CHANNEL_TYPE.CK,
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
// 重置按钮样式
button {
  width: 100%;
  height: 100%;
  background: none;
  border-radius: 0;
  border: none;
  outline: none;
  padding: 0;
  margin: 0;
  font-size: inherit;
  color: inherit;
  display: block;
}
</style>
