// postcss-transform-color.js
const postcss = require("postcss");
const { getPlatform } = require("./utils");
const { appPlatform } = require("./default");

/* 将16进制颜色转化成rgb*/
const hexToRgb = hex => {
  const rgb = [];
  hex = hex.substr(1); // 去除前缀 # 号

  if (hex.length === 3) {
    // 处理 "#abc" 成 "#aabbcc"
    hex = hex.replace(/(.)/g, "$1$1");
  }

  hex.replace(/../g, color => {
    rgb.push(parseInt(color, 0x10)); // 按16进制将字符串转换为数字
  });
  return rgb;
};
const transformColorPlugin = postcss.plugin("transform-color", opts => {
  opts = opts || {};
  const { colorMap, resourcePath } = opts;
  console.log("开始替换颜色", colorMap);
  return (root) => {
    root.walkRules((rule) => {
      rule.walkDecls(function (decl) {
        const caseValue = decl.value.toLowerCase();


        if (decl.value.indexOf("if") !== -1) {
          // 如果css含有条件编译
          const currPlatform = getPlatform();

          const platform = appPlatform;
          // 提取p_后面的值
          const platformValue = decl.value.split("if")[1];

          // 如果平台中声明了当前平台
          const value = platform.find(item => item === platformValue);

          if (value === currPlatform) {
            // 删除后面的条件 类似于 p_hjls
            decl.value = decl.value.replace("if" + platformValue, "");
          } else {
            // 删除当前声明的css
            decl.remove();
          }
        }

        /* 禁止编译*/
        if (decl.value.indexOf("noc") !== -1) {
          decl.value = decl.value.replace("noc", "");
        }else{
          colorMap.forEach(({ source, target }) => {
            /* 转小写*/
            const lowerCaseSource = source.toLowerCase();
            const sourceRgb = hexToRgb(source).join(" ");
            /* 防止写rgb格式*/
            const targetRgb = hexToRgb(target).join(" ");
            [lowerCaseSource, sourceRgb].forEach(item => {
              if (caseValue.indexOf(item) !== -1) {
                decl.value = caseValue.replace(item, item === sourceRgb ? targetRgb : target);
              }
            });
          });
        }

      });
    });

  };
});

module.exports = transformColorPlugin;
