<template>
  <div class="flex flex-space-between flex-align-center">
    <div class="header-title flex items-center">
      精选律师
      <div
        class="header-sub-title text-[14px] text-[#9F6310] flex items-center"
      >
        <img
          alt=""
          class="w-[12px] h-[20px] block"
          src="../../pages/submit-question/findlawyer/imgs/<EMAIL>"
        >
        <div class="mx-[2px]">
          2W+认证律师 平台严选
        </div>
        <img
          alt=""
          class="w-[12px] h-[20px] block"
          src="../../pages/submit-question/findlawyer/imgs/<EMAIL>"
        >
      </div>
    </div>
    <!--    <div-->
    <!--      class="right-box flex flex-align-center"-->
    <!--      @click="toAllLawyer"-->
    <!--    >-->
    <!--      全部 <img-->
    <!--        class="right-arrow"-->
    <!--        src="@/pages/submit-question/findlawyer/imgs/<EMAIL>"-->
    <!--        alt=""-->
    <!--      >-->
    <!--    </div>-->
    <!--    <div class="notice">-->
    <!--      <u-notice-bar-->
    <!--          :customStyle="{ padding: 0 }"-->
    <!--          :fontSize="12"-->
    <!--          :icon="require('@/pages/lawyer-home/img/volume.png')"-->
    <!--          :text="fictitiousLists"-->
    <!--          bgColor="#f5f5f7"-->
    <!--          color="#999999"-->
    <!--          direction="column">-->
    <!--      </u-notice-bar>-->
    <!--    </div>-->
  </div>
</template>

<script>
import { fastConsultBuyUsers } from "@/api/order.js";
// import UNoticeBar from "@/uview-ui/components/u-notice-bar/u-notice-bar.vue";

export default {
  name: "FindLawyerHeader",
  // components: {UNoticeBar},
  data() {
    return {
      fictitiousLists: []
    };
  },
  computed: {
    isLogin() {
      return !!this.$store.state.user.token;
    }
  },
  created() {
    this.getNotice();
  },
  methods: {
    getNotice() {
      fastConsultBuyUsers().then(res => {
        this.fictitiousLists = res.data?.map(el => el.scrollInfo) || [];
      });
    },
    // 跳转所有二级找律师页面
    toAllLawyer() {
      uni.navigateTo({ url: "/pages/lawyer-home/find-lawyer/index" });
    }
  }
};
</script>

<style lang="scss" scoped>
.header-title {
  font-size: 18px;
  color: #222222;
  .header-sub-title {
    padding-left: 16px;
    position: relative;
    &:after {
      position: absolute;
      content: "";
      height: 14px;
      opacity: 1;
      border: 1px solid #cccccc;
      left: 8px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
.right-box {
  font-size: 14px;
  font-weight: 400;
  color: #999999;
  .right-arrow {
    width: 16px;
    height: 16px;
  }
}
.notice {
  width: 160px;
}
</style>
