.order-item {
    background: #ffffff;
    box-sizing: border-box;
    padding: 0 12px 0 12px;
    border-radius: 8px;
    margin: 12px 16px 0;
    .order-bottom {
      height: 50px;
      justify-content: flex-end;
      .btn {
        width: 74px;
        height: 32px;
        font-size: 14px;
        border-radius: 20px;
        border: 1px solid #EEEEEE;
        color: #333;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        margin-right: 12px;
        box-sizing: border-box;
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
