class Router {
  constructor() {
    // 页面切换经过的路由
    this.routes = [];
    // 当前路由
    this.currentUrl = "";
  }

  /** 记录当前页面的路由 */
  push(path) {
    console.log(path, "当前页面的路由");

    this.currentUrl = path;

    // 是否已经存在路由
    if(this.routes.includes(path)) {
      // 如果路由已经存在，将路由放在最后一个
      this.routes.splice(this.routes.indexOf(path), 1);
    }

    this.routes.push(path);
  }

  /** 上个路由是否是问律师 */
  isLastAskLawyer() {
    return this.routes[this.routes.length - 2] === "/pages/askLawyer/index";
  }
}

const router = new Router();

export default router;
