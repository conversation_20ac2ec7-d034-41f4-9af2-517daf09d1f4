<template>
  <div class="refund_tip h-[30px] px-[10px]  rounded-[4px] bg-[#ffffff] box-border">
    <span><img
      alt=""
      class="icon"
      src="@/pages/lawyer-im/assets/tuikuan.png"
    >用户已申请退款，服务已关闭
    </span>
  </div>
</template>

<script>
import { refundInfoNew } from "@/api/order.js";

export default {
  name: "ImChatroomTypeRefundTip",
  inheritAttrs: false,
  props: {
    theme: {
      type: String,
      default: "",
    },
    caseSourceInfo: {
      type: Object,
      default: () => ({}),
    },
    customData: {
      type: Object,
      default: () => ({}),
    },
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      refundInfo: {},
    };
  },
  computed: {
    getData() {
      console.log(this.data, "xxxxxxxxxx");
      return this.customData?.customExts ? this.customData?.customExts : {};
    },
  },
  mounted() {
    if (this.caseSourceInfo.orderId) {
      refundInfoNew({ orderId: this.caseSourceInfo.orderId }).then(
        ({ data }) => {
          this.refundInfo = data;
        }
      );
    }
  },
  methods: {
    /** 点击查看详情提示文案 */
    showTips() {
      this.$toast("用户已申请退款，服务已结束");
    },
  },
};
</script>

<style lang="scss" scoped>
.icon {
  width: 16px;
  height: 16px;
  display: block;
}
.refund_tip {
  width: 100%;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #EB4738;
}
</style>
