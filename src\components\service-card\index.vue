<template>
  <div class="consult-card-content">
    <div class="top flex flex-space-between">
      <img
        :src="serviceInfo.icon"
        alt=""
      >
      <div class="title flex-1 flex flex-column flex-wrap flex-space-between">
        <p class="name">
          {{ serviceInfo.serviceName }}
        </p>
        <p class="desc">
          {{ label || caseSourceInfo.typeLabel }}行业金牌律师
        </p>
      </div>
      <div class="moneys">
        <p class="current-price">
          ¥{{ priceNumber(serviceInfo.servicePrice) }}
        </p>
        <p class="original-price">
          原价¥{{ priceNumber(serviceInfo.originalPrice) }}
        </p>
      </div>
    </div>
    <div class="bottom flex flex-align-center flex-space-between">
      <p class="desc">
        24小时内随意咨询,不限制沟通次数
      </p>
      <div class="btn">
        立即咨询
      </div>
    </div>
  </div>
</template>

<script>

import { BEHAVIOR, POINT_CODE } from "@/libs/config.js";
import { priceNumber } from "@/libs/tool.js";

export default {
  name: "ServiceCard",
  props: {
    serviceInfo: {
      type: Object,
      default: () => ({}),
    },
    caseSourceInfo: {
      type: Object,
      default: () => ({}),
    },
    label: {
      type: String,
      default: undefined,
    },
  },
  methods: {
    priceNumber,
  },
};
</script>

<style lang="scss" scoped>
.consult-card-content {
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #ffa701;
  padding: 10px 12px;

  .consult-box-header {
    img {
      width: 44px;
      height: 44px;
      border-radius: 5px;
      overflow: hidden;
    }
  }

  .title {
    padding-left: 12px;
    .name {
      font-size: 16px;
      font-weight: bold;
      color: #333333;
    }

    .desc {
      font-size: 12px;
      font-weight: 400;
      color: #999999;
    }
  }

  .moneys {
    .current-price {
      font-size: 23px;
      font-weight: bold;
      color: #EB4738;
    }

    .original-price {
      font-size: 11px;
      font-weight: 400;
      text-align: center;
      text-decoration: line-through;
      color: #999999;
    }
  }

  .bottom {
    padding-top: 7px;

    .desc {
      font-size: 12px;
      font-weight: 400;
      color: #d4b37c;
    }

    .btn {
      width: 88px;
      line-height: 32px;
      background: #e24d40;
      border-radius: 68px 68px 68px 68px;
      font-size: 14px;
      font-weight: 400;
      text-align: center;
      color: #ffffff;
    }
  }
}
</style>
