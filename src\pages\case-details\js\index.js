// import { buryPointChannelBasics } from "@/api/burypoint.js";
// import { BURY_POINT_CHANNEL_TYPE, POINT_CODE } from "@/enum/burypoint.js";
/**
	 * 2.10.0 埋点 页面访问PV、UV埋点
	 * @param {Number} sourcePage2DataStatus 留资2页资料完善状态 0-未完善 1-完善
	 */
// export function caseDetailsPageBuriedPoint(extra) {
// 	buryPointChannelBasics({
// 		code: POINT_CODE.LAW_PAGE_CASE_DETAILS,
// 		type: 1,
// 		behavior: BURY_POINT_CHANNEL_TYPE.VI,
// 		extra: {...extra}
// 	})
// }
/**
	 * 2案件详情完善案件资料点击按钮埋点
	 */
// export function caseDetailsPerfectCaseBuriedPoint() {
// 	buryPointChannelBasics({
// 		code: POINT_CODE.LAW_PAGE_CASE_DETAILS_COMPLETE_CASE_PROFILE_CLICK,
// 		type: 1,
// 		behavior: BURY_POINT_CHANNEL_TYPE.CK,
// 	})
// }
