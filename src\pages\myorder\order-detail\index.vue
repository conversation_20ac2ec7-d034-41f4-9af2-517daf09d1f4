<template>
  <div>
    <!-- 服务信息区域 -->
    <div class="bg-[#FFFFFF] p-[16px] box-border">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <img
            class="block w-[44px] h-[44px] shrink-0 rounded-[4px] mr-[12px]"
            alt=""
            :src="orderData.icon"
          >
          <span class="font-bold text-[17px] text-[#333333]">{{
            orderData.serviceName
          }}</span>
        </div>
        <div
          :style="{ color: statusInfo.color }"
          class="text-[15px]"
        >
          {{ statusInfo.txt }}
        </div>
      </div>
    </div>

    <!-- 订单信息区域 -->
    <div
      class="bg-[#FFFFFF] mt-[12px] px-[12px] py-[16px]"
    >
      <div
        v-if="orderData.info"
        class="mb-[28px]"
      >
        <div class="text-[14px] text-[#666666] mb-[12px]">
          咨询问题
        </div>
        <div class="bg-[#F5F5F7] rounded-[8px] p-[12px] box-border">
          {{ orderData.info }}
        </div>
      </div>
      <div class="flex justify-between items-center mb-[24px]">
        <span class="text-[14px] text-[#666666]">购买时间：</span>
        <span class="text-[14px] text-[#333333]">{{
          orderData.createTime
        }}</span>
      </div>

      <div
        v-if="orderData.payOrderNo"
        class="flex justify-between items-center mb-[24px]"
        @click="copyOrderNo"
      >
        <span class="text-[14px] text-[#666666]">支付单号：</span>
        <span class="text-[14px] text-[#333333]">{{
          orderData.payOrderNo
        }}</span>
      </div>

      <div class="flex justify-between items-center">
        <span class="text-[14px] text-[#666666]">购买价格：</span>
        <span class="text-[16px] font-bold text-[#333333]">¥{{ orderData.payAmount | amountFilter }}</span>
      </div>
    </div>

    <!-- 底部按钮区域 -->
    <div
      class="bg-[#FFFFFF] mt-[12px] px-[12px] py-[16px] fixed bottom-0 left-0 right-0"
    >
      <!-- 去支付按钮 -->
      <div
        v-if="showPayBtn"
        class="w-full h-[44px] bg-[#3A88F5] rounded-[100px] flex items-center justify-center position-relative"
        @click="toPay"
      >
        <div 
          v-if="Number(orderData.remainPayTime)"
          class="px-[8px] py-[2px] bg-[linear-gradient(_109deg,_#FF913E_0%,_#F54A3A_100%)] rounded-tl-[10px] rounded-br-[10px] rounded-tr-[10px] rounded-bl-[2px] flex items-center justify-center absolute -top-[16px] right-[16px]"
        >
          <div class="font-bold text-[12px] text-[#FFFFFF] flex items-center justify-center">
            <u-count-down
              v-if="Number(orderData.remainPayTime)"
              :time="Number(orderData.remainPayTime) * 1000"
              format="HH:mm:ss"
              :customStyle="{ color: '#FFFFFF', fontSize: '24rpx' }"
              @change="change"
            />
            <div class="ml-[4px]">
              订单失效
            </div>
          </div>
        </div>
        <span class="text-[16px] text-[#FFFFFF] font-bold">去支付</span>
      </div>

      <!-- 再次咨询按钮 -->
      <div
        v-if="isCloseStatus"
        class="w-full h-[44px] bg-[#3A88F5] rounded-[100px] flex items-center justify-center position-relative"
        @click="toAskLawyer"
      >
        <div 
          class="px-[8px] py-[2px] bg-[linear-gradient(_109deg,_#FF913E_0%,_#F54A3A_100%)] rounded-tl-[10px] rounded-br-[10px] rounded-tr-[10px] rounded-bl-[2px] flex items-center justify-center absolute -top-[8px] right-[16px]"
        >
          <div class="font-bold text-[12px] text-[#FFFFFF]">
            免费问
          </div>
        </div>
        <span class="text-[16px] text-[#FFFFFF] font-bold">再次咨询</span>
      </div>

      <!-- 售后客服按钮 -->
      <div
        v-if="isPayStatus"
        class="w-full h-[44px] bg-[#3A88F5] rounded-[100px] flex items-center justify-center position-relative"
        @click="toWeChatCustomerService"
      >
        <span class="text-[16px] text-[#FFFFFF] font-bold">售后客服</span>
        <div class="absolute -top-[8px] right-[16px] w-[102px] h-[20px] bg-[linear-gradient(_109deg,_#FF913E_0%,_#F54A3A_100%)] rounded-tl-[10px] rounded-br-[10px] rounded-tr-[10px] rounded-bl-[2px] flex items-center justify-center">
          <div class="font-bold text-[12px] text-[#FFFFFF]">
            9:00-18:00在线
          </div>
        </div>
      </div>

      <!-- 换律师按钮 -->
      <div
        v-if="showChangeLawyerBtn"
        class="w-full h-[44px] bg-[#F0F8FF] rounded-[100px] font-bold text-[16px] text-[#3A88F5] flex items-center justify-center mt-[16px]"
        @click="toChangeLawyer"
      >
        换律师（免费）
      </div>

      <!-- 退款按钮 -->
      <div
        v-if="showRefundBtn"
        class="text-[16px] text-[#999999] mt-[16px] text-center"
        @click="toRefund"
      >
        退款
      </div>

      <!-- 换律师详情按钮 -->
      <div
        v-if="changeLawyerBtnText"
        class="text-[16px] text-[#999999] mt-[16px] text-center"
        @click="toChangeLawyerDetail"
      >
        {{ changeLawyerBtnText }}
      </div>

      <!-- 退款详情按钮 -->
      <div
        v-if="refundBtnText"
        class="text-[16px] text-[#999999] mt-[16px] text-center"
        @click="toRefundDetail"
      >
        {{ refundBtnText }}
      </div>

      <!-- 申请售后按钮 -->
      <div
        v-if="showApplyAfterSaleBtn"
        class="text-[16px] text-[#999999] mt-[16px] text-center"
        @click="toApplyAfterSale"
      >
        申请售后
      </div>
      <u-safe-bottom />
    </div>

    <!-- 弹窗组件 -->
    <app-modal
      :show="showChangeLawyerPopup"
      cancelText="我要换律师"
      confirmText="取消"
      content="当前律师尚未接单，选择换律师服务，平台将派选优质在线律师尽快为您服务"
      title="换律师确认"
      @cancel="clickChangeLawyerPopupLeft"
      @confirm="clickChangeLawyerPopupRight"
    />
  </div>
</template>

<script>
import { orderUserGetOrderById } from "@/api/order";
import { speedOrderType, speedRefundType } from "@/libs/config";
import { formatSpeedOrderType } from "@/libs/tools";
import {
  toApplyAfterSale,
  toChangeLawyer,
  toChangeLawyerDetail,
  toRefund,
  toRefundDetail,
  toConfirmOrder,
  toAskLawyer,
} from "@/libs/turnPages";
import { toWeChatCustomerService } from "@/libs/tools";
import AppModal from "@/components/app-components/app-modal/index.vue";
import dayjs from "dayjs";
import orderTimeMixin from "@/pages/myorder/order-item/mixins/orderTimeMixin.js";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE, POINT_CODE } from "@/enum/burypoint";
import { amountFilter } from "@/libs/filter";
import { getReturnRouterStorage } from "@/libs/token.js";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath.js";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import UCountDown from "@/uview-ui/components/u-count-down/u-count-down.vue";

export default {
  name: "OrderDetail",
  components: {
    AppModal,
    USafeBottom,
    UCountDown,
  },
  filters: {
    amountFilter,
  },
  mixins: [orderTimeMixin],
  data() {
    return {
      orderId: "",
      orderData: {},
      showChangeLawyerPopup: false,
      time: 0,
    };
  },
  onLoad({ id }) {
    this.orderId = id;
    this.getOrderDetail();
  },
  computed: {
    /** 订单状态信息 */
    statusInfo() {
      return (
        formatSpeedOrderType(this.orderData.status) || {
          txt: "已支付",
          color: "#999999",
        }
      );
    },

    /** 是否显示换律师按钮 */
    showChangeLawyerBtn() {
      return (
        this.is1V1Order && !this.isServiceEnd && this.orderData.canChangeLawyer
      );
    },

    /** 是否显示退款按钮 */
    showRefundBtn() {
      if (
        this.isSpeedOrder ||
        this.isAIOrder ||
        this.isItACaseEntrustmentDeposit ||
        this.isItACaseEntrustmentTail
      ) {
        return !!(this.orderData.refundable && !this.orderData.refundId);
      }

      return (
        (this.is1V1Order || this.isPlatformOrder) &&
        !this.isServiceEnd &&
        this.orderData.refundable
      );
    },

    /** 是否显示申请售后按钮 */
    showApplyAfterSaleBtn() {
      if (
        this.orderData.changeLawyerCheckStatus === 0 ||
        this.orderData.refundStatus === speedRefundType.REFUNDING ||
        !!this.orderData.refundId || !this.isPayStatus
      )
        return false;

      return this.isServiceEnd;
    },

    /** 是否显示去支付按钮 */
    showPayBtn() {
      return this.orderData.operatorStatus === 2;
    },

    /** 是否是已支付状态 */
    isPayStatus() {
      return this.orderData.status === speedOrderType.PAID;
    },

    /** 是否是已关闭状态 */
    isCloseStatus() {
      return this.orderData.status === speedOrderType.CLOSED;
    },

    /** 换律师按钮文案 */
    changeLawyerBtnText() {
      if (this.orderData.refundStatus) return "";

      switch (Number(this.orderData.changeLawyerCheckStatus)) {
      case 0:
        return "换律师审核中";
      case 1:
        return "售后详情";
      case 2:
        return "申请失败";
      case 9:
        return "售后详情";
      default:
        return "";
      }
    },

    /** 退款按钮文案 */
    refundBtnText() {
      if (this.orderData.refundStatus === speedRefundType.REFUNDING)
        return "退款审核中";

      if (this.orderData.refundStatus === speedRefundType.REFUNDFAILED)
        return "退款失败";

      return "";
    },

    /** 是否是1V1下单未开始服务时 */
    is1V1Order() {
      return !this.orderData.serverStatus && this.is1V1OrderType;
    },

    /** 是否是1V1下单 */
    is1V1OrderType() {
      return this.orderData.createFrom === 1;
    },

    /** 是否是平台咨询未开始服务时 */
    isPlatformOrder() {
      return !this.orderData.serverStatus && !this.is1V1OrderType;
    },

    /** 是否是服务已开始—服务结束24H */
    isServiceEnd() {
      return (
        this.orderData.serverStatus === 1 ||
        (this.orderData.serverStatus === 3 && !this.serviceEndTime)
      );
    },

    /** 服务完成时间距现在时间是否超过24小时 */
    serviceEndTime() {
      const time = dayjs(this.orderData.serverCompleteTime);
      return dayjs().diff(time, "minutes") >= this.orderTime;
    },

    /** 是不是法律意见书 */
    isSpeedOrder() {
      return Number(this.orderData.type) === 9;
    },

    /** 是否是AI畅聊 */
    isAIOrder() {
      return Number(this.orderData.type) === 10;
    },

    /** 是不是案件委托定金 */
    isItACaseEntrustmentDeposit() {
      return (
        this.orderData.serviceClassifyCode &&
        this.orderData.serviceClassifyCode === "suit_case_deposit"
      );
    },

    /** 是不是案件委托尾款 */
    isItACaseEntrustmentTail() {
      return (
        this.orderData.serviceClassifyCode &&
        this.orderData.serviceClassifyCode === "suit_case_final_payment"
      );
    },
  },
  methods: {
    /** 获取订单详情 */
    async getOrderDetail() {
      try {
        const { data } = await orderUserGetOrderById({ orderId: this.orderId });
        this.orderData = data || {};
      } catch (error) {
        console.error("获取订单详情失败:", error);
        this.$toast("获取订单详情失败");
      }
    },

    /** 联系客服 */
    toWeChatCustomerService() {
      toWeChatCustomerService();
    },

    /** 换律师 */
    toChangeLawyer() {
      this.showChangeLawyerPopup = true;
    },

    /** 去换律师详情 */
    toChangeLawyerDetail() {
      toChangeLawyerDetail({
        orderId: this.orderId,
      });
    },

    /** 退款 */
    toRefund() {
      buryPointChannelBasics({
        code: "LAW_APPLET_MINE_PAGE_ORDER_RECORD_REFUND_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
      });

      toRefund({
        orderId: this.orderId,
      });
    },

    /** 去退款详情 */
    toRefundDetail() {
      toRefundDetail({
        orderId: this.orderId,
      });
    },

    /** 申请售后 */
    toApplyAfterSale() {
      toApplyAfterSale({
        orderId: this.orderId,
      });
    },

    /** 换律师弹窗左侧按钮 */
    clickChangeLawyerPopupLeft() {
      this.showChangeLawyerPopup = false;
      toChangeLawyer({
        orderId: this.orderId,
      });
    },

    /** 换律师弹窗右侧按钮 */
    clickChangeLawyerPopupRight() {
      this.showChangeLawyerPopup = false;
    },

    /** 复制订单号 */
    copyOrderNo() {
      if (this.orderData.payOrderNo) {
        uni.setClipboardData({
          data: this.orderData.payOrderNo,
          success: () => {
            this.$toast("支付单号已复制");
          },
        });
      }
    },

    /** 去支付 */
    toPay() {
      toConfirmOrder({
        serviceCode: this.orderData.serviceCode,
        businessId: this.orderData.businessId || "",
        lawyerId: this.orderData.lawyerId || "",
        orderId: this.orderData.id || "",
        /* 这里判断合同模板 单独处理*/
        ...(this.orderData.type === 8 ? {
          type: "8",
          paySuccessCallback: () => {
            buryPointChannelBasics({
              code: POINT_CODE.LAW_APPLET_PAY_SUCCESS,
              behavior: BURY_POINT_CHANNEL_TYPE.VI,
              type: 1,
              extra: {
                // 3.0.9版本新增，溯源是哪个界面支付的
                lastPagePath: getReturnRouterStorage(),
              }
            }).finally(() => {
              buryPointTransformationPath.clear();
            });
            this.$toast("支付成功");
            this.getOrderDetail(); // 刷新订单详情
          }
        } : {})
      });
    },

    /** 倒计时变化处理 */
    change(val) {
      console.log(val);
      if (val) {
        let boo = Object.values(val).every(item => item === 0);
        this.time++;
        // 倒计时结束后刷新订单详情
        boo && this.time > 5 && this.getOrderDetail(); 
        console.log("boo:", boo);
      }
    },

    /** 再次咨询 */
    toAskLawyer() {
      toAskLawyer();
    },
  },
};
</script>
