export default {
  props: {
    /** 是否显示弹窗，支持.sync修饰符 */
    visible: {
      type: Boolean,
      default: false,
    },
    /** 是否显示遮罩层 */
    overlay: {
      type: Boolean,
      default: true,
    },
    /** 弹窗中item的数据 */
    dataSource: {
      type: Array,
      default: () => [],
    },
    /** 选中的值 */
    value: {
      type: Object,
      default: () => ({}),
    },
    /** 是否显示取消按钮 */
    cancellable: {
      type: Boolean,
      default: true,
    },
    /** 是否显示标题栏 */
    showTitle: {
      type: Boolean,
      default: true,
    },
    /** 标题栏文字 */
    titleText: {
      type: String,
      default: "",
    },
    /** 是否点击切换按钮 */
    clickToSwitch: {
      type: Boolean,
      default: false,
    },
    /** 选项布局 */
    layout: {
      type: String,
      default: "horizontal",
      validator(value) {
        return ["horizontal", "vertical"].includes(value);
      },
    },
    /**
     * 每一行有多少个选项
     */
    columnNum: {
      type: Number,
      default: 3,
    },
  },
};
