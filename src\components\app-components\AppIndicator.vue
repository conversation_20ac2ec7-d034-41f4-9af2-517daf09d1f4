<template>
  <div
    class="flex items-center justify-center space-x-[8px] w-full"
  >
    <div
      v-for="(item,index) in list"
      :key="index"
      :class="[
        {
          'opacity-30': value !== index
        }
      ]"
      :style="[{ backgroundColor: color }]"
      class="w-[6px] h-[6px] rounded-[3px]"
    />
  </div>
</template>

<script>

export default {
  name: "AppIndicator",
  props: {
    list: {
      type: Array,
      default: () => []
    },
    value: {
      type: Number,
      default: 0
    },
    color: {
      type: String,
      default: "#666666"
    }
  }
};
</script>
