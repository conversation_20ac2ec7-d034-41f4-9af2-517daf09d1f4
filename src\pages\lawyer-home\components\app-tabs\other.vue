<template>
  <div class="overflow-x-auto">
    <div class="tabs flex h-[49px] items-center">
      <div
        v-for="i in list"
        :key="i.value"
        :class="[
          i.value === value ? '!bg-[#3887F5] text-[#FFFFFF]' : '',
          className,
          bgClassName
        ]"
        class="tab-item ml-[12px] flex-shrink-0 rounded-[17px] text-[12px] font-normal text-[#333333] py-[4px] px-[12px]"
        @click="handleSwitch(i.value)"
      >
        {{ i.label }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AppTabsOther",
  props: {
    value: {
      type: [Number, String],
      default: ""
    },
    list: {
      type: Array,
      default: () => []
    },
    /* 主题*/
    className: {
      type: String,
      default: ""
    },
    /** 背景类名 */
    bgClassName: {
      type: String,
      default: "bg-F5F5F7"
    }
  },
  methods: {
    handleSwitch(value) {
      if(value === this.value) return;
      this.$emit("input", value);
      this.$emit("change", value);
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
