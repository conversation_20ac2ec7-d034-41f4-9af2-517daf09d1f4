<template>
  <div
    :class="{ hideInput }"
    :style="getCustomStyle"
    class="problem-input position-relative"
  >
    <textarea
      v-if="!hideInput"
      ref="textarea"
      :maxlength="maxlength"
      :value="value"
      :showCount="false"
      placeholder-style="color: #CCC;font-size: 28rpx;"
      @blur="handleBlur"
      @focus="handleFocus"
      @input="handleInput"
    />
    <div
      v-if="placeholderShow && !value"
      :style="[placeholderStyle]"
      class="placeholder"
    >
      {{ placeholderText }}
    </div>
    <div class="text-count">
      {{ spanText }}
    </div>
    <!-- 底部的咨询类型按钮 -->
    <div class="footer flex items-center justify-between">
      <div class="flex items-center">
        <p
          v-if="showCount"
          class="text-[12px] text-[#999999]"
        >
          {{ value.length }}/{{ maxlength }}
        </p>
      </div>
      <div
        :style="[addStyle(buttonStyle)]"
        class="button flex flex-space-center flex-align-center"
        @click="showPopup = true"
      >
        <img
          v-if="!selectValue.typeLabel"
          alt=""
          class="plus"
          src="./assets/plus.svg"
        >
        <span>咨询类型<span v-if="selectValue.typeLabel">：{{ selectValue.typeLabel }}</span></span>
      </div>
    </div>

    <app-popup-select
      v-model="selectValue"
      :dataSource="lawyerSpeciality"
      :visible.sync="showPopup"
    />
  </div>
</template>

<script>
import { dataDictionary } from "@/api";
import { getPayLawyerGuideStorage } from "@/libs/token";
import { paralegalHistory } from "@/libs/paralegalTools";
import { getCurrentPageRoute } from "@/libs/turnPages.js";
import AppPopupSelect from "@/components/app-components/app-form-list/app-popup-select/index.vue";
import { addStyle } from "@/uview-ui/libs/function/index.js";

export default {
  name: "PayLawyerGuideTextarea",
  components: { AppPopupSelect },
  props: {
    value: {
      type: String,
      required: true,
      default: ""
    },
    placeholderText: {
      type: String,
      default:
        "为了便于律师精准解答，请详细描述您的问题，至少5个字。（事情经过，证据情况、遇到的问题等）"
    },
    spanText: {
      type: String,
      default: "至少描述5个字"
    },
    scene: String,
    hideInput: {
      type: Boolean,
      default: false
    },
    backgroundColor: {
      type: String,
      default: ""
    },
    /** 右下角的按钮自定义样式 */
    buttonStyle: {
      type: [Object, String],
      default: () => ({})
    },
    /** 自定义样式 */
    customStyle: {
      type: [Object],
      default: () => ({})
    },
    maxlength: {
      type: Number,
      default: 200
    },
    /* 是否显示字数统计*/
    showCount: {
      type: Boolean,
      default: false
    },
    placeholderStyle: {
      type: [String, Object],
      default: ""
    }
  },
  data() {
    return {
      /** 是否显示咨询类型弹窗 */
      showPopup: false,
      /** 案件类型 */
      lawyerSpeciality: [],
      selectValue: {
        typeLabel: "",
        typeValue: null
      },
      placeholderShow: true
    };
  },
  computed: {
    getCustomStyle() {
      return addStyle(
        {
          ...this.customStyle,
          backgroundColor: this.backgroundColor
        },
        "string"
      );
    }
  },
  watch: {
    selectValue(val) {
      console.log("咨询类型:", val);
      this.$emit("select", val);
    },
    showPopup(val) {
      this.$emit("selectPopFun", val);
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getData();
      this.setSelectInfo();
    });
  },
  methods: {
    addStyle,
    /**
     * 给selectValue赋值
     * 从外部进行赋值
     * @param value
     */
    setSelectValue(value) {
      this.selectValue = value;
    },
    /** 如果是订单界面返回，则从缓存中获取数据 */
    returnInfo() {
      if (getCurrentPageRoute().query.type === "3") {
        const fastData = getPayLawyerGuideStorage();

        this.$emit("input", fastData.info);

        const selectValue = {
          typeLabel: fastData?.typeLabel,
          typeValue: Number(fastData?.typeValue || 0)
        };

        this.selectValue = selectValue;

        this.$emit("select", selectValue);
        return true;
      }

      return false;
    },
    /** 如果是从快速咨询跳转过来，则需要将快速咨询选择的数据代入 */
    setSelectInfo() {
      // 1.5.0 获取首页弹框选择的类型
      const cacheTypeValue = uni.getStorageSync("cacheTypeValue");
      if (cacheTypeValue.value !== null) {
        this.selectValue = {
          typeValue: Number(cacheTypeValue.value),
          typeLabel: cacheTypeValue.label
        };
        this.$emit("select", this.selectValue);
      }

      if (this.returnInfo()) return;

      if (
        getCurrentPageRoute().query.type !== "1" &&
        getCurrentPageRoute().query.scene !== "kszx"
      )
        return;

      const selectValue = paralegalHistory.getConsultType();

      console.log(selectValue, "selectValue从历史记录中获取的数据");

      this.selectValue = selectValue;
      this.$emit("select", selectValue);
    },
    handleInput(event) {
      this.$emit("input", event.target.value);
    },
    /** 点击后聚焦 */
    // onPlaceholder() {
    //   this.$refs.textarea.focus()
    // },
    /** 获取案件类型数据 */
    getData() {
      /** 案件类型 */
      try {
        dataDictionary({
          groupCode: "LAWYER_SPECIALITY"
        }).then(lawyerSpeciality => {
          lawyerSpeciality.data.forEach(item => {
            item.value = Number(item.value);
          });

          this.lawyerSpeciality = lawyerSpeciality.data;

          // 如果有缓存，则不设置默认值
          if (getCurrentPageRoute().query.type === "3") return;

          const res = this.lawyerSpeciality.find(el => el.label === "综合咨询");

          if (!res) return;

          this.selectValue = {
            typeValue: Number(res.value),
            typeLabel: res.label
          };

          this.$emit("select", this.selectValue);
        });
      } catch (error) {
        console.log(error);
      }
    },
    handleFocus(event) {
      this.placeholderShow = false;
      this.$emit("focus", event);
    },
    handleBlur(event) {
      this.placeholderShow = true;
      this.$emit("blur", event);
    }
  }
};
</script>

<style lang="scss" scoped>
.problem-input {
  position: relative;
  background: #fff;
  border-radius: 8px;
  //padding: 10px 15px;
  height: 194px;
  box-sizing: border-box;

  &.hideInput {
    height: 50px;
  }
  .footer {
    position: absolute;
    bottom: 12px;
    right: 0;
    left: 0;
  }
  .button {
    padding: 0 8px;
    height: 34px;
    border-radius: 8px 8px 8px 8px;
    background: #edf3f7;
    // #ifdef MP-HJLS
    background: #e2f4ee;
    // #endif
    font-size: 13px;
    font-weight: 400;
    color: #3887f5;
    border: 0.5px solid #a0cffb;
  }

  textarea {
    font-weight: 400;
    border: none;
    outline: none;
    box-sizing: border-box;
    resize: none;
    width: 100%;
    height: 70% !important;
    font-size: 14px;
    background: inherit;
    letter-spacing: 1px;
    line-height: 16px;
  }

  .placeholder-text {
    color: #ccc;
    top: 40px;
    left: 16px;
  }

  .text-count {
    position: absolute;
    left: 16px;
    bottom: 20px;
    font-size: 13px;
    font-weight: 400;
    color: #999;
  }
}

.plus {
  width: 16px;
  height: 16px;
}

.placeholder {
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  position: absolute;
  pointer-events: none;
  font-size: 12px;
  font-weight: 400;
  color: #cccccc;
  line-height: 20px;
}
</style>
