/**
 * 退款说明
 */
export const refundInfoText = {
  title: "退款说明",
  content: [
    "1.订单服务开始前申请退款为系统自动退款。",
    "2.订单服务完成需要退款，需在24小时内申请退款。退款流程：退款申请-平台审核-退款。",
    "3.申请后需要平台审核情况，一般会在3-5个工作日内处理完毕。",
    "4.对退款有疑问可以联系平台客服。",
    "5.每笔订单只有一次退款机会。"
  ]
};

/**
 * 换律师说明
 */
export const changeLawyerInfoText = {
  title: "换律师说明",
  content: [
    "1.申请后需要平台审核情况，工作人员将查看您提供的信息进行处理。",
    "2.工作人员审核通过后将电话联系您，为您更换律师进行服务。",
    "3.每笔订单只有一次更换律师机会，更换律师后不可再申请售后。"
  ]
};


/** 退款界面type */
export const refundTypeEnum = {
  REFUND: "refund",
  CHANGE_LAWYER: "changeLawyer"
};
