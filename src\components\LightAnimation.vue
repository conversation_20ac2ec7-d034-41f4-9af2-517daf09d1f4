<template>
  <div class="position-relative">
    <img
      class="block h-full light"
      alt=""
      mode="heightFix"
      src="@/pages/submit-question/to-be-paid/animation/imgs/Frame1321315952.png"
    >
    <slot />
  </div>
</template>

<script>
export default {
  name: "LightAnimation"
};
</script>

<style lang="scss" scoped>
@keyframes btn-light {
  0% {
    left: 0;
  }
  10% {
    left: 5%;
  }
  100% {
    left: 100%;
  }
}

.light {
  position: absolute;
  left: 0;
  top: 0;
  animation: btn-light 2s cubic-bezier(0.92, 0.17, 1, 1) infinite;
}
</style>
