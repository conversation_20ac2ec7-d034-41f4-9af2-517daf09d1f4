<template>
  <div>
    <div class="input-box">
      <div class="mb-[8px] absolute -top-[38px] left-[24px]">
        <privacy-protection :privacyText="privacyText" />
      </div>
      <div
        v-if="guessYouWantSwitch"
        class="px-[12px] py-[8px]"
      >
        <guess-you-want-ask
          uvCode="LAW_APPLET_ASK_LAWYER_GUESS_YOU_WANT_ASK_QUESTION_CONFIG_CLICK"
          @click="historySelect"
        />
      </div>
      <div class="input">
        <div
          v-if="!isVoiceInput"
          class="flex flex-align-center flex-space-between"
        >
          <!-- #ifdef MP-WEIXIN -->
          <if f="hjls">
            <img
              alt=""
              class="w-[34px] h-[34px] mr-[12px] block shrink-0"
              src="@/pages/submit-question/components/AppVoiceInput/img/2.png"
              @click="isVoiceInput = true"
            >
          </if>
          <!-- #endif -->
          <div
            class="input-container"
            @click="handleClickInput"
          >
            <!-- #ifdef MP-WEIXIN -->
            <u--textarea
              ref="input"
              v-model="info"
              :cursorSpacing="50"
              :disabled="disabled"
              :focus="autoFocus"
              :maxlength="500"
              :placeholder="placeholder"
              :showConfirmBar="false"
              autoHeight
              border="none"
              customStyle="background: #f5f5f5;"
              disabledColor="#ffffff"
              fixed
              placeholderStyle="font-size: 12px;font-weight: 400;color: #cccccc;"
              shape="circle"
              @focus="handleFocus"
            />
            <!-- #endif -->
            <!-- #ifndef MP-WEIXIN -->
            <u--input
              v-model="info"
              :cursorSpacing="50"
              :disabled="disabled"
              :focus="autoFocus"
              :maxlength="500"
              :placeholder="placeholder"
              :showConfirmBar="false"
              autoHeight
              customStyle="width: 500rpx;height: 64rpx;"
              border="none"
              disabledColor="#ffffff"
              fixed
              placeholderStyle="font-size: 13px;font-weight: 400;color: #cccccc;"
              shape="circle"
              @focus="handleFocus"
            />
            <!-- #endif -->
          </div>
          <div
            :class="[
              {
                'input-button--disabled': disabled,
              },
            ]"
            class="input-button ml-[12px] shrink-0"
            @click="handleClick"
          >
            发送
          </div>
        </div>
        <div v-else>
          <div
            class="input-container"
            @click="handleClickInput"
          >
            <!-- #ifndef MP-TOUTIAO -->
            <u--input
              v-model="info"
              :cursorSpacing="50"
              :disabled="disabled"
              :focus="autoFocus"
              :maxlength="500"
              :placeholder="placeholder"
              :showConfirmBar="false"
              autoHeight
              border="none"
              disabledColor="#ffffff"
              fixed
              placeholderStyle="font-size: 13px;font-weight: 400;color: #cccccc;"
              shape="circle"
              @focus="handleFocus"
            />
            <!-- #endif -->
            <!-- #ifdef MP-TOUTIAO -->
            <u--input
              v-model="info"
              :cursorSpacing="50"
              :disabled="disabled"
              :focus="autoFocus"
              :maxlength="500"
              :placeholder="placeholder"
              :showConfirmBar="false"
              autoHeight
              customStyle="width: 500rpx;"
              border="none"
              disabledColor="#ffffff"
              fixed
              placeholderStyle="font-size: 13px;font-weight: 400;color: #cccccc;"
              shape="circle"
              @focus="handleFocus"
            />
            <!-- #endif -->
          </div>
          <div
            v-if="voiceStatus === VOICE_STATUS.WAIT"
            class="relative"
          >
            <div
              class="mt-[12px] mx-auto w-[116px] h-[32px] bg-[#22BF7E] rounded-[68px] flex items-center justify-center"
            >
              <div
                class="text-[14px] text-[#FFFFFF]"
                @click="startRecordThrottle"
              >
                开始语音识别
              </div>
            </div>
            <div
              class="absolute right-0 top-0 w-[80px] box-border h-[32px] rounded-[68px] border-[1px] border-solid border-[#DCDCDC] text-[12px] text-[#666666] float-right flex items-center justify-center"
              @click="isVoiceInput = false"
            >
              取消语音
            </div>
          </div>
          <div v-if="voiceStatus === VOICE_STATUS.START">
            <p
              class="mt-[12px] text-center text-[15px] text-[#EB4738] flex items-center justify-center"
            >
              语音识别中，请说话
              <submit-input-new-text-load />
            </p>
            <div
              class="mt-[12px] mx-auto w-[116px] h-[32px] bg-[#EB4738] rounded-[68px] flex items-center justify-center"
            >
              <div
                class="text-[14px] text-[#FFFFFF]"
                @click="stopRecord"
              >
                结束语音识别
              </div>
            </div>
          </div>
        </div>

        <u-safe-bottom />
      </div>
    </div>
    <app-placeholder :height="165" />
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import AppPlaceholder from "@/components/app-components/app-placeholder/index.vue";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import appVoice from "@/pages/submit-question/components/AppVoiceInput/appVoice";
import { VOICE_STATUS } from "@/pages/submit-question/components/AppVoiceInput/util";
import SubmitInputNewTextLoad from "@/pages/submit-question/components/SubmitInputNewTextLoad.vue";
import { throttle } from "@/libs/tools.js";
import GuessYouWantAsk from "@/components/GuessYouWantAsk.vue";
import PrivacyProtection from "@/components/PrivacyProtection.vue";
import { getCommonConfigKey } from "@/api/order";

export default {
  name: "SubmitInputNew",
  components: {
    PrivacyProtection,
    GuessYouWantAsk,
    SubmitInputNewTextLoad,
    AppPlaceholder,
    USafeBottom,
  },
  props: {
    privacyText: {
      type: String,
      default: "",
    },
    /** 禁用状态 */
    disabled: {
      type: Boolean,
      default: false,
    },
    value: {
      type: String,
      default: "",
    },
    placeholderText: {
      type: String,
      default: "说说你的问题，不少于10个字",
    },
    isBGarbageWords: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      /** 是否是语音输入 */
      isVoiceInput: false,
      /**
       * 语音识别状态
       */
      voiceStatus: VOICE_STATUS.WAIT,
      /** 语音识别，节流 */
      startRecordThrottle: throttle(this.startRecord, 2000),
      autoFocus: false,
      /** 自动拉起输入框计时 */
      timer: null,
      /** 猜你想问开关 */
      guessYouWantSwitch: false,
    };
  },
  computed: {
    VOICE_STATUS() {
      return VOICE_STATUS;
    },
    /** 提示文案 */
    placeholder() {
      return this.disabled ? "完成支付后可继续咨询" : this.placeholderText;
    },
    info: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  watch: {
    info: {
      handler(val) {
        if (val) {
          // 如果输入过内容，则不再自动拉起输入框
          clearTimeout(this.timer);
        }
      },
      immediate: true,
    },
  },
  mounted() {
    // ! 2024年10月29日 临时需求，下掉自动拉起输入框
    // 防止页面已经有默认值时 又自动拉起输入框
    // if (!this.info) {
    //   this.timer = setTimeout(() => {
    //     this.autoFocus = true;
    //   }, 5000);
    // }
    this.getGuessYouWantSwitch();

    // 监听 eventBus 改变 autoFocus 值
    this.$eventBus.$on("changeAutoFocus", (value) => {
      this.autoFocus = value;
    });
  },
  beforeDestroy() {
    // 销毁时，停止语音识别
    appVoice.stopRecord();
    // 移除 eventBus 监听器
    this.$eventBus.$off("changeAutoFocus");
  },
  methods: {
    getGuessYouWantSwitch() {
      getCommonConfigKey({
        paramName: "guess_you_want_switch",
      }).then(({ data }) => {
        this.guessYouWantSwitch = data.paramValue === "1";
      });
    },
    historySelect(data) {
      // this.autoFocus = true;
      this.$emit("history-select", data);
    },
    startRecord() {
      appVoice.onOpen(() => {
        this.voiceStatus = VOICE_STATUS.START;
      });

      appVoice.onError(() => {
        this.voiceStatus = VOICE_STATUS.WAIT;
        this.isVoiceInput = false;
        this.$toast("语音识别失败，请检查是否授权");
      });

      appVoice.onResult(({ result, status }) => {
        if (Number(status) === 1) {
          this.info = result;
        } else {
          this.info += result;
        }

        console.log(result, "识别的结果");
      });

      appVoice.onClose(() => {
        this.voiceStatus = VOICE_STATUS.WAIT;
      });

      // 事件监听注册完毕后，调用此方法开始识别
      appVoice.startRecord();
    },
    stopRecord() {
      this.isVoiceInput = false;
      appVoice.stopRecord();
    },
    /** 点击按钮 */
    async handleClick() {
      this.$emit("submit");
      // #ifdef MP-TOUTIAO
      this.$refs.input.blur();
      // #endif
    },
    /** 点击输入框 */
    handleClickInput() {
      buryPointChannelBasics({
        code: "LAW_APPLET_ASK_LAWYER_INPUT_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK,
      });

      this.$emit("click");
    },
    /** 聚焦输入框 */
    handleFocus() {
      this.$store.commit("askALawyer/CLEAR_TIMER");

      this.$emit("focus");
    },
  },
};
</script>

<style lang="scss" scoped>
.input-box {
  box-shadow: inset 0 0 0 0 #e5e5e5;
  position: fixed;
  z-index: 30;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
}

.input {
  border-radius: 0;
  opacity: 1;
  padding: 8px 16px;

  &-container {
    width: 100%;
    // #ifdef MP-TOUTIAO
    height: 44px;
    // #endif
    box-sizing: border-box;
    background: #f5f5f5;
    border-radius: 8px;
    overflow: hidden;
    opacity: 1;
  }

  &-button {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 60px;
    height: 32px;
    background: #3887f5;
    border-radius: 68px;
    opacity: 1;
    font-size: 14px;
    font-weight: 400;
    color: #ffffff;
    line-height: 16px;

    &--disabled {
      background: #9ab7ff;
    }
  }
}
</style>
