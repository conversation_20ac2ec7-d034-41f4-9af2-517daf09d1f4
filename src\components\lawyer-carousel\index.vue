<template>
  <div>
    <div
      ref="lawyers"
      class="lawyers"
      :class="{'lawyers-pc':!isMobile()}"
    >
      < @click="leftArrow" class="left-arrow arrow cursor-pointer" src="@/assets/imgs/loginPageFiftyEight/<EMAIL>"/>
      <img
        class="right-arrow arrow cursor-pointer"
        src="@/assets/imgs/loginPageFiftyEight/<EMAIL>"
        @click="rightArrow "
      >
      <div
        ref="lawyers-content"
        :style="getContentStyle"
        class="flex lawyers-content pd-17"
      >
        <div
          v-for="i in commonLawyer"
          :key="i.id"
          class="lawyers-item"
        >
          <div class="tag" />
          <img
            :src="i.imgUrl"
            alt=""
          >
          <p class="name">
            {{ i.realName }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCommonLawyer } from "@/api";
import { isMobile } from "@/libs/tool.js";

export default {
  name: "LawyerCarousel",
  data() {
    return {
      translateX: 0,
      commonLawyer: []
    };
  },
  mounted() {
    getCommonLawyer().then(({ data = [] }) => {
      console.log(data);
      this.commonLawyer = data;
    });
  },
  methods: {
    isMobile,
    leftArrow() {
      this.arrow(1);
    },
    rightArrow() {
      this.arrow(-1);
    },
    arrow(multiple) {
      /* 包裹容器*/
      const containerStyle = this.$refs.lawyers.getBoundingClientRect();
      /* 滚动容器*/
      const contentStyle = this.$refs["lawyers-content"].getBoundingClientRect();
      /* 每次滚动距离*/
      const movingDistance = containerStyle.width / 2;
      /* 最大滚动距离 保证每次都只显示半屏*/
      const maxSlide = contentStyle.width - movingDistance;
      /* 点击计算出当前需要滑动距离*/
      let translateX = this.translateX + movingDistance * multiple;
      /* 到达第一屏*/
      if (translateX > 0) return  false;
      /* 到达最后一屏 当前需要滑动距离 大于最大滚动距离*/
      if (Math.abs(translateX) >= maxSlide) return  false;
      this.translateX = translateX;
    },
  },
  computed: {

    getContentStyle() {
      return {
        transform: `translateX(${this.translateX}px)`
      };
    },
  },
};
</script>

<style scoped lang="scss">
.lawyers {
  overflow-x: auto;
  font-size: 0;
  .arrow{
    display: none;
  }
  &-pc{
    position: relative;
    overflow: hidden;
    height: 102px;
    &:hover{
      .arrow{
        display: block;
      }
    }
    .arrow{
      display: none;
      position: absolute;
      width: 27px;
      height: 27px;
      z-index: 3;
      top: 35px;
      &.left-arrow{
        left: 11px;
      }
      &.right-arrow{
        right: 11px;
      }
    }
    .lawyers-content{
      top: 0;
      left: 0;
      transform: translateX(0px);
      transition: transform 0.5s;
      position: absolute;
    }
    .lawyers-item{
      &:last-child{
        padding-right: 0;
      }
    }
  }

  .lawyers-content {
    padding-left: 17px;
  }

  .lawyers-item {
    padding-right: 32px;
    position: relative;
    .tag{
      position: absolute;
      width: 66px;
      height: 18px;
      z-index: 3;
      background: url("~@/assets/imgs/loginPageFiftyEight/<EMAIL>") no-repeat;
      background-size: 100% 100%;
      top: 56px;
    }
    img {
      width: 68px;
      height: 68px;
      display: block;
      border-radius: 50%;
      overflow: hidden;
    }

    .name {
      font-size: 13px;
      font-weight: 400;
      color: #333333;
      padding-top: 16px;
      text-align: center;
    }
  }
}
</style>
