import dayjs from "dayjs";

export const userToken = "lawMSeoToken";
export const userInfo = "lawMSeoUserInfo";
export const channelIdKey = "lawMSeoChannelId";
export const dailyLife = "lawMSeoDailyLife";
export const newUser = "lawMNewUser";
export const historyRecordKy = "lawMSeoMSeoHistoryRecordKy";
export const uuidKey = "lawMSeoUuid";
/** 支付极端情况下，需要返回到之前的界面 */
export const returnRouter = "returnRouter";
const appTokenInfoHeader = "appTokenInfoHeader";
// 案件委托留资信息
export const caseEntrustmentKey = "caseEntrustmentKey";

const $localStorage = {
  setItem: uni.setStorageSync,
  getItem: uni.getStorageSync,
  removeItem: uni.removeStorageSync,
};

export const getNewUserStateStorage = () => {
  const item = uni.getStorageSync(newUser) || "";
  console.log(item, "getNewUserStateStorage");
  return item ? item === "true" : "";
};

export const clearNewUserStateStorage = () => {
  return uni.removeStorageSync(newUser) || "";
};

export const setUserTokenStorage = (value) => {
  return uni.setStorageSync(userToken, value) || "";
};

/**
 * 支付极端情况下，需要返回到之前的界面路由
 * @param {string} router
 */
export const setReturnRouterStorage = (router) => {
  uni.setStorageSync(returnRouter, router);
};

export const getReturnRouterStorage = () => {
  return uni.getStorageSync(returnRouter) || "";
};

export const getUserTokenStorage = () => {
  return uni.getStorageSync(userToken) || "";
};

export const clearUserTokenStorage = () => {
  return uni.removeStorageSync(userToken) || "";
};

export const setUuidStorage = (value) => {
  return uni.setStorageSync(uuidKey, value) || "";
};

export const getUuidStorage = () => {
  return uni.getStorageSync(uuidKey) || "";
};

export const clearUuidStorage = () => {
  return uni.removeStorageSync(uuidKey) || "";
};

export const setChannelIdStorage = (value) => {
  return uni.setStorageSync(channelIdKey, value) || "";
};

export const getChannelIdStorage = () => {
  return uni.getStorageSync(channelIdKey) || "";
};

export const setUserInfoStroage = (data) => {
  return $localStorage.setItem(userInfo, data);
};

export const getUserInfoStorage = () => {
  return $localStorage.getItem(userInfo);
};

export const clearUserInfoLocalStorage = () => {
  return $localStorage.removeItem(userInfo);
};

export const setDailyLifeStroage = (data) => {
  return $localStorage.setItem(dailyLife, data);
};

export const getDailyLifeStorage = () => {
  return $localStorage.getItem(dailyLife);
};

export const clearDailyLifeLocalStorage = () => {
  return $localStorage.removeItem(dailyLife);
};

export const setHistoryRecordeStroage = (data) => {
  $localStorage.setItem(historyRecordKy, JSON.stringify(data.splice(0, 5)));
};

export const getHistoryRecordeStorage = () => {
  const data = $localStorage.getItem(historyRecordKy);
  return data ? JSON.parse(data) : [];
};

export const clearHistoryRecordeStorage = () => {
  return $localStorage.removeItem(historyRecordKy);
};

export const setAppTokenInfo = (data) => {
  $localStorage.setItem(appTokenInfoHeader, JSON.stringify(data));
};

export const getAppTokenInfo = () => {
  try {
    const data = $localStorage.getItem(appTokenInfoHeader);
    return JSON.parse(data);
  } catch (e) {
    return {};
  }
};

export const removeAppTokenInfo = (data) => {
  $localStorage.removeItem(appTokenInfoHeader);
};

/**
 * 引导付费界面缓存数据
 * @param [lawyerId]
 * @param info
 * @param [serverCode]
 * @param typeLabel
 * @param typeValue
 */
export const setPayLawyerGuideStorage = ({
  lawyerId,
  info,
  serverCode,
  typeLabel,
  typeValue,
}) => {
  const data = {
    lawyerId,
    info,
    serverCode,
    typeLabel,
    typeValue,
  };

  $localStorage.setItem("payLawyerGuide", JSON.stringify(data));
};
/** 获取引导付费界面缓存数据 */
export const getPayLawyerGuideStorage = () => {
  let data;

  try {
    data = JSON.parse($localStorage.getItem("payLawyerGuide"));
  } catch (e) {
    data = {};
  }

  return data;
};

export const checkAppLoginAndGetCode = () => {
  return new Promise(async (resolve) => {
    // #ifdef MP-BAIDU
    swan.getLoginCode({
      success(loginRes) {
        uni.setStorageSync("loginCode", loginRes.code);
        resolve(loginRes.code);
      },
    });
    // #endif

    // #ifdef MP-WEIXIN || MP-TOUTIAO || MP-ALIPAY
    let [, { provider }] = await uni.getProvider({
      service: "oauth",
    });
    uni.login({
      provider: provider[0],
      success: (loginRes) => {
        uni.setStorageSync("loginCode", loginRes.code);
        resolve(loginRes.code);
      },
    });
    // #endif
  });
};

/** 案件详情，需要返回到之前的界面 */
export const getCaseDetailsData = () => {
  if (process.client) {
    return $localStorage.getItem(caseDetailsData);
  }

  return undefined;
};

/* 存储当前时间 用来判断是不是当日*/
export const setTodayTime = () => {
  const date = dayjs().format("YYYY-MM-DD");
  $localStorage.setItem("todayTime", date);
};

/* 获取当前时间 用来判断是不是当日*/
export const getTodayTime = () => {
  return $localStorage.getItem("todayTime");
};

/* 清除引导策略所有缓存*/
export const clearBootPolicyUserActionStore = () => {
  uni.removeStorageSync("userActionStore");
  uni.removeStorageSync("bootPolicyScene");
};

// 案源委托留资信息
export const setCaseEntrustmentLocal = (data) => {
  $localStorage.setItem(caseEntrustmentKey, JSON.stringify(data));
};

export const clearCaseEntrustmentLocal = () => {
  $localStorage.removeItem(caseEntrustmentKey);
};
 
// 获取
export const getCaseEntrustmentLocal = () => {
  const data = $localStorage.getItem(caseEntrustmentKey);
  if(!data) return  {};
  try {
    return JSON.parse(data);
  }catch (e) {
    return  {};
  }
};

/* 缓存签订弹窗状态*/
export const setSignContractDialog = (data) => {
  const list = getSignContractDialog();
  list.push(data);
  $localStorage.setItem("signContractDialog", JSON.stringify(list));
};

export const getSignContractDialog = () => {
  const data = $localStorage.getItem("signContractDialog");
  if(!data) return  [];
  try {
    return JSON.parse(data);
  }catch (e) {
    return  [];
  }
};
export const clearSignContractDialog = () => {
  $localStorage.removeItem("signContractDialog");
};