<template>
  <div
    :class="[theme]"
    class="history-select flex flex-align-center"
    @click="$emit('click')"
  >
    <view>{{ historyItem }}</view>
  </div>
</template>

<script>
export default {
  name: "ImHistoryItem",
  props: {
    item: {
      type: String,
      required: true,
    },
    theme: String,
  },
  computed: {
    historyItem() {
      // 如果超过6个字符，则截取前6个字符，后面加省略号
      if (this.item.length > 6) {
        return this.item.substring(0, 6) + "...";
      }
      return this.item;
    },
  },
};
</script>

<style lang="scss" scoped>
.history-select {
  height: 24px;
  box-sizing: border-box;
  border-radius: 68px 68px 68px 68px;
  // #ifdef MP-HJLS
  border-radius: 4px !important;
  // #endif
  padding: 0 12px;
  font-weight: 400;
  color: #666666;
  font-size: 12px;
  flex-shrink: 0;

  &.gray {
    background: #eeeeee;
  }

  &.white {
    border-radius: 46px;
    background: #fff;
    border: 1px solid #eeeeee;
    padding: 0 8px;
  }

  &:active {
    background: #ffffff;
    color: $theme-color;
  }
  &.border-gray{
    border: 1px solid #EEEEEE;
    border-radius: 4px;
  }
}
</style>
