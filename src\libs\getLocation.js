import amapFile from "@/libs/amap-wx.js";
import { getAreaLikeCityName } from "@/api/common";
import { getCommonConfigKey } from "@/api";
/* 地图的key */
const amapKey = "dc3c27a64b6b0f70d42d25956b3a4f2a";

/**
 * @description:小程序获取地理位置
 * @return:Promise<any{city:string}>
 */
/**
 * @description:基于腾讯地图 获取定位
 * @author:djsong
 * @date:2021/6/16
 * @param:key 腾讯地图上申请的key
 * @return:Promise<any{city:string}>
 */
export const getLocationBase = (key = amapKey) => {
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    wx.authorize({
      scope: "scope.userFuzzyLocation",
      success(res) {
        console.log(res);
        if (res.errMsg === "authorize:ok") {
          wx.getFuzzyLocation({
            type: "wgs84",
            success(res) {
              let amapWX = new amapFile.AMapWX({
                key: key,
              });
              amapWX.getRegeo({
                location: `${res.longitude},${res.latitude}`,
                success: (data) => {
                  console.log("定位city:", data[0].regeocodeData.addressComponent.city);
                  let city = data?.[0]?.regeocodeData?.addressComponent?.city;
                  if (city) {
                    return resolve(city);
                  }

                  return reject("定位失败");
                },
                fail: function (error) {
                  console.log(error, "sdk定位失败");
                  reject(error);
                },
              });
            },
            fail(err) {
              console.log(err);
              reject(err);
            },
          });
        }
        else {
          reject(res);
        }
      },
      fail(err) {
        reject(err);
      },
    });
    // #endif

    // #ifdef MP-BAIDU
    swan.authorize({
      scope: "scope.userLocation",
      success: () => {
        swan.getLocation({
          type: "gcj02",
          altitude: true,
          success: (res) => {
            // console.log('定位成功：', res)
            resolve(res.city);
          },
          fail: (err) => {
            // console.log('定位失败：', err)
            reject(err);
          },
        });
      },
      fail: (err) => {
        reject(err);
      },
    });
    // #endif

    // #ifdef MP-ALIPAY
    my.getLocation({
      type: 3,
      success(res) {
        resolve(res.city);
      },
      fail(err) {
        if (err.error === 11) {
          // 错误码为11的时候，表示用户支付宝APP级别的定位权限没有开启，使用下面方法引导用户开启
          my.showAuthGuide({
            authType: "LBS"
          });
        }
        reject(err);
      },
    });
    // #endif

    // #ifdef MP-TOUTIAO
    reject();
    // #endif
  });
};


/**
 * 小程序获取地理位置
 * 返回类型： cityCode: 510100
 * 返回类型： cityName: "成都"
 * 返回类型： provinceCode: 510000
 * 返回类型： provinceName: "四川省"
 */
export function getLocation() {
  return new Promise(async (resolve, reject) => {
    let city;

    try {
      city = await getLocationBase();

      getAreaLikeCityName({
        cityName: city,
      })
        .then(({ data = {} }) => {
          resolve(data);
        })
        .catch((err) => {
          reject(err);
        });
    } catch (error) {
      reject(error);
    }
  });
}

/** 由后台参数控制请求地址是否调用 */
export function getLocationByConfig() {
  return new Promise(async (resolve, reject) => {
    getCommonConfigKey({ paramName: "C_MINI_LOCATION" }).then(res => {
      console.log(res.data?.paramValue, "C_MINI_LOCATION");
      // 如果配置开启
      if (Number(res.data?.paramValue) === 1) {
        getLocation().then(res => {
          resolve(res);
        }).catch(err => {
          reject(err);
        });
      } else {
        // 如果配置关闭
        reject("不开启定位");
      }
    });
  });
}
