<template>
  <div>
    <app-popup
      :closeOnClickOverlay="false"
      :show="show"
      :zIndex="999999999"
      showCancelButton
      @cancel="handleCancelPay"
    >
      <div class="p-[32px_16px_16px_16px]">
        <div class="flex">
          <img
            alt=""
            class="w-[18px] h-[59px] inline-block shrink-0"
            src="submit-discount-popup-new/img/<EMAIL>"
          >
          <div class="ml-[12px]">
            <div class="text-[16px] font-bold text-[#333333]">
              支付成功，提交订单
            </div>
            <div class="text-[12px] mt-[4px] text-[#666666]">
              {{ tips }}
            </div>
          </div>
        </div>
        <div class="flex">
          <img
            alt=""
            class="w-[18px] h-[101px] inline-block shrink-0"
            src="submit-discount-popup-new/img/<EMAIL>"
          >
          <div class="ml-[12px] w-full">
            <div class="text-[14px] font-bold text-[#333333]">
              律师等待接入服务，提供定制解决方案
            </div>
            <div class="w-full grid grid-cols-3 gap-x-[9px] mt-[14px]">
              <div
                v-for="(item, index) in lawyerList"
                :key="index"
              >
                <div class="relative w-[32px] h-[32px] mx-auto">
                  <img
                    :src="item.imgUrl"
                    alt=""
                    class="w-[32px] h-[32px] rounded-[16px]"
                  >
                  <i
                    class="absolute right-0 bottom-0 rounded-full w-[8px] h-[8px] bg-[#22BF7E] border-[1px] border-solid border-[#FFFFFF]"
                  />
                </div>
                <div
                  class="text-[13px] font-bold text-[#333333] mt-[4px] text-center"
                >
                  {{ item.realName }}
                </div>
                <div
                  class="text-[10px] text-[#999999] text-ellipsis mt-[4px] text-center"
                >
                  {{ item.lawyerOffice }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="mt-[36px]">
          <div class="flex items-center justify-between">
            <div class="text-[13px] text-[#666666]">
              选择服务：
            </div>
            <div class="text-[13px] text-[#333333]">
              {{ serviceData.serviceName }}
            </div>
          </div>
          <div class="flex items-center justify-between mt-[10px]">
            <div class="text-[13px] text-[#666666]">
              应付金额：
            </div>
            <div class="text-[15px] text-[#F34747] font-bold">
              ¥{{ serviceData.servicePrice | amountFilter }}
            </div>
          </div>
        </div>
        <div
          class="w-[343px] h-[44px] text-[16px] bg-[#3887F5_noc] mt-[17px] rounded-[40px] text-[#FFFFFF] flex items-center justify-center"
          @click="continuePay"
        >
          <span class="font-bold">继续支付</span><span class="text-[12px]">（未服务100%退款）</span>
        </div>
        <div
          class="w-[343px] h-[44px] bg-[#FFFFFF] rounded-[40px] flex items-center justify-center text-[13px] text-[#F78C3E] mt-[12px] border-[1px] border-solid border-[#F78C3E]"
          @click="toAskLawyer"
        >
          先去免费问律师
        </div>
      </div>

      <u-safe-bottom v-if="safeAreaInsetBottom" />
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { getStorageServiceCode } from "@/libs/pay";
import { lawyerListV3 } from "@/api/findlawyer";
import Store from "@/store";
import { isObjNull } from "@/libs/basics-tools";

import { toAskLawyer } from "@/libs/turnPages";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import { turnToBePay } from "@/pages/submit-question/to-be-paid";

export default {
  name: "RepidReturnPopup",
  components: { USafeBottom, AppPopup },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    safeAreaInsetBottom: {
      type: Boolean,
      default: true
    },
    tips: {
      type: String,
      required: true,
      default: "平台将立即匹配律师，10s内接入服务，提供定制解决方案"
    },
    lawyerInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      serviceData: {},
      /** 律师信息 */
      lawyerList: []
    };
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          buryPointChannelBasics({
            code: "LAW_APPLET_PAY_RETENTION_POP_UP_PAGE",
            type: 1,
            behavior: BURY_POINT_CHANNEL_TYPE.CK
          });

          if (isObjNull(this.lawyerInfo)) {
            this.getTabData();
          } else {
            this.lawyerList = [this.lawyerInfo];
          }

          getStorageServiceCode().then(res => {
            this.serviceData = res;
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    toAskLawyer() {
      toAskLawyer();
    },
    /** 获取数据 */
    async getTabData() {
      lawyerListV3({
        /** 页码 */
        currentPage: 1,
        /** 每页条数 */
        pageSize: 3,
        sort: 1
      }).then(res => {
        this.lawyerList = res.data.records || [];
      });
    },
    /** 点击继续支付 */
    continuePay() {
      Store.commit("payState/PAY_FAIL_POPUP_PAY");
    },
    /** 点击取消支付 */
    handleCancelPay() {
      this.show = false;
      this.$emit("close");

      turnToBePay({
        lawyerId: this.lawyerInfo.id
      });
    },
    payFailCallback() {
      this.show = true;
      this.$emit("pay");
    }
  }
};
</script>

<style lang="scss" scoped></style>
