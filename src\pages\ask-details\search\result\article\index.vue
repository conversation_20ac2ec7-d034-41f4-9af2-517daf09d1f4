<template>
  <div>
    <search-input
      v-model="searchInput"
      disabled
    />
    <div
      v-for="item in articleV2LawyerPageSearchData.records"
      :key="item.id"
      class="bg-[#FFFFFF] rounded-[8px] overflow-hidden mt-[12px] mx-[12px]"
    >
      <essay-item :data="item" />
    </div>
    <u-safe-bottom />
  </div>
</template>

<script>
import searchData from "@/pages/ask-details/search/searchData";
import { articleV2LawyerPageSearch } from "@/api/common";
import SearchInput from "@/components/SearchInput.vue";
import EssayItem from "@/components/essay-item/index.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "ResultArticle",
  components: { USafeBottom, EssayItem, SearchInput },
  data() {
    return {
      /** 文章数据 */
      articleV2LawyerPageSearchData: {},
      searchInput: searchData.searchText || ""
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    /** 请求数据 */
    getData() {
      const keyword = searchData.searchText;

      articleV2LawyerPageSearch({
        keyword,
        currentPage: 1,
        pageSize: 99
      }).then(res => {
        this.articleV2LawyerPageSearchData = res.data;
      });
    }
  }
};
</script>
