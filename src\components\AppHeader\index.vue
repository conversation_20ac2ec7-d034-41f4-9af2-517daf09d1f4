<template>
  <div>
    <app-placeholder
      :height="statusBarHeight"
      :showSafe="false"
    />
    <div
      :style="[
        {
          height: `${navigationBarHeight}px`
        }
      ]"
      class="flex items-center"
    >
      <img
        alt=""
        class="w-[228px] h-[28px] block ml-[12px]"
        src="@/pages/index/imgs/Frame1321315769.png"
      >
    </div>
  </div>
</template>

<script>
import AppPlaceholder from "@/components/app-components/app-placeholder/index.vue";
import AppHeaderMixin from "@/components/AppHeader/AppHeaderMixin";

export default {
  name: "AppHeader",
  components: { AppPlaceholder },
  mixins: [AppHeaderMixin]
};
</script>
