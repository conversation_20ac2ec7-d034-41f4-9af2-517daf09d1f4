export const isNull = (o) => {
  return o === null || o === "" || o === undefined;
};
export const isObj = (o) => {
  // 是否对象
  return Object.prototype.toString.call(o).slice(8, -1) === "Object";
};
export const isObjNull = (o) => {
  if (isNull(o)) return false;
  return (Object.keys(o) && Object.keys(o).length) === 0;
};
export const isArray = (o) => {
  // 是否数组
  return Object.prototype.toString.call(o) === "[object Array]";
};
export const isFunction = (o) => {
  // 是否函数
  return Object.prototype.toString.call(o).slice(8, -1) === "Function";
};
export const isString = (o) => {
  // 是否字符串
  return Object.prototype.toString.call(o).slice(8, -1) === "String";
};
export const isFile = (o) => {
  // 是否文件
  return Object.prototype.toString.call(o).slice(8, -1) === "File";
};
export const isNumber = (o) => {
  return Object.prototype.toString.call(o).slice(8, -1) === "Number";
};
export const isArrNull = (o) => {
  return o.length === 0;
};

// 乘法 解决精度问题
/**
 * @param {number} arg1 - 第一个乘数
 * @param {number} arg2 - 第二个乘数
 * @returns {number} 返回乘法计算结果
 */
export const accMul = (arg1, arg2) => {
  // 处理无效输入
  if (arg1 == null || arg2 == null) {
    return 0;
  }
  // 处理0的情况
  if (arg1 === 0 || arg2 === 0) {
    return 0;
  }

  // 将数字转换为字符串，避免科学计数法
  const str1 = arg1.toString();
  const str2 = arg2.toString();

  // 获取小数位数
  const decimal1 = str1.includes(".") ? str1.split(".")[1].length : 0;
  const decimal2 = str2.includes(".") ? str2.split(".")[1].length : 0;

  // 转换为整数进行计算
  const num1 = Number(str1.replace(".", ""));
  const num2 = Number(str2.replace(".", ""));

  // 计算最终结果
  const result = (num1 * num2) / Math.pow(10, decimal1 + decimal2);

  // 处理特殊情况
  if (isNaN(result)) {
    return 0;
  }

  return result;
};

// 除法 解决精度问题
/**
 * @param {number} arg1 - 被除数
 * @param {number} arg2 - 除数
 * @returns {number} 返回除法计算结果，始终为数字类型
 */
export const accDiv = (arg1, arg2) => {
  // 处理除数为0的情况
  if (arg2 === 0) {
    return 0;
  }

  // 将数字转换为字符串，避免科学计数法
  const str1 = arg1.toString();
  const str2 = arg2.toString();

  // 获取小数位数
  const decimal1 = str1.includes(".") ? str1.split(".")[1].length : 0;
  const decimal2 = str2.includes(".") ? str2.split(".")[1].length : 0;

  // 转换为整数进行计算
  const num1 = Number(str1.replace(".", ""));
  const num2 = Number(str2.replace(".", ""));

  // 计算最终结果
  const result = (num1 / num2) * Math.pow(10, decimal2 - decimal1);

  // 处理特殊情况
  if (isNaN(result)) {
    return 0;
  }

  return result; // 确保返回数字类型
};

const basicsTools = {
  isArray,
  isArrNull,
  isFile,
  isFunction,
  isNull,
  isNumber,
  isObj,
  isObjNull,
  isString,
};
basicsTools.install = function (Vue) {
  Vue.prototype.$basicsTools = basicsTools;
};
export default basicsTools;
