.app-button {
  border-radius: 22px;
  font-size: 16px;
  font-weight: 400;
  border: none;

  &--large {
    width: 100%;
    height: 44px;
  }

  &--middle {
    width: 100%;
    font-size: 14px;
    height: 32px;
    padding: 0 6px;
  }

  &--small {
    font-size: 13px;
    height: 30px;
    padding: 0 12px;
  }

  &--mini {
    height: 25px;
  }

  &:after {
    content: none;
  }

  &-default {
    background: $theme-color;
    color: #ffffff;

    &--disabled {
      background: #A0CFFB;
    }
  }

  &-plain {
    border: 1px solid $theme-color;
    background: #ffffff;
    color: $theme-color;

    &--default {
      border: 1px solid #cccccc;
      color: #333333;
    }
  }

  &--fixed {
    width: 343px;
    position: fixed;
    bottom: 10px;
    z-index: 99;
  }
}

button {
  padding: 0;
  margin: 0;
}

button::after {
  border: 0;
}
