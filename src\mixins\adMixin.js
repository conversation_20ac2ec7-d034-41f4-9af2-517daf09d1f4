import { bindOnHook } from "@/libs/hooks";

export default {
  created() {
    let rewardedVideoAd = null;
    const fn = () => {
      if (wx.createRewardedVideoAd) {
        rewardedVideoAd = wx.createRewardedVideoAd({
          adUnitId: "adunit-c7f669c130efe5f7"
        });

        rewardedVideoAd.onError(err => {
          console.log("激励视频 广告加载失败", err);
        });

        this.$store.commit("adState/SET_REWARDED_VIDEO_AD", rewardedVideoAd);
        bindOnHook.call(this, "onShow", () => {
          this.$store.commit("adState/SET_REWARDED_VIDEO_AD", rewardedVideoAd);
        });
      }
    };
    console.log("created");
    fn();

  }
};
