<template>
  <div>
    <detail-header />
    <!-- 问题部分 -->
    <div class="question">
      <div class="question__detail">
        <div class="question__detail__icon position-relative">
          <img
            alt=""
            class="question__detail__icon__img background-image"
            src="@/pages/ask-details/img/<EMAIL>"
          >
        </div>
        {{ details.detail }}
      </div>
      <div class="mg-tp-24 flex flex-align-center flex-space-between">
        <div class="question__time">
          发布时间: {{ getTime }}
        </div>
        <span class="question__label">#{{ details.typeLabel }}</span>
      </div>
      <img
        alt=""
        class="question__image"
        src="@/pages/ask-details/img/Group718.png"
        @click="toAskLawyer"
      >
    </div>
    <!-- 回答部分 -->
    <div class="answer position-relative">
      <div class="answer__title flex flex-align-center flex-space-between">
        <div class="flex flex-align-center">
          <img
            alt=""
            class="answer__title__icon"
            src="@/pages/ask-details/img/<EMAIL>"
          >
          <div>律师解答</div>
        </div>
        <div class="answer__title__right">
          解决法律问题，就上法临
        </div>
      </div>
      <div
        v-for="item in answerContent"
        :key="item.id"
        class="answer__item"
      >
        <div class="lawyer__info flex flex-space-between flex-align-center">
          <div
            class="flex flex-align-center"
            @click="toLawyerHome(item.lawyerId)"
          >
            <div class="position-relative mg-r-12">
              <img
                :src="item.lawyerHeadImg"
                alt=""
                class="lawyer__info__header"
              >
              <img
                v-if="item.vip"
                alt=""
                class="lawyer__info__header__vip"
                src="@/pages/index/imgs/vip.png"
              >
            </div>
            <div>
              <div class="lawyer__info__name flex flex-align-center">
                <div>
                  {{ item.lawyerName }}
                </div>
                <img
                  alt=""
                  class="lawyer__info__name__icon"
                  src="@/pages/ask-details/img/<EMAIL>"
                >
              </div>
              <div class="lawyer__info__help">
                已帮助<span class="lawyer__info__help__text mg-l-4 mg-r-4">{{
                  item.serviceNum
                }}</span>人<span class="mg-l-8 mg-r-4">评价</span><span class="lawyer__info__help__text">{{
                  item.score.toFixed(1)
                }}</span>
              </div>
            </div>
          </div>
          <div
            class="lawyer__info__button"
            @click="toAsk(item)"
          >
            咨询我
          </div>
        </div>
        <img
          v-if="item.hasAccepted === 1"
          alt=""
          class="answer__icon"
          src="@/pages/ask-details/img/answer-icon.png"
        >
        <div class="answer__content">
          {{ item.content }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import myQuestionMixin from "@/pages/lawyer-home/my-question/mixin/myQuestionMixin.js";
import { toSearchResultPage } from "@/libs/turnPages";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath";
import {
  BURY_POINT_CHANNEL_TYPE,
  FROM_PAGE,
  globalTransformationPath,
  POINT_CODE
} from "@/enum/burypoint";
import { buryPointChannelBasics } from "@/api/burypoint";
import DetailHeader from "@/components/detail-header/index.vue";
import { toLawyerFakeImBefore } from "@/libs/tools";
import searchData from "@/pages/ask-details/search/searchData";
import { SEARCH_PAGE_TYPE } from "@/libs/config";

export default {
  name: "AskHeader",
  components: { DetailHeader },
  mixins: [myQuestionMixin],
  props: {
    details: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    toAskLawyer() {
      buryPointTransformationPath.addDataSources({
        fromPage: FROM_PAGE.WDXQ_HQZX
      });

      buryPointChannelBasics({
        code:
          "LAW_APPLET_RECENTLY_QA_LAWYER_ANSWER_DETAIL_PAGE_GET_ANSWER_CLICK",
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
        type: 1
      });

      searchData.searchText = this.details.detail;

      toSearchResultPage({
        type: SEARCH_PAGE_TYPE.SPECIAL
      });
    },
    /**
     * 跳转到自营律师下单页
     * https://lanhuapp.com/web/#/item/project/product?pid=83504fa8-598e-4bee-addf-4f9afe8d6208&image_id=b829d101-a861-454b-b864-aec4b6b59b80&tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=a1eeb4af-6052-496f-8521-2372959dcf9e&docId=b829d101-a861-454b-b864-aec4b6b59b80&docType=axure&pageId=c7ef7d8e66034189bc8d85a8d7437293&parentId=1238e7dc-5398-4940-9d4c-9c3f8346b27d
     */
    toAsk(data) {
      buryPointTransformationPath.addDataSources({
        fromPage: FROM_PAGE.WDXQY_TWZX
      });

      buryPointTransformationPath.add(
        globalTransformationPath.LAW_APPLET_QA_ITEM_CLICK
      );

      buryPointChannelBasics({
        code:
          "LAW_APPLET_RECENTLY_QA_LAWYER_ANSWER_DETAIL_PAGE_CONSULT_ME_CLICK",
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
        type: 1
      });

      buryPointChannelBasics({
        code: POINT_CODE.LAW_APPLET_QA_ITEM_CLICK,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      toLawyerFakeImBefore({
        lawyerInfo: {
          id: data.lawyerId
        },
        createSource: 5,
        createSourceBusinessId: this.details.id,
        itemClassType: 2
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.question {
  padding: 0 16px 24px 16px;
  border-radius: 0 0 16px 16px;
  background-color: #fff;

  &__detail {
    font-size: 18px;
    font-weight: bold;
    color: #333333;

    &__icon {
      display: inline-block;
      width: 20px;
      height: 20px;
      margin-right: 4px;
      margin-top: 2px;

      &__img {
        z-index: 0;
        top: 3px;
      }
    }
  }

  &__time {
    font-size: 12px;
    font-weight: 400;
    color: #8791a3;
  }

  &__label {
    font-size: 12px;
    font-weight: 400;
    color: #3887f5;
  }

  &__image {
    margin-top: 16px;
    display: block;
    width: 347px;
    height: 47px;
  }
}

.answer {
  &__title {
    width: 375px;
    height: 56px;
    box-sizing: border-box;
    padding: 16px;
    background: linear-gradient(
      180deg,
      #ffffff 0%,
      rgba(255, 255, 255, 0) 100%
    );
    border-radius: 16px 16px 0 0;
    opacity: 1;
    font-size: 18px;
    font-weight: bold;
    color: #333333;

    &__icon {
      margin-right: 7px;
      display: block;
      width: 24px;
      height: 24px;
    }

    &__right {
      font-size: 13px;
      font-weight: 400;
      color: #999999;
    }
  }
}

.answer {
  margin: 12px 0;

  &__item {
    padding: 17px 12px 20px 12px;
    width: 351px;
    box-sizing: border-box;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 8px;

    &:not(:last-child) {
      margin-bottom: 16px;
    }
  }

  &__icon {
    display: block;
    width: 74px;
    height: 23px;
    margin-top: 16px;
  }

  &__content {
    margin-top: 12px;
    font-size: 15px;
    font-weight: 400;
    color: #333333;
  }
}

.lawyer__info {
  &__header {
    display: block;
    width: 40px;
    height: 40px;
    border-radius: 5px;

    &__vip {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 12px;
      height: 12px;
    }
  }

  &__name {
    font-size: 16px;
    font-weight: bold;
    color: #333333;

    &__icon {
      width: 52px;
      height: 17px;
      display: block;
      margin-left: 8px;
    }
  }

  &__button {
    padding: 5px 12px;
    background: #ebf3fe;
    border-radius: 40px;
    opacity: 1;
    font-size: 12px;
    font-weight: 400;
    color: #3887f5;
  }

  &__help {
    display: flex;
    margin-top: 4px;
    align-items: center;
    font-size: 12px;
    font-weight: 400;
    color: #999999;

    &__text {
      font-size: 12px;
      font-weight: 400;
      color: #444444;
    }
  }
}
</style>
