import { requestCore, requestInfo } from "@/libs/axios";

/* 合同类型列表*/
export const listContractType = (data) => requestInfo.post("/contract/listContractType", data);

/* 合同内容列表*/
export const listContractByTypeIdList = (data) => requestInfo.post("/contract/listContractByTypeId", data);

/* 合同发送邮箱*/
export const sendEmail = (data) => requestCore.post("/contract/mail", data);

/* 合同内容详情*/
export const listContractByDetail = (data) => requestInfo.post("/contract/selectOne", data);

/* 合同类型详情*/
export const listContractByTypeDetail = (data) => requestInfo.post("/contract/selectOneContractType", data);
