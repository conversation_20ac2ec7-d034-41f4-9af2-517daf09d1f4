<template>
  <div
    :style="[$u.addStyle(labelStyle)]"
    class="w-full"
    @click="handleClick"
  >
    <div class="select flex flex-space-end">
      {{ label }}
    </div>
    <slot />
  </div>
</template>

<script>
export default {
  name: "AppFormItemLayout",
  mixins: [uni.$u.mixin],
  props: {
    label: {
      type: String,
      default: "",
    },
    labelStyle: {
      type: [Object, String],
      default: () => ({}),
    },
  },
  methods: {
    handleClick() {
      this.$emit("click");
    },
  },
};
</script>

<style lang="scss" scoped>
.select {
  width: 100%;
}
</style>
