<template>
  <div
    class="notice-container"
    :style="getStyle"
  >
    <div
      v-if="icon"
      class="icon flex"
    >
      <!-- <van-icon :name="icon" :color="iconColor" :size="iconSize" /> -->
    </div>
    <slot>
      {{ msg }}
    </slot>
  </div>
</template>

<script>
// import { Icon } from 'vant'
export default {
  name: "Index",
  components: {
    // 'van-icon': Icon
  },
  props: {
    msg: {
      type: String,
      default: ""
    },
    color: {
      type: String,
      default: "#177EFF"
    },
    backgroundColor: {
      type: String,
      default: "#F0F7FF"
    },
    height: {
      type: [Number, String],
      default: "100%"
    },
    icon: {
      type: String,
      default: ""
    },
    iconColor: {
      type: String,
      default: "#177EFF"
    },
    iconSize: {
      type: String,
      default: "28"
    }
  },
  computed: {
    getStyle(){
      return `color:${this.color};background-color:${this.backgroundColor};height:${this.height}`;
    }
  }
};
</script>

<style scoped lang="scss">
.notice-container{
  height: 100%;
  display: flex;
  align-items: center;
  font-size: 14px;
  padding-left: 16px;
}
.icon{
  padding-right: 5px;
  align-items: center;
}
</style>
