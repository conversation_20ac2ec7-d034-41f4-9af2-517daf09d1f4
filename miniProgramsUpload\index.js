const { getCmdOption } = require("./base.js");
const path = require("path");
const cmdOption = getCmdOption();
/*
* 命令参数：
* UNI_PLATFORM：上传的平台
* UPLOAD_ENV：上传的环境 dev||build
* FILE_NAME:上传的文件 没有就是直接指定到UNI_PLATFORM
* */


const {
  UNI_PLATFORM,
  UPLOAD_ENV,
  FILE_NAME
} = cmdOption;

const uploadPath = path.resolve(__dirname, `./${UNI_PLATFORM.replace("mp-", "")}/upload.js`);

const upload =  require(uploadPath);
const { getMiniVersion } = require("./base");

const isDev = UPLOAD_ENV === "dev";

const projectPath = path.resolve(__dirname, `../dist/${UPLOAD_ENV}/${FILE_NAME || UNI_PLATFORM}`);
console.log("上传小程序文件============", projectPath);
upload({
  ...cmdOption,
  PROJECT_PATH: projectPath,
  isDev,
  version: getMiniVersion(FILE_NAME)
});
