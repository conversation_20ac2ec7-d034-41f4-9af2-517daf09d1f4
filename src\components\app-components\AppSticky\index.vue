<template>
  <div>
    <div
      :style="[style]"
      class="sticky"
    >
      <slot />
    </div>
    <div
      v-if="!isScreenShow"
      :style="{
        height: rect.height + 'px'
      }"
    />
  </div>
</template>

<script>
import { getClientRect } from "@/libs/tools";
import { bindOnHook } from "@/libs/hooks";

export default {
  name: "AppSticky",
  data() {
    return {
      windowHeight: 0,
      scrollTop: 0,
      rect: {}
    };
  },
  computed: {
    style() {
      if (!this.isScreenShow) {
        return {
          position: "fixed",
          bottom: `${this.tabsHeight}px`
        };
      }

      return {};
    },
    /** 是否在视窗中完全展示 */
    isScreenShow() {
      return !(
        this.rect.top + this.rect.height - this.scrollTop >
        this.windowHeight - this.tabsHeight
      );
    },
    tabsHeight() {
      return this.$store.getters.getTabsHeight;
    }
  },
  mounted() {
    this.getScreenInfo();

    this.getWindowHeight();

    const fnCallback = ({ scrollTop }) => {
      this.scrollTop = scrollTop;
    };

    const parent = bindOnHook.call(this, "onPageScroll", fnCallback);

    this.$on("hook:destroyed", () => {
      parent.$off("hook:onPageScroll", fnCallback);
    });
  },
  methods: {
    getWindowHeight() {
      const systemInfo = uni.getSystemInfoSync();

      const { windowHeight } = systemInfo;

      this.windowHeight = windowHeight;
    },
    getScreenInfo() {
      setTimeout(() => {
        getClientRect.call(this, ".sticky").then(rect => {
          this.rect = rect;
        });
      }, 1000);
    }
  }
};
</script>
