<template>
  <div
    v-if="show"
    :style="[containerStyle]"
    class="fixed right-[12px]"
  >
    <div
      class="relative"
      @click="handleClick"
    >
      <img
        alt=""
        class="w-[351px] h-[108px] block"
        src="../../pages/submit-question/findlawyer/imgs/Frame1321315356.png"
      >
      <img
        alt=""
        class="w-[12px] h-[12px] absolute top-[44px] block right-[4px]"
        src="../../pages/submit-question/findlawyer/imgs/close.png"
        @click.stop="show = false"
      >
    </div>
  </div>
</template>

<script>
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import { toAskLawyer } from "@/libs/turnPages";

export default {
  name: "FindRetainCapitalIcon",
  data() {
    return {
      /** 显示缩略 */
      show: false
    };
  },
  computed: {
    containerStyle() {
      let style = {
        bottom: (this.$store.state.tabsHeight + 12) * 2 + "rpx"
      };

      return uni.$u.addStyle(style);
    }
  },
  mounted() {
    setTimeout(() => {
      buryPointChannelBasics({
        code: "LAW_APPLET_FIND_LAWYER_STAY_BOTTOM_POP_UP_PAGE",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      this.show = true;
    }, 3000);
  },
  methods: {
    handleClick() {
      buryPointChannelBasics({
        code:
          "LAW_APPLET_FIND_LAWYER_STAY_BOTTOM_POP_UP_PAGE_CONSULT_NOW_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      toAskLawyer();
    }
  }
};
</script>
