<template>
  <div
    :class="type"
    class="flex text-content"
  >
    <img
      v-if="type === 'left'"
      alt=""
      class="text-content-left"
      src="@/pages/ask-details/img/left-icon.png"
    >
    <img
      v-if="type === 'right'"
      alt=""
      class="text-content-right"
      src="@/pages/ask-details/img/right-icon.png"
    >
    <div class="text">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: "ImLayoutText",
  props: {
    type: {
      type: String,
      validator: (value) => ["left", "right"].includes(value),
    },
  },
};
</script>

<style lang="scss" scoped>
.text {
  padding: 8px;
  font-size: 12px;
  font-weight: 400;
  color: #333333;
  max-width: 263px;
}

.text-content {
  position: relative;

  &-left {
    position: absolute;
    top: -6px;
    left: 12px;
    width: 12px;
    height: 6px;
    opacity: 1;
  }

  &-right {
    position: absolute;
    top: -6px;
    right: 12px;
    width: 12px;
    height: 6px;
    opacity: 1;
  }

  &.left {
    .text {
      background: #f5f5f7;
      border-radius: 4px;
    }
  }

  &.right {
    justify-content: flex-end;

    .text {
      background: #E1EAFA;
      border-radius: 4px;
    }
  }
}
</style>
