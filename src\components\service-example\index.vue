<template>
  <div class="example-container">
    <img
      alt=""
      class="example-tip"
      mode="widthFix"
      src="@/components/service-example/img/example-tip.png"
    >
    <div
      v-for="(i, index) in list"
      :key="index"
      :class="{ all: index === fixedIndex }"
      class="example-item"
    >
      <div class="header">
        <div class="user-info flex flex-align-center">
          <img
            :src="i.rightPortrait"
            alt=""
            class="user-info-img"
          >
          {{ i.userName }}
        </div>
        <p class="title text-ellipsis">
          {{ i.title }}
        </p>
        <img
          :src="i.service"
          alt=""
          class="service"
          mode="widthFix"
        >
      </div>
      <div class="recording">
        <div
          v-for="(n, nIndex) in i.historyRecord"
          :key="'h' + nIndex"
          class="recording-item"
        >
          <div
            :class="n.layoutType"
            class="flex"
          >
            <img
              :src="n.layoutType === 'left' ? i.leftPortrait : i.rightPortrait"
              alt=""
              class="head-portrait"
            >
            <div class="content">
              <p
                v-if="n.text"
                class="content-text"
              >
                {{ n.text }}
              </p>
              <img
                v-else
                :src="n.image"
                alt=""
                class="content-img"
                mode="widthFix"
              >
            </div>
          </div>
        </div>
      </div>
      <div
        class="fixed-state flex flex-align-center flex-space-center"
        @click="changeState(index)"
      >
        <p class="fixed-state-text">
          {{ index === fixedIndex ? "收起" : "查看" }}
        </p>
        <img
          :class="[{ clickType: index === fixedIndex }, 'fixed-state-img']"
          alt=""
          mode="widthFix"
          src="@/components/service-example/img/<EMAIL>"
        >
      </div>
    </div>
  </div>
</template>

<script>
const rightPortrait = require("@/components/service-example/img/avator.png");
export default {
  name: "ServiceExample",
  data() {
    return {
      fixedIndex: -1,
      list: [
        {
          leftPortrait: require("@/components/service-example/img/head-portrait5.png"),
          rightPortrait: rightPortrait,
          service: require("@/components/service-example/img/<EMAIL>"),
          title: "对方污蔑我，我打了对方一巴掌怎么办？",
          userName: "用户李**",
          historyRecord: [
            {
              layoutType: "right",
              text: "对方污蔑我，我打了对方一巴掌怎么办？",
            },
            {
              layoutType: "left",
              text: "要看对方是否报警",
            },
            {
              layoutType: "right",
              text: "报警后如何处理？",
            },
            {
              layoutType: "left",
              text: "如果因为你的一巴掌造成对方轻伤，可能会被拘留，如轻伤痛，可能会被罚款",
            },
            {
              layoutType: "left",
              text: "至于对方污蔑您，可以收集证据和材料进行起诉",
            },
            {
              layoutType: "right",
              text: "我不会写起诉书怎么办？",
            },
            {
              layoutType: "right",
              text: "这个很简单的，稍等给您拨打电话沟通细节，我来帮您撰写起诉书",
            },
          ],
        },
        {
          leftPortrait: require("@/components/service-example/img/head-portrait3.png"),
          rightPortrait: rightPortrait,
          service: require("@/components/service-example/img/<EMAIL>"),
          title: "夫妻性格不合，已分居，我想离婚男方不同意怎么办？",
          userName: "用户赵**",
          historyRecord: [
            {
              layoutType: "right",
              text: "夫妻性格不合，已分居，我想离婚男方不同意怎么办？",
            },
            {
              layoutType: "left",
              text: "我现在给您打电话，您方便接听吗",
            },
            {
              layoutType: "right",
              text: "现在不太方便",
            },
            {
              layoutType: "left",
              image: require("@/components/service-example/img/phone-card.png"),
            },
            {
              layoutType: "right",
              text: "好的",
            },
          ],
        },
        {
          leftPortrait: require("@/components/service-example/img/head-portrait4.png"),
          rightPortrait: rightPortrait,
          service: require("@/components/service-example/img/<EMAIL>"),
          title: "可以帮我看下合同吗？",
          userName: "用户王**",
          historyRecord: [
            {
              layoutType: "right",
              text: "可以帮我看下合同吗？",
            },
            {
              layoutType: "left",
              text: "没问题，您将合同发过来",
            },
            {
              layoutType: "right",
              image: require("@/components/service-example/img/<EMAIL>"),
            },
            {
              layoutType: "left",
              text: "您的合同没有问题，为了避免违约风险，建议您在第三条第4点，这里再加一下违约说明，具体添加文字我发送给您。",
            },
            {
              layoutType: "left",
              image: require("@/components/service-example/img/<EMAIL>"),
            },
            {
              layoutType: "right",
              text: "好的，谢谢律师",
            },
          ],
        },
        {
          leftPortrait: require("@/components/service-example/img/head-portrait1.png"),
          rightPortrait: rightPortrait,
          service: require("@/components/service-example/img/<EMAIL>"),
          title: "婚后母亲委托理财的钱，怎么证明不纳入离婚财产呢？",
          userName: "用户邓**",
          historyRecord: [
            {
              layoutType: "right",
              text: "婚后母亲委托理财的钱，怎么证明不纳入离婚财产呢？",
            },
            {
              layoutType: "left",
              text: "资金来源，多大金额，和你收入相匹配么？",
            },
            {
              layoutType: "left",
              text: "有无委托你理财的相关证据么？",
            },
            {
              layoutType: "right",
              text: "钱都是从妈妈银行卡直接转给我的，25W",
            },
            {
              layoutType: "left",
              text: "你可以整理出来母亲给你的转账记录，只要属实可以不签委托理财协议",
            },
            {
              layoutType: "right",
              text: "协议如何写，怎么补？",
            },
            {
              layoutType: "left",
              text: "你先去银行把所有转账明细查询到打印出来，提供到相关材料信息，我可以帮您起草协议",
            },
            {
              layoutType: "right",
              text: "好的，谢谢律师",
            },
            {
              layoutType: "left",
              text: "好的，麻烦给个好评呢",
            },
          ],
        },
      ],
    };
  },
  methods: {
    changeState(index) {
      if (this.fixedIndex === index) {
        this.fixedIndex = -1;
      } else {
        this.fixedIndex = index;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.example-container {
  overflow: hidden;
}

.example-tip {
  display: block;
  width: 100%;
  border-radius: 8px;
}

.example-item {
  margin-top: 12px;
  background: #f5f5f7;
  padding: 12px 12px 20px;
  border-radius: 12px 12px 12px 12px;
  position: relative;
  overflow: hidden;
  height: 174px;
  box-sizing: border-box;

  &.all {
    height: auto;
    padding-bottom: 36px;
  }

  .header {
    .user-info {
      font-size: 12px;
      font-weight: 400;
      color: #333333;

      .user-info-img {
        width: 20px;
        height: 20px;
        padding-right: 8px;
      }
    }

    .title {
      padding-top: 8px;
      padding-bottom: 16px;
      font-size: 14px;
      font-weight: 500;
      color: #222222;
    }

    .service {
      width: 100%;
    }
  }

  .recording {
    .recording-item {
      .left,
      .right {
        padding-top: 16px;

        .head-portrait {
          width: 28px;
          height: 28px;
        }

        .content {
          overflow: hidden;
          max-width: 247px;

          .content-text {
            padding: 8px;
            font-size: 12px;
            font-weight: 400;
            color: #333333;
          }

          .content-img {
            max-width: 100%;
            display: block;
          }
        }
      }

      .left {
        .head-portrait {
          padding-right: 8px;
        }

        .content {
          border-radius: 0 6px 6px 6px;

          .content-text {
            background: #ffffff;
          }
        }
      }

      .right {
        flex-direction: row-reverse;

        .head-portrait {
          padding-left: 8px;
        }

        .content {
          border-radius: 6px 0 6px 6px;

          .content-text {
            background: #E1EAFA;
          }
        }
      }
    }
  }

  .fixed-state {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding-top: 24px;
    padding-bottom: 12px;
    background: linear-gradient(180deg, rgba(245, 245, 247, 0) 0%, #f5f5f7 80%);
    font-size: 14px;
    font-weight: 400;
    color: #3887F5;

    .fixed-state-text {
      padding-right: 4px;
    }

    .fixed-state-img {
      width: 12px;
      height: 12px;
      transform: rotate(180deg);
      transition: all 0.3s;
      transform-origin: center center;

      &.clickType {
        transform: rotate(0deg);
      }
    }
  }
}
</style>
