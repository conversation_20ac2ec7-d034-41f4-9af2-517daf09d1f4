<template>
  <div class="relative">
    <!--左边服务   -->
    <div
      v-if="showMoreServices && btnShowState"
      class="absolute left-[12px] bottom-0 pb-[8px]"
    >
      <!--  这个按钮在发送完免费条数或者已支付后不展示     -->
      <p
        class="flex items-center justify-center w-[72px] h-[25px] bg-[#FFFFFF] rounded-[14px] border-[1px] border-solid border-[#EEEEEE] text-[12px] text-[#333333]"
        @click="moreServices"
      >
        更多服务
      </p>
    </div>
    <!-- 右边服务   -->
    <div
      v-if="!isArrNull(list)"
      class="absolute right-[8px] bottom-[16px]"
    >
      <div
        class="w-[54px] py-[8px] bg-[#FFFFFF] [box-shadow:0px_2px_12px_0px_rgba(0,0,0,0.12)] rounded-[8px]"
      >
        <div
          v-for="i in list"
          :key="i.id"
          class="pb-[12px] last-of-type:pb-[0] flex flex-column items-center justify-center"
          @click="handleClick(i)"
        >
          <img
            :src="i.icon"
            alt=""
            class="w-[20px] h-[20px]"
          >
          <p class="text-[10px] pt-[2px] text-[#333333]">
            {{ i.text }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { isArrNull, isNull, isObjNull } from "@/libs/basics-tools.js";
import { buryPointChannelBasics } from "@/api/burypoint.js";
import {
  HJLS_BURYPOINT,
  hjlsGlobalTransformationPath
} from "@/enum/hjls-burypoint.js";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint.js";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath.js";

export default {
  name: "PrivateChatService",
  props: {
    /* 是否付费*/
    isOrderPay: {
      type: Boolean,
      default: false
    },
    /* 免费发送次数*/
    freeSendNum: {
      type: Number,
      default: 0
    },
    /* 是否打赏*/
    isPraise: {
      type: Boolean,
      default: false
    },
    /* 电话咨询服务*/
    phoneConsultingService: {
      type: Object,
      default: () => ({})
    },
    /* 图文咨询服务*/
    graphicConsultingService: {
      type: Object,
      default: () => ({})
    },
    /* 追问服务*/
    followUpService: {
      type: Object,
      default: () => ({})
    },
    /** 是否显示更多服务 */
    showMoreServices: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {};
  },
  computed: {
    /* 按钮显示逻辑 有免费发送次数并且未支付*/
    btnShowState() {
      return !this.isOrderPay;
    },
    list() {
      return [
        {
          icon: require("@/pages/lawyer-im/assets/<EMAIL>"),
          text: "追问",
          fn: "toASK",
          id: 1,
          /* 没有免费次数并且未支付展示*/
          show:
            this.freeSendNum < 1 &&
            !this.isOrderPay &&
            !isNull(this.followUpService) &&
            !isObjNull(this.followUpService),
          /* 埋点*/
          buryPoint:
            HJLS_BURYPOINT.HJ_APPLET_FREE_CHAT_IM_MORE_SERVICE_FLOAT_ICON_ASK_AGAIN_CLICK,
          /* 转化路径*/
          transformationPath:
            hjlsGlobalTransformationPath.HJ_APPLET_FREE_CHAT_IM_MORE_SERVICE_FLOAT_ICON_ASK_AGAIN_CLICK
        },
        {
          icon: require("@/pages/lawyer-im/assets/<EMAIL>"),
          text: "打赏",
          fn: "toPraise",
          id: 2,
          /* 没有免费发送次数展示并且没有打赏过*/
          show: (this.isOrderPay || this.freeSendNum < 1) && !this.isPraise,
          /* 埋点*/
          buryPoint:
            HJLS_BURYPOINT.HJ_APPLET_FREE_CHAT_IM_MORE_SERVICE_FLOAT_ICON_REWARD_CLICK,
          /* 转化路径*/
          transformationPath:
            hjlsGlobalTransformationPath.HJ_APPLET_FREE_CHAT_IM_MORE_SERVICE_FLOAT_ICON_REWARD_CLICK
        },
        {
          icon: require("@/pages/lawyer-im/assets/<EMAIL>"),
          text: "图文咨询",
          fn: "toTextConsult",
          id: 3,
          /* 未支付展示*/
          show:
            !this.isOrderPay &&
            !isNull(this.graphicConsultingService) &&
            !isObjNull(this.graphicConsultingService),
          /* 埋点*/
          buryPoint:
            HJLS_BURYPOINT.HJ_APPLET_FREE_CHAT_IM_MORE_SERVICE_FLOAT_ICON_TWZX_CONSULT_CLICK,
          /* 转化路径*/
          transformationPath:
            hjlsGlobalTransformationPath.HJ_APPLET_FREE_CHAT_IM_MORE_SERVICE_FLOAT_ICON_TWZX_CONSULT_CLICK
        },
        {
          icon: require("@/pages/lawyer-im/assets/<EMAIL>"),
          text: "电话咨询",
          fn: "toPhoneConsult",
          id: 4,
          /* 未支付展示*/
          show:
            !this.isOrderPay &&
            !isNull(this.phoneConsultingService) &&
            !isObjNull(this.phoneConsultingService),
          /* 埋点*/
          buryPoint:
            HJLS_BURYPOINT.HJ_APPLET_FREE_CHAT_IM_MORE_SERVICE_FLOAT_ICON_DHZX_CONSULT_CLICK,
          /* 转化路径*/
          transformationPath:
            hjlsGlobalTransformationPath.HJ_APPLET_FREE_CHAT_IM_MORE_SERVICE_FLOAT_ICON_DHZX_CONSULT_CLICK
        }
      ].filter(i => i.show);
    }
  },
  methods: {
    isArrNull,
    moreServices() {
      buryPointChannelBasics({
        code: HJLS_BURYPOINT.HJ_APPLET_FREE_CHAT_IM_MORE_SERVICE_CLICK,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });
      this.$emit("moreServices");
    },
    handleClick(data) {
      const { fn, buryPoint, transformationPath } = data;
      if (transformationPath) {
        buryPointTransformationPath.add(transformationPath);
      }
      if (buryPoint) {
        buryPointChannelBasics({
          code: buryPoint,
          type: 1,
          behavior: BURY_POINT_CHANNEL_TYPE.CK
        });
      }
      if (fn) this.$emit(fn, data);
    }
  }
};
</script>

<style lang="scss" scoped></style>
