const readline = require("readline");
const tma = require("tt-ide-cli");

/** 邮箱登陆 */
async function emailLogin(email, password) {
  await tma.loginByEmail({
    email,
    password,
  });
}

/** 手机号登陆 */
async function phoneLogin(phoneNumber, code) {
  await tma.loginByPhone({
    phoneNumber,
    code,
  });
}

// 获取控制台输入
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

function question(query) {
  return new Promise((resolve) => {
    rl.question(query, (answer) => {
      resolve(answer);
    });
  });
}

(async () => {
  const tips = `
请输入数字选择登陆方式：
1. 手机号登陆（默认）
2. 邮箱登陆
3. 退出
    `;
  const res = await question(tips);

  switch (res) {
  case "2":
    const email = await question("请输入邮箱：");
    const password = await question("请输入密码：");
    await emailLogin(email, password);
    break;
  case "3":
    await tma.logout();
    break;
  default:
    /** @type {string} */
    const phoneNumber = await question("请输入手机号：");
    await tma.sendVerificationCodeToPhone({ phoneNumber });
    const code = await question("请输入验证码：");
    await phoneLogin(phoneNumber, code);
    console.log("登陆成功");
    rl.close();
    break;
  }
})();
