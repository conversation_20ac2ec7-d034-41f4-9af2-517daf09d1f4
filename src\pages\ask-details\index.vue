<template>
  <login-layout>
    <div>
      <ask-header :details="details" />
      <detail-lawyer
        v-if="details.typeValue"
        :details="details"
      />
      <ask-details-banner />
      <u-safe-bottom />
    </div>
  </login-layout>
</template>

<script>
import AskHeader from "@/pages/ask-details/components/ask-header/index.vue";
import { qaMessageDetail } from "@/api/ask.js";
import { isArray, isArrNull } from "@/libs/basics-tools.js";
import { formatTime } from "@/libs/filter.js";
import {
  askMessageRole,
  askMessageType,
  SHARE_DICTIONARY
} from "@/libs/config.js";
import { buryPointChannelBasics } from "@/api/burypoint.js";
import { BURY_POINT_CHANNEL_TYPE, POINT_CODE } from "@/enum/burypoint.js";
import { setNavigationBarTitle, shareAppMessageNew } from "@/libs/tools.js";
import DetailLawyer from "@/pages/ask-details/components/detail-lawyer.vue";
import LoginLayout from "@/components/login/login-layout.vue";
import AskDetailsBanner from "@/pages/ask-details/components/ask-details-banner.vue";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import { toAskLawyer } from "@/libs/turnPages";
import { shareDataDictionary } from "@/api";
import seo from "@/libs/seo";

export default {
  name: "AskDetails",
  components: {
    USafeBottom,
    AskDetailsBanner,
    LoginLayout,
    DetailLawyer,
    AskHeader
  },
  data() {
    return {
      id: "",
      askMessageType,
      askMessageRole,
      details: {
        replies: []
      },
      checkStatusArr: [2, 3, 0]
    };
  },
  onLoad(params) {
    buryPointChannelBasics({
      code: "LAW_APPLET_QA_MESSAGE_DETAIL_PAGE",
      behavior: BURY_POINT_CHANNEL_TYPE.VI,
      type: 1
    });

    buryPointChannelBasics({
      code: "LAW_APPLET_RECENTLY_QA_LAWYER_ANSWER_DETAIL_PAGE",
      behavior: BURY_POINT_CHANNEL_TYPE.VI,
      type: 1
    });

    this.id = params.id;

    qaMessageDetail({
      qaMessageId: params.id
    }).then(({ data }) => {
      this.details = data;

      seo.setPageInfo({
        title: (this.details.detail || "").slice(0, 50) + " - 法临网",
        keywords: `${this.details.typeLabel}`,
        description: this.details?.replies?.[0]?.content || ""
      });
    });

    buryPointChannelBasics({
      code: POINT_CODE.LAW_APPLET_CONSULT_DETAIL_PAGE,
      behavior: BURY_POINT_CHANNEL_TYPE.VI,
      type: 1
    });
    setNavigationBarTitle({
      title: "精选问答",
      backgroundColor: "#EEF5FF"
    });
  },
  onHide() {
    clearTimeout(this.timer);
  },
  onReachBottom() {
    this.scrollToLower();
  },
  // 分享
  async onShareAppMessage() {
    const { remark: data } =
      (await shareDataDictionary()).find(
        item => item.value === SHARE_DICTIONARY.ASK_DETAILS
      ) || {};

    return shareAppMessageNew(data);
  },
  computed: {
    /* 详情状态*/
    detailStatus() {
      let obj = {};
      switch (Number(this.details.checkStatus)) {
      case 2:
        obj = {
          text: "当前问答审核失败 ! " + (this.details.closeReason || ""),
          name: "close-circle"
        };
        break;
      case 3:
        obj = {
          text: "当前问答已失效",
          name: "error-circle"
        };
        break;
      default:
        obj = {
          text: "当前问答正在审核中",
          name: "clock"
        };
        break;
      }
      return obj;
    }
  },
  methods: {
    isArrNull,
    isArray,
    formatTime,
    toDownload() {
      uni.navigateTo({
        url: "/pages/toutiao-download/index"
      });
    },
    scrollToLower() {
      if (!this.isShowPopup) this.showBottomPopup = true;
    },
    toAskLawyer() {
      toAskLawyer();
    }
  }
};
</script>

<style lang="scss" scoped>
.invalid-tip {
  color: #eb4738;
  padding: 7px 17px;
  background: #fcf1ed;

  &.invalid-sh0 {
    color: #f78c3e !important;
    background: #faf2e8 !important;
  }

  .text {
    padding-left: 5px;
  }
}

.popup-wr,
.popup-wr-btm {
  position: relative;

  .img-main {
    width: 100%;
  }

  .img-btn {
    position: absolute;
    width: 287px;
    height: 54px;
    top: 154px;
    left: 20px;
  }
}

.popup-wr-btm {
  font-size: 0;

  .img-main {
    height: 262px;
  }

  .img-btn {
    width: 311px;
    top: 164px;
    left: 32px;
  }
}
</style>
