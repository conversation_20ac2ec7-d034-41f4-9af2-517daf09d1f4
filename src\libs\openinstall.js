import { isObjNull } from "@/libs/basics-tools.js";
import { getDownloadPageAddress, is_weixn } from "@/libs/tool.js";
import { axiosBaseHeadersConfig } from "@/libs/config.js";

let openinstallExample = {};


export const getOpenInstallParams = () => {
  let data = {};
  try {
    data = JSON.parse(localStorage.getItem("loginPageParams") || JSON.stringify({}));
    // eslint-disable-next-line no-empty
  }catch (e){

  }
  return data;
};

export const initOpenInstall = () => {
  return new Promise((resolve, reject) => {
    if (!window["OpenInstall"] && is_weixn())reject();
    if(isObjNull(openinstallExample)){
      const data = getOpenInstallParams();
      // OpenInstall初始化时将与openinstall服务器交互，应尽可能早的调用
      /* web页面向app传递的json数据(json string/js Object)，应用被拉起或是首次安装时，通过相应的android/ios api可以获取此数据*/
      // eslint-disable-next-line no-undef
      new OpenInstall({
        /* appKey必选参数，平台为每个应用分配的ID*/
        appKey: process.env.VUE_APP_OPENINSTALL_KEY,
        /* 直接指定渠道编号，默认通过当前页url中的channelCode参数自动检测渠道编号*/
        channelCode: data.channelId,
        /* 自定义遮罩的html*/
        // mask:function(){
        //  return "<div id='_shadow' style='position:fixed;left:0;top:0;background:rgba(0,255,0,0.5);filter:alpha(opacity=50);width:100%;height:100%;z-index:10000;'></div>"
        // },
        /* 初始化完成的回调函数，可选*/
        onready: function() {
          /* 在app已安装的情况尝试拉起app*/
          // this.schemeWakeup({data:data,channelCode:"test-channelcode"});//延迟指定绑定参数与渠道编号

          /* 用户点击某个按钮时(假定按钮id为downloadButton)，安装app*/
          openinstallExample = this;
          resolve(openinstallExample);
        }
      }, data);
    }else{
      resolve(openinstallExample);
    }
  });
};

export const startOpenInstall = (data = {}) => {
  if(is_weixn()){
    window.location.href = getDownloadPageAddress({
      ...getOpenInstallParams(),
      osVersion: axiosBaseHeadersConfig.osVersion
    });
  }else{
    const openParams = getOpenInstallParams();
    const params = { data: { ...openParams, ...data }, channelCode: openParams.channelId || "" };
    initOpenInstall().then((that) => {
      that.wakeupOrInstall(params);
    });
  }
};
