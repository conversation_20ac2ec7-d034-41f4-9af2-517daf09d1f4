<template>
  <div class="ask-details-banner">
    <img
      v-if="bannerUrl.imageUrl"
      :src="bannerUrl.imageUrl"
      alt=""
      class="banner"
      @click="jumpToPage"
    >
    <div class="title">
      大家都在问
    </div>
    <div
      class="card-box position-relative"
      @click="clickTurnPage"
    >
      <img
        alt=""
        class="card-image background-image"
        src="@/pages/ask-details/img/<EMAIL>"
      >
      <p class="people">
        <span class="count">{{ count }}</span> 位律师在线为您解答
      </p>
      <div class="swiper-box">
        <phone-content-button-swiper />
      </div>
    </div>
  </div>
</template>

<script>
import { toAskLawyer } from "@/libs/turnPages";
import { advertListPosition } from "@/api/lawyer";
import buryPointTransformationPath from "@/libs/buryPointTransformationPath";
import { BURY_POINT_CHANNEL_TYPE, FROM_PAGE } from "@/enum/burypoint";
import { randomNumBoth } from "@/libs/tool";
import PhoneContentButtonSwiper from "@/components/PhoneContentButtonSwiper.vue";
import { buryPointChannelBasics } from "@/api/burypoint";

export default {
  name: "AskDetailsBanner",
  components: { PhoneContentButtonSwiper },
  data() {
    return {
      bannerUrl: {},
    };
  },
  computed: {
    count(){
      // 生成一个1500-3500的随机数
      return randomNumBoth(1500, 3500);
    }
  },
  mounted() {
    this.getBannerData();
  },
  methods: {
    /** 获取广告位数据 */
    getBannerData() {
      advertListPosition({ positionId: 168 }).then(({ data }) => {
        this.bannerUrl = data?.[0]?.advertContents?.[0] || {};
      });
    },
    jumpToPage() {
      buryPointChannelBasics({
        code: "LAW_APPLET_RECENTLY_QA_LAWYER_ANSWER_DETAIL_PAGE_BANNER_CLICK",
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
        type: 1,
      });

      uni.navigateTo({
        url: this.bannerUrl.addressUrl,
      });
    },
    /** 点击跳转 */
    clickTurnPage() {
      buryPointTransformationPath.addDataSources({
        fromPage: FROM_PAGE.WDXQ_DJDZW,
      });

      buryPointChannelBasics({
        code: "LAW_APPLET_RECENTLY_QA_LAWYER_ANSWER_DETAIL_PAGE_ONE_KEY_CONSULT_CLICK",
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
        type: 1,
      });

      // 跳转到问律师
      toAskLawyer();
    },
  },
};
</script>

<style lang="scss" scoped>
.ask-details-banner{
  padding-bottom: 20px;
}
.banner {
  display: block;
  width: 351px;
  height: 95px;
  margin: 12px auto 0;
  border-radius: 8px;
}

.title {
  margin-top: 12px;
  padding: 16px;
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}
.card-box{
  width: 351px;
  height: 234px;
  margin: 0 auto;
  padding: 83px 0 25px 12px;
  box-sizing: border-box;
  .people{
    font-size: 13px;
    padding-left: 88px;
    box-sizing: border-box;
    color: #999;
    margin-bottom: 82px;
    .count{
      color: #22BF7E;
      margin-right: 3px;
    }
  }
  .swiper-box{
    width: 327px;
  }
}
//.card-image {
//  display: block;
//  width: 351px;
//  height: 234px;
//}
</style>
