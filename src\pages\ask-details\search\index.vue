<template>
  <div>
    <search-input
      v-model="searchInput"
      :placeholder="placeholder"
      @search="goResult"
    />
    <div class="px-[12px] mt-[16px]">
      <div class="text-[16px] font-bold text-[#333333]">
        大家都在搜
      </div>
      <div class="mt-[16px]">
        <search-topic
          :groupCode="groupCode"
          @goResult="goResult"
        />
      </div>
    </div>
    <img
      v-if="bannerUrl.imageUrl"
      :src="bannerUrl.imageUrl"
      alt=""
      class="w-[351px] mt-[12px] mx-[12px]"
      mode="widthFix"
      @click="jumpToPage(bannerUrl.addressUrl)"
    >
  </div>
</template>
<script>
import SearchTopic from "@/pages/ask-details/search/components/SearchTopic.vue";
import SearchInput from "@/components/SearchInput.vue";
import searchData from "@/pages/ask-details/search/searchData";
import { toSearchResultPage } from "@/libs/turnPages";
import { advertListPosition } from "@/api/lawyer";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";
import { SEARCH_PAGE_TYPE } from "@/libs/config";

export default {
  name: "Search",
  components: {
    SearchInput,
    SearchTopic
  },
  data() {
    return {
      searchInput: "",
      /** 广告位数据 */
      bannerUrl: {},
      placeholder: searchData.searchText,
      type: SEARCH_PAGE_TYPE.COMMON
    };
  },
  onLoad(params) {
    this.type = params.type || SEARCH_PAGE_TYPE.COMMON;

    this.getBannerData();
  },
  onShow() {
    this.placeholder = searchData.searchText;
  },
  computed: {
    isContractSearch() {
      return this.type === SEARCH_PAGE_TYPE.CONTRACT;
    },
    /** 是否是专场 */
    isSpecialSearch() {
      return this.type === SEARCH_PAGE_TYPE.SPECIAL;
    },
    groupCode() {
      if(this.isSpecialSearch) return "HT_SS_DJSS";

      return this.isContractSearch ? "HT_SS_DJSS" : "C_SS_SSY_DJSS";
    }
  },
  methods: {
    /** 点击跳转到结果页 */
    goResult(value) {
      // 如果有值，就存入搜索数据
      // 如果没有值，下个页面就会使用缓存中的数据
      if (value) searchData.searchText = value;

      toSearchResultPage({
        type: this.type
      });
    },
    /** 获取广告位数据 */
    getBannerData() {
      const positionId = this.isContractSearch ? 182 : 178;

      advertListPosition({ positionId }).then(({ data }) => {
        this.bannerUrl = data?.[0]?.advertContents?.[0] || {};
      });
    },
    /** 点击跳转页面 */
    jumpToPage(addressUrl) {
      buryPointChannelBasics({
        code: "LAW_APPLET_SEARCH_PAGE_CARD_CLICK",
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      uni.navigateTo({
        url: addressUrl
      });
    }
  }
};
</script>
<style>
page {
  background-color: #fff;
}
</style>
