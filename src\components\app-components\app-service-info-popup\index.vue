<template>
  <app-popup
    :show="dialogShow"
    :zIndex="9990"
    mode="center"
  >
    <div class="info-dialog-modal">
      <div class="info-dialog-content">
        <div class="info-dialog-title">
          {{ serverInfo.title }}
        </div>
        <scroll-view
          class="info-dialog-nr"
          scrollY="true"
        >
          <div style="white-space: pre-line; line-height: 20px">
            {{ serverInfo.content }}
          </div>
        </scroll-view>
      </div>
      <div
        class="info-dialog-btn"
        @click="dialogShow = false"
      >
        我知道了
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";

export default {
  name: "AppServiceInfoPopup",
  components: { AppPopup },
  props: {
    serverInfo: {
      type: Object,
      default: () => ({})
    },
    value: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    dialogShow: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.info-dialog-modal {
  width: 311px;
  background: #fff;
  border-radius: 16px;

  .info-dialog-content {
    padding: 24px;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 16px;

    .info-dialog-title {
      font-size: 18px;
      font-weight: bold;
      color: #333333;
      margin-bottom: 12px;
      text-align: center;
    }

    .info-dialog-nr {
      height: 300px;
      font-size: 14px;
      color: #666;
    }
  }

  .info-dialog-btn {
    height: 46px;
    line-height: 46px;
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    color: #3887f5;
    border-top: 1px solid #eeeeee;
  }
}
</style>
