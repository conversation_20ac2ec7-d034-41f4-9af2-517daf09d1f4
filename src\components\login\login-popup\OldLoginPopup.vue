<template>
  <div>
    <app-popup
      :closeOnClickOverlay="false"
      :duration="0"
      :round="0"
      :safeAreaInsetBottom="false"
      :show="show"
      :zIndex="zIndex"
      bgColor="transparent"
      mode="bottom"
      @open="open"
    >
      <div class="flex items-center pr-[16px] justify-end mb-[16px]">
        <img
          alt=""
          class="w-[24px] h-[24px] block"
          src="../../../pages/submit-question/imgs/apK8Dj.png"
          @click="loginPopupClose"
        >
      </div>
      <if t="hjls">
        <img
          alt=""
          class="w-[375px] h-[146px] block"
          src="../imgs/header.png"
        >
      </if>
      <if f="hjls">
        <login-header :index="index" />
      </if>
      <div
        class="w-[375px] pb-[24px] bg-[#FFFFFF] position-relative box-border"
      >
        <div class="text-wr">
          <login-swiper />
          <if t="hjls">
            <img
              alt=""
              class="w-[48px] h-[48px] block mx-auto mt-[30px]"
              src="../imgs/<EMAIL>"
            >
          </if>
          <if f="hjls">
            <img
              alt=""
              class="w-[48px] h-[48px] block mx-auto mt-[30px]"
              src="../imgs/logo.png"
            >
          </if>
          <div class="text-center">
            <div class="font-bold text-[18px] text-[#333333] mt-[12px]">
              欢迎登录
            </div>
            <if t="hjls">
              <div class="text-[12px] text-[#999999] mt-[4px]">
                未注册手机号验证后自动创建好佳律师账号
              </div>
            </if>
            <if f="hjls">
              <div class="text-[12px] text-[#999999] mt-[4px]">
                未注册手机号验证后自动创建法临账号
              </div>
            </if>
          </div>
        </div>
        <div>
          <div
            class="text-[13px] text-[#999999] flex justify-center items-center mt-[40px]"
          >
            <if t="hjls">
              <img
                v-if="check"
                alt=""
                class="w-[16px] h-[16px] block"
                src="../imgs/<EMAIL>"
                @click="check = false"
              >
              <img
                v-else
                alt=""
                class="w-[16px] h-[16px] block"
                src="../imgs/check.png"
                @click="check = true"
              >
            </if>
            <if f="hjls">
              <img
                v-if="check"
                alt=""
                class="w-[16px] h-[16px] block"
                src="../imgs/check-active.png"
                @click="check = false"
              >
              <img
                v-else
                alt=""
                class="w-[16px] h-[16px] block"
                src="../imgs/check.png"
                @click="check = true"
              >
            </if>
            <span class="ml-[4px]">
              阅读并同意
            </span>
            <span
              class="text-[#F78C3E]"
              @click="protocolClick(1)"
            >
              《隐私政策》
            </span>
            <span
              class="text-[#F78C3E]"
              @click="protocolClick(2)"
            >
              《用户服务协议》
            </span>
          </div>
          <button
            class="w-[311px] h-[44px] mt-[16px] bg-[#082846] rounded-[8px] text-[16px] font-bold text-[#FFFFFF] flex items-center justify-center box-border mx-auto"
            open-type="getPhoneNumber"
            @click="btnHandleClick(false, true)"
            @getphonenumber="getPhoneNumberHandler"
          >
            一键登录
          </button>
        </div>
        <u-safe-bottom />
      </div>
    </app-popup>
  </div>
</template>
<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import LoginHeader from "@/components/login/components/LoginHeader.vue";
import LoginSwiper from "@/components/login/LoginSwiper.vue";
import LoginPopupMixin from "@/components/login/mixins/LoginPopupMixin";
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";

export default {
  name: "OldLoginPopup",
  components: { USafeBottom, AppPopup, LoginHeader, LoginSwiper },
  mixins: [LoginPopupMixin]
};
</script>
