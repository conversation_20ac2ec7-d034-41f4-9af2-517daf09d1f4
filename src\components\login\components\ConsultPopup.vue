<template>
  <div>
    <!-- 返回拦截弹窗 -->
    <app-popup
      :closeOnClickOverlay="false"
      :safeAreaInsetBottom="true"
      :show="show"
      :zIndex="9990"
      mode="center"
      showCancelButton
      @cancel="close"
    >
      <div class="bg-[#FFFFFF] rounded-[16px] p-[24px_24px_28px_24px]">
        <div class="font-bold text-[20px] text-[#333333] text-center">
          咨询接入提醒
        </div>
        <div class="text-[14px] text-[#666666] mt-[12px]  leading-[24px]">
          当前已有多位律师等待接入，您当前咨询队列靠前，现在咨询最快10秒得到回复。
        </div>
        <img
          alt=""
          class="w-[263px] h-[82px] block mt-[22px]"
          src="@/components/login/imgs/<EMAIL>"
        >
        <div
          class="w-[247px] h-[48px] bg-[#FF8A36] rounded-[52px] font-bold text-[17px] text-[#FFFFFF] flex items-center justify-center box-border mt-[22px] mx-auto"
          @click="jump"
        >
          立即接入
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "@/components/app-components/app-popup/index.vue";
import { toAskLawyer } from "@/libs/turnPages";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";

export default {
  name: "ConsultPopup",
  components: { AppPopup },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    /** 弹窗显示 */
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
  watch: {
    show() {
      if (this.show) {
        buryPointChannelBasics({
          code: "LAW_APPLET_INDEX_PAGE_V2_POP_UP_REGISTER_NO_LEFT_DATA_PAGE",
          behavior: BURY_POINT_CHANNEL_TYPE.VI,
          type: 1
        });
      }
    }
  },
  methods: {
    close() {
      this.show = false;
    },
    jump() {
      toAskLawyer();
      buryPointChannelBasics({
        code: "LAW_APPLET_INDEX_PAGE_V2_POP_UP_REGISTER_NO_LEFT_DATA_CLICK",
        behavior: BURY_POINT_CHANNEL_TYPE.VI,
        type: 1
      });
      this.show = false;
    }
  }
};
</script>
