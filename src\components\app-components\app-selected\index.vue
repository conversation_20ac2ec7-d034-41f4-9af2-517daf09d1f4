<template>
  <div>
    <div class="selected">
      <div
        v-for="(item, index) in list"
        :key="item.value"
        :class="[
          'item',
          {
            'item--selected': index === activeIndex,
          },
        ]"
        @click="handleClick(item, index)"
      >
        {{ item.name }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AppSelected",
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      activeIndex: 0,
    };
  },
  methods: {
    handleChange(item, index) {
      this.$emit("change", {
        ...item,
        index,
      });
    },
    handleClick(item, index) {
      // 如果index相同，不触发change事件
      if (this.activeIndex !== index) {
        this.handleChange(item, index);
      }

      this.activeIndex = index;

      this.$emit("click", {
        ...item,
        index,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.selected {
  display: flex;
  padding: 12px 16px;
  overflow-x: auto;
}

.item {
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 400;
  color: #666666;
  border-radius: 17px;
  background: #f5f5f7;
  flex-shrink: 0;
  margin-right: 8px;

  &--selected {
    background: #3887f5;
    color: #ffffff;
  }
}
</style>
