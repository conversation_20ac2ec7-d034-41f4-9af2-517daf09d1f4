import { getUserTokenStorage } from "@/libs/token";
import { axiosBaseHeadersConfig } from "@/libs/config";
export const fileUpload = ({ url = "/core/upload/image", fileName = "file", file, data = {} }) => {
  if(!file.url) file.url = file.path;
  // 上传api及接口调用
  console.log("uploadFilePromise===========:", file);
  let uploadObj = {}; // uploadFile 参数
  let token = getUserTokenStorage();
  uploadObj = {
    filePath: file.url,
    name: fileName
  };
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: url,
      Accept: "multipart/form-data",
      header: {
        "Content-Type": "multipart/form-data",
        token: token,
        osversion: axiosBaseHeadersConfig.osVersion
      },
      ...uploadObj,
      // #ifdef MP-WEIXIN
      formData: data,
      // #endif
      // #ifdef MP-TOUTIAO || MP-BAIDU
      formData: {
        file: file,
        ...data
      },
      // #endif
      success: res => {
        // #ifdef MP-TOUTIAO || MP-WEIXIN || MP-ALIPAY
        const data =JSON.parse(res.data);
        // #endif
        // #ifdef MP-BAIDU
        const data = res.data;
        // #endif
        if(data.code===0){
          setTimeout(()=>{
            resolve(data);
          },1000)
        }else{
          reject(data);
          uni.showToast({
            title: data.message,
            icon: "none"
          })
        }
      },
      fail: error => {
        console.log("error:", error);
        reject(error);
      }
    });
  });
};
