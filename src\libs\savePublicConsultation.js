// 保存公共咨询
import { caseSourceV2Save, currencyGetAddress } from "@/api";
import { isNull } from "@/libs/basics-tools";
import { caseSourceV2GetCaseOrZx } from "@/api/special";

export function savePublicConsultation(data) {
  return caseSourceV2GetCaseOrZx().then(res => {
    /** 0 无 1 案源 2 问答 */
    const type = Number(res.data?.type || 0);

    if (type !== 0) {
      return Promise.resolve(res);
    } else {
      return caseSourceV2Save(data);
    }
  });
}

// 保存公共咨询 传入了城市 就走传入的  没有就走接口请求的
export function savePublicConsultationByCity(res) {
  const params = res || {};
  if (isNull(res.happenAddress)) {
    return currencyGetAddress().then(({ data }) => {
      params.happenAddress = data.provinceName + data.cityName;
      params.regionCode = data.cityCode;
      return savePublicConsultation(params);
    });
  }
  return savePublicConsultation(params);
}
