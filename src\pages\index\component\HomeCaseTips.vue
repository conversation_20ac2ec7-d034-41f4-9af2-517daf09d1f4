<template>
  <div>
    <app-login-show>
      <div v-if="popupType">
        <div
          v-if="showType === 1"
          class="check !py-[20px] flex flex-space-between flex-align-center relative"
        >
          <div class="flex flex-align-center">
            <div class="check-header">
              <div class="check-header-unread" />
              <img
                :src="randomAvatar"
                alt=""
                class="check-header-img"
              >
            </div>
            <p class="check-text">
              {{ contentText }}
            </p>
          </div>
          <div>
            <img
              alt=""
              class="w-[76px] h-[26px] block flex-shrink-0"
              src="@/components/login/imgs/1.png"
              @click="handleClick"
            >
            <div class="text-[10px] text-center text-[#999999]">
              未服务自动退
            </div>
          </div>
          <div
            class="px-[8px] py-[2px] text-[12px] text-[#EB4738] flex items-center justify-center bg-[#FCF1ED] rounded-tl-none rounded-br-none rounded-tr-none rounded-bl-[8px] absolute top-0 right-0"
          >
            限时{{ hours }}:{{ minutes }}:{{ seconds }}
          </div>
        </div>
        <div
          v-else-if="showType === 2"
          class="check flex flex-space-between flex-align-center"
        >
          <div>
            <p class="check-text">
              {{ contentText }}
            </p>
            <div class="flex items-center mt-[8px]">
              <find-lawyer-card
                :containerSize="50"
                :size="20"
              />
              <div class="ml-[8px] text-[13px] text-[#999999]">
                排队中...
              </div>
            </div>
          </div>
          <img
            alt=""
            class="w-[88px] h-[32px] flex-shrink-0"
            src="@/components/login/imgs/2.png"
            @click="handleClick"
          >
        </div>
        <div
          v-else
          class="check flex flex-space-between flex-align-center"
        >
          <div class="flex flex-align-center">
            <div class="check-header">
              <div class="check-header-unread" />
              <img
                :src="randomAvatar"
                alt=""
                class="check-header-img"
              >
            </div>
            <p class="check-text">
              {{ contentText }}
            </p>
          </div>
          <img
            alt=""
            class="check-button flex-shrink-0"
            src="@/pages/index/imgs/red-look-btn.png"
            @click="handleClick"
          >
        </div>
      </div>
    </app-login-show>
  </div>
</template>

<script>
import AppLoginShow from "@/components/app-components/app-login-show/index.vue";
import { bindOnHook } from "@/libs/hooks.js";
import navToType from "@/pages/index/mixins/navToType.js";
import FindLawyerCard from "@/components/findlawyer/findlawyer-card/index.vue";
import countDownMixin from "@/pages/rapid-consultation-confirm-order/my-consultation/js/countDownMixin";

export default {
  name: "HomeCaseTips",
  components: { FindLawyerCard, AppLoginShow },
  mixins: [navToType, countDownMixin],
  data() {
    return {
      /** 问律师结果页信息 */
      resultData: {},
      loginServerData: {
        popupType: 0,
      },
      useHourFormat: true
    };
  },
  computed: {
    popupType() {
      return this.loginServerData.popupType;
    },
    showType() {
      switch (this.popupType) {
      case 2:
        if (this.notRushAnswerConsumeStatus) return 2;
        return 0;
      case 3:
        return 2;
      case 6:
        return 2;
      case 4:
        return 1;
      default:
        return 0;
      }
    },
    /** 显示文案 */
    contentText() {
      // 如果没有律师回复
      // if(this.isNotReply){
      //   return `您发布的的问题"${this.showCheckText}"已被律师关注，点击去查看`
      // }
      //
      // return `您发布的的问题"${this.showCheckText}"已被律师回复，点击去查看`
      if (!this.popupType) return null;

      const workCity = `${this.extInfo.workCity || ""}地区`;
      const lawyerName = `${this.extInfo.lawyerName || ""}律师`;

      switch (this.popupType) {
      case 2:
        if (this.notRushAnswerConsumeStatus) {
          return "您的法律问题正在排队接入律师，点击支付立即接入律师，不用等待。";
        }
        return `您的法律问题已被【${workCity}】的【${lawyerName}】回复，点击立即沟通。`;
      case 3:
        return "您的法律诉求平台正在加速曝光中，点击支付立即接入律师。";
      case 4:
        return `您有一笔【${this.extInfo.serviceName}】待支付订单，已为您保留优惠，立即支付`;
      case 5:
        return "您的付费咨询订单正在接入本地律师，查看进度";
      case 6:
        return "您的法律诉求平台正在加速曝光中，点击支付立即接入律师。";
      case 7:
        return "您的付费咨询已有律师接单，点击沟通";
      case 8:
        return `您的法律诉求已被【${workCity}】的【${lawyerName}】回复，点击立即沟通。`;
      default:
        return `您的法律诉求已被【${workCity}】的【${lawyerName}】回复，点击立即沟通。`;
      }
    },
    /** 随机取一个头像 */
    randomAvatar() {
      const avatarList = [
        require("@/pages/submit-question/imgs/header/1.png"),
        require("@/pages/submit-question/imgs/header/2.png"),
        require("@/pages/submit-question/imgs/header/3.png"),
        require("@/pages/submit-question/imgs/header/4.png"),
        require("@/pages/submit-question/imgs/header/5.png"),
        require("@/pages/submit-question/imgs/header/6.png"),
        require("@/pages/submit-question/imgs/header/7.png"),
        require("@/pages/submit-question/imgs/header/8.png"),
      ];
      const icon1 = require("@/pages/submit-question/imgs/<EMAIL>");
      const icon2 = require("@/pages/submit-question/imgs/<EMAIL>");
      // 我的提问 && 如果没有律师回复
      if (
        this.loginServerData.popupType === 2 &&
        this.notRushAnswerConsumeStatus
      ) {
        return icon1;
      }
      // UI设计图的特殊Icon
      if (
        this.loginServerData.popupType === 3 ||
        this.loginServerData.popupType === 6
      ) {
        return icon2;
      }
      // 随机取一个头像
      return avatarList[Math.floor(Math.random() * avatarList.length)];
    },
    /** 接单律师头像集合 */
    lawyerList() {
      return this.resultData?.urls || [];
    },
    /** 是否没有律师回复 */
    isNotReply() {
      return this.lawyerList.length === 0;
    },
  },
  watch: {
    "extInfo.remainPayTime": {
      handler(val) {
        if (val > 0) {
          this.countDown = val;
        }
      },
      deep: true
    }
  },
  mounted() {
    bindOnHook.call(this, "onShow", () => {
      this.getQuestionResult();
    });
    this.getQuestionResult();
  },
  methods: {
    handleClick() {
      // 设置时间戳
      const timestamp = Date.now();
      uni.setStorageSync("curTipTimestamp", {
        popupType: this.loginServerData.popupType,
        timestamp,
      });

      this.btnHandleClick();
    },
    /** 请求对应数据 */
    getQuestionResult() {
      // https://lanhuapp.com/web/#/item/project/product?tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&pid=00e1b6c6-693e-4371-afb2-3c495aa940e8&versionId=5870eb0f-925f-4320-9b6b-14db2af54dd9&docId=a8e6a1d7-224b-4f9c-85f4-8c6680988cc5&docType=axure&pageId=ad8240c6aeef4e888a808f8efb13963a&image_id=a8e6a1d7-224b-4f9c-85f4-8c6680988cc5&parentId=1e274b48-14ff-4f1c-85c2-c3af85cfc4b0
      // 去掉以前的判断，现在后端有返回值就会显示
      this.getLoginData();
    },
  },
};
</script>

<style lang="scss" scoped>
.check {
  margin: 12px auto 0 auto;
  width: 351px;
  background: #ffffff;
  border-radius: 8px;
  opacity: 1;
  box-sizing: border-box;
  padding: 12px;
}

.check-header {
  flex-shrink: 0;
  width: 34px;
  height: 34px;
  opacity: 1;
  position: relative;

  &-unread {
    position: absolute;
    top: 0;
    right: 2px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #eb4738;
    opacity: 1;
    border: 1px solid #ffffff;
    box-sizing: border-box;
  }

  &-img {
    width: 100%;
    height: 100%;
    border-radius: 17px;
  }
}

.check-text {
  font-size: 14px;
  font-weight: 400;
  box-sizing: border-box;
  color: #333333;
  margin-left: 8px;
}

.check-button {
  margin-left: 8px;
  flex-shrink: 0;
  width: 63px;
  height: 26px;
}
</style>
