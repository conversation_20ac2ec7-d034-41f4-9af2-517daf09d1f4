.ask__header {
  .header {
    padding: 20px 16px 32px 16px;
    overflow: hidden;
    background: #EBF3FF;

    .content {

      &__icon {
        width: 22px;
        height: 24px;
        opacity: 1;
        margin-right: 8px;
      }

      &__label {
        font-size: 22px;
        font-weight: bold;
        color: #333333;
        .tag{
          width: 22px;
          height: 24px;
          vertical-align: sub;
          margin-right: 8px;
        }
      }

      &__detail {
        margin-top: 24px;
        font-size: 16px;
        font-weight: 400;
        color: #333333;
        line-height: 19px;
      }
    }

    .info {
      margin-top: 24px;

      &__head {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: block;
      }

      &__name {
        padding-left: 8px;
        font-size: 12px;
        font-weight: 400;
        color: #8791A3;
      }

      &__date {
        font-size: 12px;
        font-weight: 400;
        color: #8791A3;
      }
    }
  }
}

.answer {
  margin-top: -16px;
  background-color: #fff;
  border-radius: 16px;

  &__item {
    padding: 16px;

    &:not(:last-child) {
      border-bottom: 0.5px solid #EEEEEE;
    }
  }

  .lawyer__info {
    &__header {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 12px;
      display: block;
    }

    &__name {
      font-size: 16px;
      font-weight: bold;
      color: #333333;
    }

    &__city {
      margin-top: 4px;
      font-size: 12px;
      font-weight: 400;
      color: #999999;
    }
  }

  &__content {
    margin-top: 16px;
    font-size: 14px;
    padding: 12px;
    background: #F5F5F7;
    border-radius: 4px;

    &__label {
      flex-shrink: 0;
      color: #3887F5;
    }

    &__text {
      word-break: break-all;
      color: #333333;
    }
  }

  &__bottom {
    margin: 12px 0 -4px 0;

    &__time {
      font-size: 12px;
      font-weight: 400;
      color: #999999;
    }

    &__icon {
      margin-right: 20px;

      &__img {
        width: 16px;
        height: 16px;
        display: block;
      }

      &__text {
        margin-left: 4px;
        font-size: 12px;
        font-weight: 400;
        color: #F9613C;
      }
    }

    &__button {
      padding: 5px 12px;
      background: #3887F5;
      border-radius: 40px;
      opacity: 1;
      font-size: 12px;
      font-weight: 400;
      color: #FFFFFF;
    }
  }
}

.answer__icon {
  position: absolute;
  top: 0;
  left: 16px;
  width: 22px;
  height: 24px;
  display: block;
}

.parting {
  color: #DDDDDD;
  margin: 0 4px;
}
