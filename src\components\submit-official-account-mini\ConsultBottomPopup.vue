<template>
  <div
    v-if="!isLogin"
    :style="[containerStyle]"
    class="z-[9990]"
  >
    <div
      v-if="showConsult"
      class="w-[375px] h-[42px] bg-[rgba(0,0,0,0.6)] flex items-center justify-between box-border pl-[8px] pr-[16px]"
    >
      <div class="flex items-center">
        <img
          alt=""
          class="w-[48px] h-[20px] block"
          src="@/assets/imgs/common/<EMAIL>"
        >
        <p class="text-[14px] text-[#FFFFFF] ml-[4px]">
          登录后免费在线咨询
        </p>
      </div>
      <div
        class="w-[67px] h-[26px] bg-[linear-gradient(_297deg,_#2F81F5_0%,_#87C5FF_100%)] rounded-[68px] text-[13px] text-[#FFFFFF] flex items-center justify-center"
        @click="consult"
      >
        去咨询
      </div>
    </div>
    <u-safe-bottom v-if="showSafe" />
  </div>
</template>

<script>
import USafeBottom from "@/uview-ui/components/u-safe-bottom/u-safe-bottom.vue";
import timeConfig from "@/libs/timeConfig";
import { toConfirmOrderPlus, whetherToLogIn } from "@/libs/tools";
import { sceneType } from "@/libs/config";
import { buryPointChannelBasics } from "@/api/burypoint";
import { BURY_POINT_CHANNEL_TYPE } from "@/enum/burypoint";

export default {
  name: "ConsultBottomPopup",
  components: { USafeBottom },
  props: {
    position: {
      type: String,
      default: "bottom",
      validator(value) {
        return ["none", "bottom", "top"].includes(value);
      }
    },
    showSafe: {
      type: Boolean,
      default: false
    },
    uvCode: {
      type: String,
      default: ""
    },
    clickCode: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      /** 去咨询 */
      showConsult: false
    };
  },
  computed: {
    isLogin() {
      return !!this.$store.getters["user/getToken"];
    },

    containerStyle() {
      let style = {};

      switch (this.position) {
      case "bottom":
        style = {
          position: "fixed",
          left: 0,
          right: 0,
          bottom: this.$store.getters.getPageBottom
        };
        break;

      case "top":
        style = {
          position: "fixed",
          left: 0,
          right: 0,
          top: 0
        };
        break;

      default:
        style = {};
      }

      return uni.$u.addStyle(style);
    }
  },
  mounted() {
    buryPointChannelBasics({
      code: this.uvCode,
      type: 1,
      behavior: BURY_POINT_CHANNEL_TYPE.CK
    });

    this.showConsultPopup();
  },
  methods: {
    /**
     * 未登录状态下：首页新增停留X秒弹出浮窗设置 ，X秒后台参数配置
     * https://lanhuapp.com/web/#/item/project/product?pid=2388751e-e4eb-4bd3-aaac-cf4ba47ae8a3&teamId=43efda02-ce73-4682-acd1-afc75cbf6b0c&tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=ab564c38-df93-437f-8015-8d8bd337cd92&docId=2189784a-d27f-4592-8a67-8d14b3cba87e&docType=axure&pageId=8edc7e679fbf4fd9b1c04866512fb9ff&image_id=2189784a-d27f-4592-8a67-8d14b3cba87e&parentId=ebe61574-270f-4d7d-84eb-48a8dda743e5
     */
    showConsultPopup() {
      if (this.isLogin) return;

      timeConfig.sydb().then(time => {
        setTimeout(() => {
          this.showConsult = true;
        }, time);
      });
    },
    /** 点击咨询 */
    consult() {
      buryPointChannelBasics({
        code: this.clickCode,
        type: 1,
        behavior: BURY_POINT_CHANNEL_TYPE.CK
      });

      whetherToLogIn(() => {
        toConfirmOrderPlus(sceneType.sydb);
      });
    }
  }
};
</script>
